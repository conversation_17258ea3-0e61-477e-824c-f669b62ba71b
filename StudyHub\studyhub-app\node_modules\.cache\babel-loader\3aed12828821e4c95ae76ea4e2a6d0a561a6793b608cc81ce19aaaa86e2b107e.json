{"ast": null, "code": "'use client';\n\nimport _defineProperty from \"C:/Users/<USER>/OneDrive/\\u30C9\\u30AD\\u30E5\\u30E1\\u30F3\\u30C8/Projects/StudyHub/studyhub-app/node_modules/@babel/runtime/helpers/esm/defineProperty.js\";\nimport useLazyRef from \"../useLazyRef/useLazyRef.js\";\nimport useOnMount from \"../useOnMount/useOnMount.js\";\nexport class Timeout {\n  constructor() {\n    _defineProperty(this, \"currentId\", null);\n    _defineProperty(this, \"clear\", () => {\n      if (this.currentId !== null) {\n        clearTimeout(this.currentId);\n        this.currentId = null;\n      }\n    });\n    _defineProperty(this, \"disposeEffect\", () => {\n      return this.clear;\n    });\n  }\n  static create() {\n    return new Timeout();\n  }\n  /**\n   * Executes `fn` after `delay`, clearing any previously scheduled call.\n   */\n  start(delay, fn) {\n    this.clear();\n    this.currentId = setTimeout(() => {\n      this.currentId = null;\n      fn();\n    }, delay);\n  }\n}\nexport default function useTimeout() {\n  const timeout = useLazyRef(Timeout.create).current;\n  useOnMount(timeout.disposeEffect);\n  return timeout;\n}", "map": {"version": 3, "names": ["_defineProperty", "useLazyRef", "useOnMount", "Timeout", "constructor", "currentId", "clearTimeout", "clear", "create", "start", "delay", "fn", "setTimeout", "useTimeout", "timeout", "current", "disposeEffect"], "sources": ["C:/Users/<USER>/OneDrive/ドキュメント/Projects/StudyHub/studyhub-app/node_modules/@mui/utils/esm/useTimeout/useTimeout.js"], "sourcesContent": ["'use client';\n\nimport useLazyRef from \"../useLazyRef/useLazyRef.js\";\nimport useOnMount from \"../useOnMount/useOnMount.js\";\nexport class Timeout {\n  static create() {\n    return new Timeout();\n  }\n  currentId = null;\n\n  /**\n   * Executes `fn` after `delay`, clearing any previously scheduled call.\n   */\n  start(delay, fn) {\n    this.clear();\n    this.currentId = setTimeout(() => {\n      this.currentId = null;\n      fn();\n    }, delay);\n  }\n  clear = () => {\n    if (this.currentId !== null) {\n      clearTimeout(this.currentId);\n      this.currentId = null;\n    }\n  };\n  disposeEffect = () => {\n    return this.clear;\n  };\n}\nexport default function useTimeout() {\n  const timeout = useLazyRef(Timeout.create).current;\n  useOnMount(timeout.disposeEffect);\n  return timeout;\n}"], "mappings": "AAAA,YAAY;;AAAC,OAAAA,eAAA;AAEb,OAAOC,UAAU,MAAM,6BAA6B;AACpD,OAAOC,UAAU,MAAM,6BAA6B;AACpD,OAAO,MAAMC,OAAO,CAAC;EAAAC,YAAA;IAAAJ,eAAA,oBAIP,IAAI;IAAAA,eAAA,gBAYR,MAAM;MACZ,IAAI,IAAI,CAACK,SAAS,KAAK,IAAI,EAAE;QAC3BC,YAAY,CAAC,IAAI,CAACD,SAAS,CAAC;QAC5B,IAAI,CAACA,SAAS,GAAG,IAAI;MACvB;IACF,CAAC;IAAAL,eAAA,wBACe,MAAM;MACpB,OAAO,IAAI,CAACO,KAAK;IACnB,CAAC;EAAA;EAvBD,OAAOC,MAAMA,CAAA,EAAG;IACd,OAAO,IAAIL,OAAO,CAAC,CAAC;EACtB;EAGA;AACF;AACA;EACEM,KAAKA,CAACC,KAAK,EAAEC,EAAE,EAAE;IACf,IAAI,CAACJ,KAAK,CAAC,CAAC;IACZ,IAAI,CAACF,SAAS,GAAGO,UAAU,CAAC,MAAM;MAChC,IAAI,CAACP,SAAS,GAAG,IAAI;MACrBM,EAAE,CAAC,CAAC;IACN,CAAC,EAAED,KAAK,CAAC;EACX;AAUF;AACA,eAAe,SAASG,UAAUA,CAAA,EAAG;EACnC,MAAMC,OAAO,GAAGb,UAAU,CAACE,OAAO,CAACK,MAAM,CAAC,CAACO,OAAO;EAClDb,UAAU,CAACY,OAAO,CAACE,aAAa,CAAC;EACjC,OAAOF,OAAO;AAChB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}