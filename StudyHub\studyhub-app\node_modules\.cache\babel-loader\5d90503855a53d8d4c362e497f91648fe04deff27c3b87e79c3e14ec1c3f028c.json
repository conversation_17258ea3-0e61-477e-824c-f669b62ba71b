{"ast": null, "code": "import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getStackUtilityClass(slot) {\n  return generateUtilityClass('MuiStack', slot);\n}\nconst stackClasses = generateUtilityClasses('MuiStack', ['root']);\nexport default stackClasses;", "map": {"version": 3, "names": ["generateUtilityClasses", "generateUtilityClass", "getStackUtilityClass", "slot", "stackClasses"], "sources": ["C:/Users/<USER>/OneDrive/ドキュメント/Projects/StudyHub/studyhub-app/node_modules/@mui/material/Stack/stackClasses.js"], "sourcesContent": ["import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getStackUtilityClass(slot) {\n  return generateUtilityClass('MuiStack', slot);\n}\nconst stackClasses = generateUtilityClasses('MuiStack', ['root']);\nexport default stackClasses;"], "mappings": "AAAA,OAAOA,sBAAsB,MAAM,mCAAmC;AACtE,OAAOC,oBAAoB,MAAM,iCAAiC;AAClE,OAAO,SAASC,oBAAoBA,CAACC,IAAI,EAAE;EACzC,OAAOF,oBAAoB,CAAC,UAAU,EAAEE,IAAI,CAAC;AAC/C;AACA,MAAMC,YAAY,GAAGJ,sBAAsB,CAAC,UAAU,EAAE,CAAC,MAAM,CAAC,CAAC;AACjE,eAAeI,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}