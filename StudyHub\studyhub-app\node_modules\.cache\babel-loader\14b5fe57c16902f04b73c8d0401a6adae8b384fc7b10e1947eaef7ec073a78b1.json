{"ast": null, "code": "import { collection, addDoc, getDocs, doc, updateDoc, query, orderBy, limit, Timestamp, onSnapshot, arrayUnion, arrayRemove } from 'firebase/firestore';\nimport { db } from '../firebase/config';\nexport class PostService {\n  // Create a new post\n  static async createPost(postData) {\n    try {\n      const docRef = await addDoc(collection(db, 'posts'), {\n        ...postData,\n        timestamp: Timestamp.now(),\n        likes: [],\n        comments: 0\n      });\n      return docRef.id;\n    } catch (error) {\n      console.error('Error creating post:', error);\n      throw error;\n    }\n  }\n\n  // Get all posts\n  static async getPosts() {\n    try {\n      const q = query(collection(db, 'posts'), orderBy('timestamp', 'desc'), limit(50));\n      const querySnapshot = await getDocs(q);\n      return querySnapshot.docs.map(doc => {\n        var _doc$data$timestamp;\n        return {\n          id: doc.id,\n          ...doc.data(),\n          timestamp: ((_doc$data$timestamp = doc.data().timestamp) === null || _doc$data$timestamp === void 0 ? void 0 : _doc$data$timestamp.toDate()) || new Date()\n        };\n      });\n    } catch (error) {\n      console.error('Error fetching posts:', error);\n      throw error;\n    }\n  }\n\n  // Subscribe to real-time posts updates\n  static subscribeToPosts(callback) {\n    const q = query(collection(db, 'posts'), orderBy('timestamp', 'desc'), limit(50));\n    return onSnapshot(q, querySnapshot => {\n      const posts = querySnapshot.docs.map(doc => {\n        var _doc$data$timestamp2;\n        return {\n          id: doc.id,\n          ...doc.data(),\n          timestamp: ((_doc$data$timestamp2 = doc.data().timestamp) === null || _doc$data$timestamp2 === void 0 ? void 0 : _doc$data$timestamp2.toDate()) || new Date()\n        };\n      });\n      callback(posts);\n    }, error => {\n      console.error('Error in posts subscription:', error);\n    });\n  }\n\n  // Like a post\n  static async likePost(postId, userId) {\n    try {\n      const postRef = doc(db, 'posts', postId);\n      await updateDoc(postRef, {\n        likes: arrayUnion(userId)\n      });\n    } catch (error) {\n      console.error('Error liking post:', error);\n      throw error;\n    }\n  }\n\n  // Unlike a post\n  static async unlikePost(postId, userId) {\n    try {\n      const postRef = doc(db, 'posts', postId);\n      await updateDoc(postRef, {\n        likes: arrayRemove(userId)\n      });\n    } catch (error) {\n      console.error('Error unliking post:', error);\n      throw error;\n    }\n  }\n\n  // Add a comment to a post\n  static async addComment(commentData) {\n    try {\n      // Add comment to comments collection\n      const docRef = await addDoc(collection(db, 'comments'), {\n        ...commentData,\n        timestamp: Timestamp.now()\n      });\n\n      // Increment comment count on the post\n      const postRef = doc(db, 'posts', commentData.postId);\n      await updateDoc(postRef, {\n        comments: arrayUnion(docRef.id)\n      });\n      return docRef.id;\n    } catch (error) {\n      console.error('Error adding comment:', error);\n      throw error;\n    }\n  }\n\n  // Get comments for a post\n  static async getPostComments(postId) {\n    try {\n      const q = query(collection(db, 'comments'), orderBy('timestamp', 'asc'));\n      const querySnapshot = await getDocs(q);\n      const allComments = querySnapshot.docs.map(doc => {\n        var _doc$data$timestamp3;\n        return {\n          id: doc.id,\n          ...doc.data(),\n          timestamp: ((_doc$data$timestamp3 = doc.data().timestamp) === null || _doc$data$timestamp3 === void 0 ? void 0 : _doc$data$timestamp3.toDate()) || new Date()\n        };\n      });\n\n      // Filter comments for this specific post\n      return allComments.filter(comment => comment.postId === postId);\n    } catch (error) {\n      console.error('Error fetching post comments:', error);\n      throw error;\n    }\n  }\n}", "map": {"version": 3, "names": ["collection", "addDoc", "getDocs", "doc", "updateDoc", "query", "orderBy", "limit", "Timestamp", "onSnapshot", "arrayUnion", "arrayRemove", "db", "PostService", "createPost", "postData", "doc<PERSON>ef", "timestamp", "now", "likes", "comments", "id", "error", "console", "getPosts", "q", "querySnapshot", "docs", "map", "_doc$data$timestamp", "data", "toDate", "Date", "subscribeToPosts", "callback", "posts", "_doc$data$timestamp2", "likePost", "postId", "userId", "postRef", "unlikePost", "addComment", "commentData", "getPostComments", "allComments", "_doc$data$timestamp3", "filter", "comment"], "sources": ["C:/Users/<USER>/OneDrive/ドキュメント/Projects/StudyHub/studyhub-app/src/services/postService.ts"], "sourcesContent": ["import { \n  collection, \n  addDoc, \n  getDocs, \n  doc, \n  updateDoc, \n  query, \n  orderBy, \n  limit,\n  Timestamp,\n  onSnapshot,\n  arrayUnion,\n  arrayRemove \n} from 'firebase/firestore';\nimport { db } from '../firebase/config';\n\nexport interface Post {\n  id: string;\n  authorId: string;\n  authorName: string;\n  authorAvatar?: string;\n  content: string;\n  imageUrl?: string;\n  timestamp: Date;\n  likes: string[]; // Array of user IDs who liked the post\n  comments: number;\n  tags: string[];\n}\n\nexport interface PostComment {\n  id: string;\n  postId: string;\n  authorId: string;\n  authorName: string;\n  authorAvatar?: string;\n  content: string;\n  timestamp: Date;\n}\n\nexport class PostService {\n  // Create a new post\n  static async createPost(postData: Omit<Post, 'id' | 'timestamp' | 'likes' | 'comments'>): Promise<string> {\n    try {\n      const docRef = await addDoc(collection(db, 'posts'), {\n        ...postData,\n        timestamp: Timestamp.now(),\n        likes: [],\n        comments: 0,\n      });\n      return docRef.id;\n    } catch (error) {\n      console.error('Error creating post:', error);\n      throw error;\n    }\n  }\n\n  // Get all posts\n  static async getPosts(): Promise<Post[]> {\n    try {\n      const q = query(\n        collection(db, 'posts'),\n        orderBy('timestamp', 'desc'),\n        limit(50)\n      );\n      \n      const querySnapshot = await getDocs(q);\n      return querySnapshot.docs.map(doc => ({\n        id: doc.id,\n        ...doc.data(),\n        timestamp: doc.data().timestamp?.toDate() || new Date(),\n      })) as Post[];\n    } catch (error) {\n      console.error('Error fetching posts:', error);\n      throw error;\n    }\n  }\n\n  // Subscribe to real-time posts updates\n  static subscribeToPosts(callback: (posts: Post[]) => void): () => void {\n    const q = query(\n      collection(db, 'posts'),\n      orderBy('timestamp', 'desc'),\n      limit(50)\n    );\n    \n    return onSnapshot(q, (querySnapshot) => {\n      const posts = querySnapshot.docs.map(doc => ({\n        id: doc.id,\n        ...doc.data(),\n        timestamp: doc.data().timestamp?.toDate() || new Date(),\n      })) as Post[];\n      callback(posts);\n    }, (error) => {\n      console.error('Error in posts subscription:', error);\n    });\n  }\n\n  // Like a post\n  static async likePost(postId: string, userId: string): Promise<void> {\n    try {\n      const postRef = doc(db, 'posts', postId);\n      await updateDoc(postRef, {\n        likes: arrayUnion(userId)\n      });\n    } catch (error) {\n      console.error('Error liking post:', error);\n      throw error;\n    }\n  }\n\n  // Unlike a post\n  static async unlikePost(postId: string, userId: string): Promise<void> {\n    try {\n      const postRef = doc(db, 'posts', postId);\n      await updateDoc(postRef, {\n        likes: arrayRemove(userId)\n      });\n    } catch (error) {\n      console.error('Error unliking post:', error);\n      throw error;\n    }\n  }\n\n  // Add a comment to a post\n  static async addComment(commentData: Omit<PostComment, 'id' | 'timestamp'>): Promise<string> {\n    try {\n      // Add comment to comments collection\n      const docRef = await addDoc(collection(db, 'comments'), {\n        ...commentData,\n        timestamp: Timestamp.now(),\n      });\n\n      // Increment comment count on the post\n      const postRef = doc(db, 'posts', commentData.postId);\n      await updateDoc(postRef, {\n        comments: arrayUnion(docRef.id)\n      });\n\n      return docRef.id;\n    } catch (error) {\n      console.error('Error adding comment:', error);\n      throw error;\n    }\n  }\n\n  // Get comments for a post\n  static async getPostComments(postId: string): Promise<PostComment[]> {\n    try {\n      const q = query(\n        collection(db, 'comments'),\n        orderBy('timestamp', 'asc')\n      );\n      \n      const querySnapshot = await getDocs(q);\n      const allComments = querySnapshot.docs.map(doc => ({\n        id: doc.id,\n        ...doc.data(),\n        timestamp: doc.data().timestamp?.toDate() || new Date(),\n      })) as PostComment[];\n\n      // Filter comments for this specific post\n      return allComments.filter(comment => comment.postId === postId);\n    } catch (error) {\n      console.error('Error fetching post comments:', error);\n      throw error;\n    }\n  }\n}\n"], "mappings": "AAAA,SACEA,UAAU,EACVC,MAAM,EACNC,OAAO,EACPC,GAAG,EACHC,SAAS,EACTC,KAAK,EACLC,OAAO,EACPC,KAAK,EACLC,SAAS,EACTC,UAAU,EACVC,UAAU,EACVC,WAAW,QACN,oBAAoB;AAC3B,SAASC,EAAE,QAAQ,oBAAoB;AAyBvC,OAAO,MAAMC,WAAW,CAAC;EACvB;EACA,aAAaC,UAAUA,CAACC,QAA+D,EAAmB;IACxG,IAAI;MACF,MAAMC,MAAM,GAAG,MAAMf,MAAM,CAACD,UAAU,CAACY,EAAE,EAAE,OAAO,CAAC,EAAE;QACnD,GAAGG,QAAQ;QACXE,SAAS,EAAET,SAAS,CAACU,GAAG,CAAC,CAAC;QAC1BC,KAAK,EAAE,EAAE;QACTC,QAAQ,EAAE;MACZ,CAAC,CAAC;MACF,OAAOJ,MAAM,CAACK,EAAE;IAClB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC5C,MAAMA,KAAK;IACb;EACF;;EAEA;EACA,aAAaE,QAAQA,CAAA,EAAoB;IACvC,IAAI;MACF,MAAMC,CAAC,GAAGpB,KAAK,CACbL,UAAU,CAACY,EAAE,EAAE,OAAO,CAAC,EACvBN,OAAO,CAAC,WAAW,EAAE,MAAM,CAAC,EAC5BC,KAAK,CAAC,EAAE,CACV,CAAC;MAED,MAAMmB,aAAa,GAAG,MAAMxB,OAAO,CAACuB,CAAC,CAAC;MACtC,OAAOC,aAAa,CAACC,IAAI,CAACC,GAAG,CAACzB,GAAG;QAAA,IAAA0B,mBAAA;QAAA,OAAK;UACpCR,EAAE,EAAElB,GAAG,CAACkB,EAAE;UACV,GAAGlB,GAAG,CAAC2B,IAAI,CAAC,CAAC;UACbb,SAAS,EAAE,EAAAY,mBAAA,GAAA1B,GAAG,CAAC2B,IAAI,CAAC,CAAC,CAACb,SAAS,cAAAY,mBAAA,uBAApBA,mBAAA,CAAsBE,MAAM,CAAC,CAAC,KAAI,IAAIC,IAAI,CAAC;QACxD,CAAC;MAAA,CAAC,CAAC;IACL,CAAC,CAAC,OAAOV,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7C,MAAMA,KAAK;IACb;EACF;;EAEA;EACA,OAAOW,gBAAgBA,CAACC,QAAiC,EAAc;IACrE,MAAMT,CAAC,GAAGpB,KAAK,CACbL,UAAU,CAACY,EAAE,EAAE,OAAO,CAAC,EACvBN,OAAO,CAAC,WAAW,EAAE,MAAM,CAAC,EAC5BC,KAAK,CAAC,EAAE,CACV,CAAC;IAED,OAAOE,UAAU,CAACgB,CAAC,EAAGC,aAAa,IAAK;MACtC,MAAMS,KAAK,GAAGT,aAAa,CAACC,IAAI,CAACC,GAAG,CAACzB,GAAG;QAAA,IAAAiC,oBAAA;QAAA,OAAK;UAC3Cf,EAAE,EAAElB,GAAG,CAACkB,EAAE;UACV,GAAGlB,GAAG,CAAC2B,IAAI,CAAC,CAAC;UACbb,SAAS,EAAE,EAAAmB,oBAAA,GAAAjC,GAAG,CAAC2B,IAAI,CAAC,CAAC,CAACb,SAAS,cAAAmB,oBAAA,uBAApBA,oBAAA,CAAsBL,MAAM,CAAC,CAAC,KAAI,IAAIC,IAAI,CAAC;QACxD,CAAC;MAAA,CAAC,CAAW;MACbE,QAAQ,CAACC,KAAK,CAAC;IACjB,CAAC,EAAGb,KAAK,IAAK;MACZC,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;IACtD,CAAC,CAAC;EACJ;;EAEA;EACA,aAAae,QAAQA,CAACC,MAAc,EAAEC,MAAc,EAAiB;IACnE,IAAI;MACF,MAAMC,OAAO,GAAGrC,GAAG,CAACS,EAAE,EAAE,OAAO,EAAE0B,MAAM,CAAC;MACxC,MAAMlC,SAAS,CAACoC,OAAO,EAAE;QACvBrB,KAAK,EAAET,UAAU,CAAC6B,MAAM;MAC1B,CAAC,CAAC;IACJ,CAAC,CAAC,OAAOjB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;MAC1C,MAAMA,KAAK;IACb;EACF;;EAEA;EACA,aAAamB,UAAUA,CAACH,MAAc,EAAEC,MAAc,EAAiB;IACrE,IAAI;MACF,MAAMC,OAAO,GAAGrC,GAAG,CAACS,EAAE,EAAE,OAAO,EAAE0B,MAAM,CAAC;MACxC,MAAMlC,SAAS,CAACoC,OAAO,EAAE;QACvBrB,KAAK,EAAER,WAAW,CAAC4B,MAAM;MAC3B,CAAC,CAAC;IACJ,CAAC,CAAC,OAAOjB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC5C,MAAMA,KAAK;IACb;EACF;;EAEA;EACA,aAAaoB,UAAUA,CAACC,WAAkD,EAAmB;IAC3F,IAAI;MACF;MACA,MAAM3B,MAAM,GAAG,MAAMf,MAAM,CAACD,UAAU,CAACY,EAAE,EAAE,UAAU,CAAC,EAAE;QACtD,GAAG+B,WAAW;QACd1B,SAAS,EAAET,SAAS,CAACU,GAAG,CAAC;MAC3B,CAAC,CAAC;;MAEF;MACA,MAAMsB,OAAO,GAAGrC,GAAG,CAACS,EAAE,EAAE,OAAO,EAAE+B,WAAW,CAACL,MAAM,CAAC;MACpD,MAAMlC,SAAS,CAACoC,OAAO,EAAE;QACvBpB,QAAQ,EAAEV,UAAU,CAACM,MAAM,CAACK,EAAE;MAChC,CAAC,CAAC;MAEF,OAAOL,MAAM,CAACK,EAAE;IAClB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7C,MAAMA,KAAK;IACb;EACF;;EAEA;EACA,aAAasB,eAAeA,CAACN,MAAc,EAA0B;IACnE,IAAI;MACF,MAAMb,CAAC,GAAGpB,KAAK,CACbL,UAAU,CAACY,EAAE,EAAE,UAAU,CAAC,EAC1BN,OAAO,CAAC,WAAW,EAAE,KAAK,CAC5B,CAAC;MAED,MAAMoB,aAAa,GAAG,MAAMxB,OAAO,CAACuB,CAAC,CAAC;MACtC,MAAMoB,WAAW,GAAGnB,aAAa,CAACC,IAAI,CAACC,GAAG,CAACzB,GAAG;QAAA,IAAA2C,oBAAA;QAAA,OAAK;UACjDzB,EAAE,EAAElB,GAAG,CAACkB,EAAE;UACV,GAAGlB,GAAG,CAAC2B,IAAI,CAAC,CAAC;UACbb,SAAS,EAAE,EAAA6B,oBAAA,GAAA3C,GAAG,CAAC2B,IAAI,CAAC,CAAC,CAACb,SAAS,cAAA6B,oBAAA,uBAApBA,oBAAA,CAAsBf,MAAM,CAAC,CAAC,KAAI,IAAIC,IAAI,CAAC;QACxD,CAAC;MAAA,CAAC,CAAkB;;MAEpB;MACA,OAAOa,WAAW,CAACE,MAAM,CAACC,OAAO,IAAIA,OAAO,CAACV,MAAM,KAAKA,MAAM,CAAC;IACjE,CAAC,CAAC,OAAOhB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;MACrD,MAAMA,KAAK;IACb;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}