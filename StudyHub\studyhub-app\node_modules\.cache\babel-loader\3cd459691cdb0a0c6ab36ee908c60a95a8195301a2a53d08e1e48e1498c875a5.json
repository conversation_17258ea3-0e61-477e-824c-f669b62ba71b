{"ast": null, "code": "import { isElement } from \"./instanceOf.js\";\nexport default function getDocumentElement(element) {\n  // $FlowFixMe[incompatible-return]: assume body is always available\n  return ((isElement(element) ? element.ownerDocument :\n  // $FlowFixMe[prop-missing]\n  element.document) || window.document).documentElement;\n}", "map": {"version": 3, "names": ["isElement", "getDocumentElement", "element", "ownerDocument", "document", "window", "documentElement"], "sources": ["C:/Users/<USER>/OneDrive/ドキュメント/Projects/StudyHub/studyhub-app/node_modules/@popperjs/core/lib/dom-utils/getDocumentElement.js"], "sourcesContent": ["import { isElement } from \"./instanceOf.js\";\nexport default function getDocumentElement(element) {\n  // $FlowFixMe[incompatible-return]: assume body is always available\n  return ((isElement(element) ? element.ownerDocument : // $FlowFixMe[prop-missing]\n  element.document) || window.document).documentElement;\n}"], "mappings": "AAAA,SAASA,SAAS,QAAQ,iBAAiB;AAC3C,eAAe,SAASC,kBAAkBA,CAACC,OAAO,EAAE;EAClD;EACA,OAAO,CAAC,CAACF,SAAS,CAACE,OAAO,CAAC,GAAGA,OAAO,CAACC,aAAa;EAAG;EACtDD,OAAO,CAACE,QAAQ,KAAKC,MAAM,CAACD,QAAQ,EAAEE,eAAe;AACvD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}