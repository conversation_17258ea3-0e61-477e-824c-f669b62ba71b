// Simple Firebase connection test
import { auth, db } from './firebase/config';
import { createUserWithEmailAndPassword } from 'firebase/auth';

console.log('Firebase Auth:', auth);
console.log('Firebase DB:', db);
console.log('Auth config:', auth.config);

// Test function to check Firebase connection
export const testFirebaseConnection = async () => {
  try {
    console.log('Testing Firebase connection...');
    console.log('Auth instance:', auth);
    console.log('Auth app:', auth.app);
    console.log('Auth config:', auth.config);
    
    // Try to create a test user (this will fail but should give us useful error info)
    await createUserWithEmailAndPassword(auth, '<EMAIL>', 'testpassword');
  } catch (error) {
    console.log('Expected error (this helps us debug):', error);
    console.log('Error code:', error.code);
    console.log('Error message:', error.message);
  }
};

// Call the test function
testFirebaseConnection();
