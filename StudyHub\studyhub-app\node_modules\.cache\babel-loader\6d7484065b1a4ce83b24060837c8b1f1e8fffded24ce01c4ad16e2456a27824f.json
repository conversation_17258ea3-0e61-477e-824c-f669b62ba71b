{"ast": null, "code": "'use client';\n\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"activeStep\", \"alternativeLabel\", \"children\", \"className\", \"component\", \"connector\", \"nonLinear\", \"orientation\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport integerPropType from '@mui/utils/integerPropType';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { useDefaultProps } from '../DefaultPropsProvider';\nimport styled from '../styles/styled';\nimport { getStepperUtilityClass } from './stepperClasses';\nimport StepConnector from '../StepConnector';\nimport StepperContext from './StepperContext';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    orientation,\n    nonLinear,\n    alternativeLabel,\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['root', orientation, nonLinear && 'nonLinear', alternativeLabel && 'alternativeLabel']\n  };\n  return composeClasses(slots, getStepperUtilityClass, classes);\n};\nconst StepperRoot = styled('div', {\n  name: 'MuiStepper',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.root, styles[ownerState.orientation], ownerState.alternativeLabel && styles.alternativeLabel, ownerState.nonLinear && styles.nonLinear];\n  }\n})(({\n  ownerState\n}) => _extends({\n  display: 'flex'\n}, ownerState.orientation === 'horizontal' && {\n  flexDirection: 'row',\n  alignItems: 'center'\n}, ownerState.orientation === 'vertical' && {\n  flexDirection: 'column'\n}, ownerState.alternativeLabel && {\n  alignItems: 'flex-start'\n}));\nconst defaultConnector = /*#__PURE__*/_jsx(StepConnector, {});\nconst Stepper = /*#__PURE__*/React.forwardRef(function Stepper(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiStepper'\n  });\n  const {\n      activeStep = 0,\n      alternativeLabel = false,\n      children,\n      className,\n      component = 'div',\n      connector = defaultConnector,\n      nonLinear = false,\n      orientation = 'horizontal'\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const ownerState = _extends({}, props, {\n    nonLinear,\n    alternativeLabel,\n    orientation,\n    component\n  });\n  const classes = useUtilityClasses(ownerState);\n  const childrenArray = React.Children.toArray(children).filter(Boolean);\n  const steps = childrenArray.map((step, index) => {\n    return /*#__PURE__*/React.cloneElement(step, _extends({\n      index,\n      last: index + 1 === childrenArray.length\n    }, step.props));\n  });\n  const contextValue = React.useMemo(() => ({\n    activeStep,\n    alternativeLabel,\n    connector,\n    nonLinear,\n    orientation\n  }), [activeStep, alternativeLabel, connector, nonLinear, orientation]);\n  return /*#__PURE__*/_jsx(StepperContext.Provider, {\n    value: contextValue,\n    children: /*#__PURE__*/_jsx(StepperRoot, _extends({\n      as: component,\n      ownerState: ownerState,\n      className: clsx(classes.root, className),\n      ref: ref\n    }, other, {\n      children: steps\n    }))\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? Stepper.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * Set the active step (zero based index).\n   * Set to -1 to disable all the steps.\n   * @default 0\n   */\n  activeStep: integerPropType,\n  /**\n   * If set to 'true' and orientation is horizontal,\n   * then the step label will be positioned under the icon.\n   * @default false\n   */\n  alternativeLabel: PropTypes.bool,\n  /**\n   * Two or more `<Step />` components.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * An element to be placed between each step.\n   * @default <StepConnector />\n   */\n  connector: PropTypes.element,\n  /**\n   * If set the `Stepper` will not assist in controlling steps for linear flow.\n   * @default false\n   */\n  nonLinear: PropTypes.bool,\n  /**\n   * The component orientation (layout flow direction).\n   * @default 'horizontal'\n   */\n  orientation: PropTypes.oneOf(['horizontal', 'vertical']),\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport default Stepper;", "map": {"version": 3, "names": ["_objectWithoutPropertiesLoose", "_extends", "_excluded", "React", "PropTypes", "clsx", "integerPropType", "composeClasses", "useDefaultProps", "styled", "getStepperUtilityClass", "StepConnector", "StepperContext", "jsx", "_jsx", "useUtilityClasses", "ownerState", "orientation", "nonLinear", "alternativeLabel", "classes", "slots", "root", "StepperRoot", "name", "slot", "overridesResolver", "props", "styles", "display", "flexDirection", "alignItems", "defaultConnector", "Stepper", "forwardRef", "inProps", "ref", "activeStep", "children", "className", "component", "connector", "other", "childrenA<PERSON>y", "Children", "toArray", "filter", "Boolean", "steps", "map", "step", "index", "cloneElement", "last", "length", "contextValue", "useMemo", "Provider", "value", "as", "process", "env", "NODE_ENV", "propTypes", "bool", "node", "object", "string", "elementType", "element", "oneOf", "sx", "oneOfType", "arrayOf", "func"], "sources": ["C:/Users/<USER>/OneDrive/ドキュメント/Projects/StudyHub/studyhub-app/node_modules/@mui/material/Stepper/Stepper.js"], "sourcesContent": ["'use client';\n\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"activeStep\", \"alternativeLabel\", \"children\", \"className\", \"component\", \"connector\", \"nonLinear\", \"orientation\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport integerPropType from '@mui/utils/integerPropType';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { useDefaultProps } from '../DefaultPropsProvider';\nimport styled from '../styles/styled';\nimport { getStepperUtilityClass } from './stepperClasses';\nimport StepConnector from '../StepConnector';\nimport StepperContext from './StepperContext';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    orientation,\n    nonLinear,\n    alternativeLabel,\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['root', orientation, nonLinear && 'nonLinear', alternativeLabel && 'alternativeLabel']\n  };\n  return composeClasses(slots, getStepperUtilityClass, classes);\n};\nconst StepperRoot = styled('div', {\n  name: 'MuiStepper',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.root, styles[ownerState.orientation], ownerState.alternativeLabel && styles.alternativeLabel, ownerState.nonLinear && styles.nonLinear];\n  }\n})(({\n  ownerState\n}) => _extends({\n  display: 'flex'\n}, ownerState.orientation === 'horizontal' && {\n  flexDirection: 'row',\n  alignItems: 'center'\n}, ownerState.orientation === 'vertical' && {\n  flexDirection: 'column'\n}, ownerState.alternativeLabel && {\n  alignItems: 'flex-start'\n}));\nconst defaultConnector = /*#__PURE__*/_jsx(StepConnector, {});\nconst Stepper = /*#__PURE__*/React.forwardRef(function Stepper(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiStepper'\n  });\n  const {\n      activeStep = 0,\n      alternativeLabel = false,\n      children,\n      className,\n      component = 'div',\n      connector = defaultConnector,\n      nonLinear = false,\n      orientation = 'horizontal'\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const ownerState = _extends({}, props, {\n    nonLinear,\n    alternativeLabel,\n    orientation,\n    component\n  });\n  const classes = useUtilityClasses(ownerState);\n  const childrenArray = React.Children.toArray(children).filter(Boolean);\n  const steps = childrenArray.map((step, index) => {\n    return /*#__PURE__*/React.cloneElement(step, _extends({\n      index,\n      last: index + 1 === childrenArray.length\n    }, step.props));\n  });\n  const contextValue = React.useMemo(() => ({\n    activeStep,\n    alternativeLabel,\n    connector,\n    nonLinear,\n    orientation\n  }), [activeStep, alternativeLabel, connector, nonLinear, orientation]);\n  return /*#__PURE__*/_jsx(StepperContext.Provider, {\n    value: contextValue,\n    children: /*#__PURE__*/_jsx(StepperRoot, _extends({\n      as: component,\n      ownerState: ownerState,\n      className: clsx(classes.root, className),\n      ref: ref\n    }, other, {\n      children: steps\n    }))\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? Stepper.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * Set the active step (zero based index).\n   * Set to -1 to disable all the steps.\n   * @default 0\n   */\n  activeStep: integerPropType,\n  /**\n   * If set to 'true' and orientation is horizontal,\n   * then the step label will be positioned under the icon.\n   * @default false\n   */\n  alternativeLabel: PropTypes.bool,\n  /**\n   * Two or more `<Step />` components.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * An element to be placed between each step.\n   * @default <StepConnector />\n   */\n  connector: PropTypes.element,\n  /**\n   * If set the `Stepper` will not assist in controlling steps for linear flow.\n   * @default false\n   */\n  nonLinear: PropTypes.bool,\n  /**\n   * The component orientation (layout flow direction).\n   * @default 'horizontal'\n   */\n  orientation: PropTypes.oneOf(['horizontal', 'vertical']),\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport default Stepper;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,6BAA6B,MAAM,yDAAyD;AACnG,OAAOC,QAAQ,MAAM,oCAAoC;AACzD,MAAMC,SAAS,GAAG,CAAC,YAAY,EAAE,kBAAkB,EAAE,UAAU,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,aAAa,CAAC;AACnI,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,IAAI,MAAM,MAAM;AACvB,OAAOC,eAAe,MAAM,4BAA4B;AACxD,OAAOC,cAAc,MAAM,2BAA2B;AACtD,SAASC,eAAe,QAAQ,yBAAyB;AACzD,OAAOC,MAAM,MAAM,kBAAkB;AACrC,SAASC,sBAAsB,QAAQ,kBAAkB;AACzD,OAAOC,aAAa,MAAM,kBAAkB;AAC5C,OAAOC,cAAc,MAAM,kBAAkB;AAC7C,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,iBAAiB,GAAGC,UAAU,IAAI;EACtC,MAAM;IACJC,WAAW;IACXC,SAAS;IACTC,gBAAgB;IAChBC;EACF,CAAC,GAAGJ,UAAU;EACd,MAAMK,KAAK,GAAG;IACZC,IAAI,EAAE,CAAC,MAAM,EAAEL,WAAW,EAAEC,SAAS,IAAI,WAAW,EAAEC,gBAAgB,IAAI,kBAAkB;EAC9F,CAAC;EACD,OAAOZ,cAAc,CAACc,KAAK,EAAEX,sBAAsB,EAAEU,OAAO,CAAC;AAC/D,CAAC;AACD,MAAMG,WAAW,GAAGd,MAAM,CAAC,KAAK,EAAE;EAChCe,IAAI,EAAE,YAAY;EAClBC,IAAI,EAAE,MAAM;EACZC,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAK;IACpC,MAAM;MACJZ;IACF,CAAC,GAAGW,KAAK;IACT,OAAO,CAACC,MAAM,CAACN,IAAI,EAAEM,MAAM,CAACZ,UAAU,CAACC,WAAW,CAAC,EAAED,UAAU,CAACG,gBAAgB,IAAIS,MAAM,CAACT,gBAAgB,EAAEH,UAAU,CAACE,SAAS,IAAIU,MAAM,CAACV,SAAS,CAAC;EACxJ;AACF,CAAC,CAAC,CAAC,CAAC;EACFF;AACF,CAAC,KAAKf,QAAQ,CAAC;EACb4B,OAAO,EAAE;AACX,CAAC,EAAEb,UAAU,CAACC,WAAW,KAAK,YAAY,IAAI;EAC5Ca,aAAa,EAAE,KAAK;EACpBC,UAAU,EAAE;AACd,CAAC,EAAEf,UAAU,CAACC,WAAW,KAAK,UAAU,IAAI;EAC1Ca,aAAa,EAAE;AACjB,CAAC,EAAEd,UAAU,CAACG,gBAAgB,IAAI;EAChCY,UAAU,EAAE;AACd,CAAC,CAAC,CAAC;AACH,MAAMC,gBAAgB,GAAG,aAAalB,IAAI,CAACH,aAAa,EAAE,CAAC,CAAC,CAAC;AAC7D,MAAMsB,OAAO,GAAG,aAAa9B,KAAK,CAAC+B,UAAU,CAAC,SAASD,OAAOA,CAACE,OAAO,EAAEC,GAAG,EAAE;EAC3E,MAAMT,KAAK,GAAGnB,eAAe,CAAC;IAC5BmB,KAAK,EAAEQ,OAAO;IACdX,IAAI,EAAE;EACR,CAAC,CAAC;EACF,MAAM;MACFa,UAAU,GAAG,CAAC;MACdlB,gBAAgB,GAAG,KAAK;MACxBmB,QAAQ;MACRC,SAAS;MACTC,SAAS,GAAG,KAAK;MACjBC,SAAS,GAAGT,gBAAgB;MAC5Bd,SAAS,GAAG,KAAK;MACjBD,WAAW,GAAG;IAChB,CAAC,GAAGU,KAAK;IACTe,KAAK,GAAG1C,6BAA6B,CAAC2B,KAAK,EAAEzB,SAAS,CAAC;EACzD,MAAMc,UAAU,GAAGf,QAAQ,CAAC,CAAC,CAAC,EAAE0B,KAAK,EAAE;IACrCT,SAAS;IACTC,gBAAgB;IAChBF,WAAW;IACXuB;EACF,CAAC,CAAC;EACF,MAAMpB,OAAO,GAAGL,iBAAiB,CAACC,UAAU,CAAC;EAC7C,MAAM2B,aAAa,GAAGxC,KAAK,CAACyC,QAAQ,CAACC,OAAO,CAACP,QAAQ,CAAC,CAACQ,MAAM,CAACC,OAAO,CAAC;EACtE,MAAMC,KAAK,GAAGL,aAAa,CAACM,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,KAAK;IAC/C,OAAO,aAAahD,KAAK,CAACiD,YAAY,CAACF,IAAI,EAAEjD,QAAQ,CAAC;MACpDkD,KAAK;MACLE,IAAI,EAAEF,KAAK,GAAG,CAAC,KAAKR,aAAa,CAACW;IACpC,CAAC,EAAEJ,IAAI,CAACvB,KAAK,CAAC,CAAC;EACjB,CAAC,CAAC;EACF,MAAM4B,YAAY,GAAGpD,KAAK,CAACqD,OAAO,CAAC,OAAO;IACxCnB,UAAU;IACVlB,gBAAgB;IAChBsB,SAAS;IACTvB,SAAS;IACTD;EACF,CAAC,CAAC,EAAE,CAACoB,UAAU,EAAElB,gBAAgB,EAAEsB,SAAS,EAAEvB,SAAS,EAAED,WAAW,CAAC,CAAC;EACtE,OAAO,aAAaH,IAAI,CAACF,cAAc,CAAC6C,QAAQ,EAAE;IAChDC,KAAK,EAAEH,YAAY;IACnBjB,QAAQ,EAAE,aAAaxB,IAAI,CAACS,WAAW,EAAEtB,QAAQ,CAAC;MAChD0D,EAAE,EAAEnB,SAAS;MACbxB,UAAU,EAAEA,UAAU;MACtBuB,SAAS,EAAElC,IAAI,CAACe,OAAO,CAACE,IAAI,EAAEiB,SAAS,CAAC;MACxCH,GAAG,EAAEA;IACP,CAAC,EAAEM,KAAK,EAAE;MACRJ,QAAQ,EAAEU;IACZ,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ,CAAC,CAAC;AACFY,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAG7B,OAAO,CAAC8B,SAAS,CAAC,yBAAyB;EACjF;EACA;EACA;EACA;EACA;AACF;AACA;AACA;AACA;EACE1B,UAAU,EAAE/B,eAAe;EAC3B;AACF;AACA;AACA;AACA;EACEa,gBAAgB,EAAEf,SAAS,CAAC4D,IAAI;EAChC;AACF;AACA;EACE1B,QAAQ,EAAElC,SAAS,CAAC6D,IAAI;EACxB;AACF;AACA;EACE7C,OAAO,EAAEhB,SAAS,CAAC8D,MAAM;EACzB;AACF;AACA;EACE3B,SAAS,EAAEnC,SAAS,CAAC+D,MAAM;EAC3B;AACF;AACA;AACA;EACE3B,SAAS,EAAEpC,SAAS,CAACgE,WAAW;EAChC;AACF;AACA;AACA;EACE3B,SAAS,EAAErC,SAAS,CAACiE,OAAO;EAC5B;AACF;AACA;AACA;EACEnD,SAAS,EAAEd,SAAS,CAAC4D,IAAI;EACzB;AACF;AACA;AACA;EACE/C,WAAW,EAAEb,SAAS,CAACkE,KAAK,CAAC,CAAC,YAAY,EAAE,UAAU,CAAC,CAAC;EACxD;AACF;AACA;EACEC,EAAE,EAAEnE,SAAS,CAACoE,SAAS,CAAC,CAACpE,SAAS,CAACqE,OAAO,CAACrE,SAAS,CAACoE,SAAS,CAAC,CAACpE,SAAS,CAACsE,IAAI,EAAEtE,SAAS,CAAC8D,MAAM,EAAE9D,SAAS,CAAC4D,IAAI,CAAC,CAAC,CAAC,EAAE5D,SAAS,CAACsE,IAAI,EAAEtE,SAAS,CAAC8D,MAAM,CAAC;AACxJ,CAAC,GAAG,KAAK,CAAC;AACV,eAAejC,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}