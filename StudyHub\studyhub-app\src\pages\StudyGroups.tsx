import React, { useState, useEffect } from 'react';
import {
  Con<PERSON><PERSON>,
  <PERSON>po<PERSON>,
  <PERSON>,
  CardContent,
  CardActions,
  Button,
  Box,
  Chip,
  Avatar,
  AvatarGroup,
  Fab,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Switch,
  FormControlLabel,
  Alert,
  CircularProgress,
  Tabs,
  Tab,
  IconButton,
  Menu,
  ListItemIcon,
  ListItemText,
  Divider,
  Tooltip,
} from '@mui/material';
import { Grid } from '@mui/material';
import {
  Add,
  Group,
  Person,
  Schedule,
  Public,
  Lock,
  Search,
  FilterList,
  MoreVert,
  ExitToApp,
  Settings,
  Chat,
  People,
} from '@mui/icons-material';
import { useAuth } from '../contexts/AuthContext';
import { StudyGroupService } from '../services/studyGroupService';
import { StudyGroup, CreateStudyGroupData } from '../types/studyGroup';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`simple-tabpanel-${index}`}
      aria-labelledby={`simple-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
}

const StudyGroups: React.FC = () => {
  const { currentUser, userProfile } = useAuth();
  const [tabValue, setTabValue] = useState(0);
  const [open, setOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [publicGroups, setPublicGroups] = useState<StudyGroup[]>([]);
  const [myGroups, setMyGroups] = useState<StudyGroup[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterSubject, setFilterSubject] = useState('');
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [selectedGroup, setSelectedGroup] = useState<StudyGroup | null>(null);

  const [newGroup, setNewGroup] = useState<CreateStudyGroupData>({
    name: '',
    subject: '',
    category: '',
    description: '',
    maxMembers: 10,
    isPrivate: false,
    tags: [],
  });

  // Load study groups on component mount
  useEffect(() => {
    loadPublicGroups();
    if (currentUser) {
      loadMyGroups();
    }
  }, [currentUser]);

  const loadPublicGroups = async () => {
    try {
      setLoading(true);
      const groups = await StudyGroupService.getPublicStudyGroups();
      setPublicGroups(groups);
    } catch (error: any) {
      setError('Failed to load study groups');
    } finally {
      setLoading(false);
    }
  };

  const loadMyGroups = async () => {
    if (!currentUser) return;
    try {
      const groups = await StudyGroupService.getUserStudyGroups(currentUser.uid);
      setMyGroups(groups);
    } catch (error: any) {
      setError('Failed to load your study groups');
    }
  };

  const handleCreateGroup = async () => {
    if (!currentUser || !userProfile) return;

    try {
      setLoading(true);
      setError('');

      await StudyGroupService.createStudyGroup(newGroup, currentUser.uid, userProfile);

      setSuccess('Study group created successfully!');
      setOpen(false);
      setNewGroup({
        name: '',
        subject: '',
        category: '',
        description: '',
        maxMembers: 10,
        isPrivate: false,
        tags: [],
      });

      // Reload groups
      loadPublicGroups();
      loadMyGroups();
    } catch (error: any) {
      setError(error.message || 'Failed to create study group');
    } finally {
      setLoading(false);
    }
  };

  const handleJoinGroup = async (group: StudyGroup) => {
    if (!currentUser || !userProfile) return;

    try {
      setLoading(true);
      setError('');

      await StudyGroupService.joinStudyGroup(group.id, currentUser.uid, userProfile);

      setSuccess(`Successfully joined ${group.name}!`);

      // Reload groups
      loadPublicGroups();
      loadMyGroups();
    } catch (error: any) {
      setError(error.message || 'Failed to join study group');
    } finally {
      setLoading(false);
    }
  };

  const handleLeaveGroup = async (group: StudyGroup) => {
    if (!currentUser) return;

    try {
      setLoading(true);
      setError('');

      await StudyGroupService.leaveStudyGroup(group.id, currentUser.uid);

      setSuccess(`Left ${group.name} successfully`);

      // Reload groups
      loadPublicGroups();
      loadMyGroups();
    } catch (error: any) {
      setError(error.message || 'Failed to leave study group');
    } finally {
      setLoading(false);
    }
  };

  const handleMenuClick = (event: React.MouseEvent<HTMLElement>, group: StudyGroup) => {
    setAnchorEl(event.currentTarget);
    setSelectedGroup(group);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
    setSelectedGroup(null);
  };

  const isUserMember = (group: StudyGroup): boolean => {
    return currentUser ? group.members.some(member => member.uid === currentUser.uid) : false;
  };

  const filteredPublicGroups = publicGroups.filter(group => {
    const matchesSearch = !searchTerm ||
      group.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      group.description.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesSubject = !filterSubject || group.subject === filterSubject;

    return matchesSearch && matchesSubject;
  });

  const subjects = ['Computer Science', 'Mathematics', 'Physics', 'Chemistry', 'Biology', 'Engineering', 'Business', 'Literature', 'History', 'Psychology'];
  if (!currentUser) {
    return (
      <Container maxWidth="lg">
        <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
          <Alert severity="info">Please log in to view and join study groups.</Alert>
        </Box>
      </Container>
    );
  }

  return (
    <Container maxWidth="lg">
      <Box sx={{ mb: 4 }}>
        <Typography variant="h4" gutterBottom>
          Study Groups
        </Typography>
        <Typography variant="body1" color="text.secondary">
          Join study groups to collaborate with peers and enhance your learning experience.
        </Typography>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }} onClose={() => setError('')}>
          {error}
        </Alert>
      )}

      {success && (
        <Alert severity="success" sx={{ mb: 3 }} onClose={() => setSuccess('')}>
          {success}
        </Alert>
      )}

      <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}>
        <Tabs value={tabValue} onChange={(e, newValue) => setTabValue(newValue)}>
          <Tab label="Discover Groups" />
          <Tab label="My Groups" />
        </Tabs>
      </Box>

      <TabPanel value={tabValue} index={0}>
        {/* Search and Filter */}
        <Box sx={{ mb: 3, display: 'flex', gap: 2, alignItems: 'center' }}>
          <TextField
            placeholder="Search study groups..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            sx={{ flexGrow: 1 }}
            InputProps={{
              startAdornment: <Search sx={{ mr: 1, color: 'text.secondary' }} />,
            }}
          />
          <FormControl sx={{ minWidth: 200 }}>
            <InputLabel>Subject</InputLabel>
            <Select
              value={filterSubject}
              label="Subject"
              onChange={(e) => setFilterSubject(e.target.value)}
            >
              <MenuItem value="">All Subjects</MenuItem>
              {subjects.map((subject) => (
                <MenuItem key={subject} value={subject}>
                  {subject}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Box>

        {loading ? (
          <Box display="flex" justifyContent="center" py={4}>
            <CircularProgress />
          </Box>
        ) : (
          <Grid container spacing={3}>
            {filteredPublicGroups.map((group) => (
              <Grid xs={12} md={6} lg={4} key={group.id}>
                <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
                  <CardContent sx={{ flexGrow: 1 }}>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
                      <Typography variant="h6" component="h3" sx={{ flexGrow: 1 }}>
                        {group.name}
                      </Typography>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                        {group.isPrivate ? <Lock fontSize="small" /> : <Public fontSize="small" />}
                      </Box>
                    </Box>

                    <Box sx={{ display: 'flex', gap: 1, mb: 2 }}>
                      <Chip label={group.subject} size="small" color="primary" />
                      <Chip label={group.category} size="small" variant="outlined" />
                    </Box>

                    <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                      {group.description}
                    </Typography>

                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                        <People fontSize="small" />
                        <Typography variant="body2">
                          {group.currentMembers}/{group.maxMembers}
                        </Typography>
                      </Box>
                      <AvatarGroup max={3} sx={{ '& .MuiAvatar-root': { width: 24, height: 24, fontSize: '0.75rem' } }}>
                        {group.members.slice(0, 3).map((member, index) => (
                          <Avatar key={index} src={member.profilePicture} alt={member.displayName}>
                            {member.displayName[0]}
                          </Avatar>
                        ))}
                      </AvatarGroup>
                    </Box>

                    {group.tags.length > 0 && (
                      <Box sx={{ display: 'flex', gap: 0.5, flexWrap: 'wrap' }}>
                        {group.tags.map((tag, index) => (
                          <Chip key={index} label={tag} size="small" variant="outlined" />
                        ))}
                      </Box>
                    )}
                  </CardContent>

                  <CardActions sx={{ justifyContent: 'space-between', px: 2, pb: 2 }}>
                    {isUserMember(group) ? (
                      <Button
                        variant="outlined"
                        color="error"
                        onClick={() => handleLeaveGroup(group)}
                        disabled={loading}
                      >
                        Leave Group
                      </Button>
                    ) : (
                      <Button
                        variant="contained"
                        onClick={() => handleJoinGroup(group)}
                        disabled={loading || group.currentMembers >= group.maxMembers}
                      >
                        {group.currentMembers >= group.maxMembers ? 'Full' : 'Join Group'}
                      </Button>
                    )}
                  </CardActions>
                </Card>
              </Grid>
            ))}
          </Grid>
        )}
      </TabPanel>

      <TabPanel value={tabValue} index={1}>
        {loading ? (
          <Box display="flex" justifyContent="center" py={4}>
            <CircularProgress />
          </Box>
        ) : myGroups.length === 0 ? (
          <Box textAlign="center" py={4}>
            <Typography variant="h6" color="text.secondary" gutterBottom>
              You haven't joined any study groups yet
            </Typography>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
              Explore the Discover Groups tab to find groups that match your interests
            </Typography>
            <Button variant="contained" onClick={() => setTabValue(0)}>
              Discover Groups
            </Button>
          </Box>
        ) : (
          <Grid container spacing={3}>
            {myGroups.map((group) => (
              <Grid xs={12} md={6} lg={4} key={group.id}>
                <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
                  <CardContent sx={{ flexGrow: 1 }}>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
                      <Typography variant="h6" component="h3" sx={{ flexGrow: 1 }}>
                        {group.name}
                      </Typography>
                      <IconButton
                        size="small"
                        onClick={(e) => handleMenuClick(e, group)}
                      >
                        <MoreVert />
                      </IconButton>
                    </Box>

                    <Box sx={{ display: 'flex', gap: 1, mb: 2 }}>
                      <Chip label={group.subject} size="small" color="primary" />
                      <Chip label={group.category} size="small" variant="outlined" />
                    </Box>

                    <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                      {group.description}
                    </Typography>

                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                        <People fontSize="small" />
                        <Typography variant="body2">
                          {group.currentMembers}/{group.maxMembers}
                        </Typography>
                      </Box>
                      <AvatarGroup max={3} sx={{ '& .MuiAvatar-root': { width: 24, height: 24, fontSize: '0.75rem' } }}>
                        {group.members.slice(0, 3).map((member, index) => (
                          <Avatar key={index} src={member.profilePicture} alt={member.displayName}>
                            {member.displayName[0]}
                          </Avatar>
                        ))}
                      </AvatarGroup>
                    </Box>

                    {group.tags.length > 0 && (
                      <Box sx={{ display: 'flex', gap: 0.5, flexWrap: 'wrap' }}>
                        {group.tags.map((tag, index) => (
                          <Chip key={index} label={tag} size="small" variant="outlined" />
                        ))}
                      </Box>
                    )}
                  </CardContent>

                  <CardActions sx={{ justifyContent: 'space-between', px: 2, pb: 2 }}>
                    <Button
                      variant="contained"
                      startIcon={<Chat />}
                      onClick={() => {/* Navigate to chat */}}
                    >
                      Open Chat
                    </Button>
                    <Button
                      variant="outlined"
                      startIcon={<People />}
                      onClick={() => {/* Navigate to group details */}}
                    >
                      Members
                    </Button>
                  </CardActions>
                </Card>
              </Grid>
            ))}
          </Grid>
        )}
      </TabPanel>

      {/* Context Menu */}
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleMenuClose}
      >
        <MenuItem onClick={() => {/* Navigate to group settings */}}>
          <ListItemIcon>
            <Settings fontSize="small" />
          </ListItemIcon>
          <ListItemText>Group Settings</ListItemText>
        </MenuItem>
        <MenuItem onClick={() => {/* Navigate to chat */}}>
          <ListItemIcon>
            <Chat fontSize="small" />
          </ListItemIcon>
          <ListItemText>Open Chat</ListItemText>
        </MenuItem>
        <Divider />
        <MenuItem
          onClick={() => {
            if (selectedGroup) {
              handleLeaveGroup(selectedGroup);
            }
            handleMenuClose();
          }}
          sx={{ color: 'error.main' }}
        >
          <ListItemIcon>
            <ExitToApp fontSize="small" color="error" />
          </ListItemIcon>
          <ListItemText>Leave Group</ListItemText>
        </MenuItem>
      </Menu>

      {/* Floating Action Button */}
      <Fab
        color="primary"
        aria-label="create group"
        sx={{ position: 'fixed', bottom: 16, right: 16 }}
        onClick={() => setOpen(true)}
      >
        <Add />
      </Fab>

      {/* Create Group Dialog */}
      <Dialog open={open} onClose={() => setOpen(false)} maxWidth="sm" fullWidth>
        <DialogTitle>Create New Study Group</DialogTitle>
        <DialogContent>
          <Box sx={{ pt: 1 }}>
            <TextField
              fullWidth
              label="Group Name"
              value={newGroup.name}
              onChange={(e) => setNewGroup({ ...newGroup, name: e.target.value })}
              sx={{ mb: 2 }}
            />
            <FormControl fullWidth sx={{ mb: 2 }}>
              <InputLabel>Subject</InputLabel>
              <Select
                value={newGroup.subject}
                label="Subject"
                onChange={(e) => setNewGroup({ ...newGroup, subject: e.target.value })}
              >
                {subjects.map((subject) => (
                  <MenuItem key={subject} value={subject}>
                    {subject}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
            <FormControl fullWidth sx={{ mb: 2 }}>
              <InputLabel>Category</InputLabel>
              <Select
                value={newGroup.category}
                label="Category"
                onChange={(e) => setNewGroup({ ...newGroup, category: e.target.value })}
              >
                <MenuItem value="Study Group">Study Group</MenuItem>
                <MenuItem value="Project Team">Project Team</MenuItem>
                <MenuItem value="Exam Prep">Exam Prep</MenuItem>
                <MenuItem value="Research">Research</MenuItem>
                <MenuItem value="Homework Help">Homework Help</MenuItem>
              </Select>
            </FormControl>
            <TextField
              fullWidth
              label="Description"
              multiline
              rows={3}
              value={newGroup.description}
              onChange={(e) => setNewGroup({ ...newGroup, description: e.target.value })}
              sx={{ mb: 2 }}
            />
            <TextField
              fullWidth
              label="Maximum Members"
              type="number"
              value={newGroup.maxMembers}
              onChange={(e) => setNewGroup({ ...newGroup, maxMembers: parseInt(e.target.value) || 10 })}
              inputProps={{ min: 2, max: 50 }}
              sx={{ mb: 2 }}
            />
            <FormControlLabel
              control={
                <Switch
                  checked={newGroup.isPrivate}
                  onChange={(e) => setNewGroup({ ...newGroup, isPrivate: e.target.checked })}
                />
              }
              label="Private Group (invite only)"
            />
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpen(false)} disabled={loading}>
            Cancel
          </Button>
          <Button
            onClick={handleCreateGroup}
            variant="contained"
            disabled={loading || !newGroup.name || !newGroup.subject || !newGroup.category}
          >
            {loading ? <CircularProgress size={20} /> : 'Create Group'}
          </Button>
        </DialogActions>
      </Dialog>
    </Container>
  );
};

export default StudyGroups;
