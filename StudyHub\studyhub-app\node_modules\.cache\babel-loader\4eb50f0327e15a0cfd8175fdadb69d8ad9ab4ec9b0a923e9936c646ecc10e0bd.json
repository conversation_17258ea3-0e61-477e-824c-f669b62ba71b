{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\\\u30C9\\u30AD\\u30E5\\u30E1\\u30F3\\u30C8\\\\Projects\\\\StudyHub\\\\studyhub-app\\\\src\\\\pages\\\\Explore.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Container, Grid, Card, CardContent, Typography, Button, TextField, Box, Avatar, Chip, Paper, InputAdornment, Tab, Tabs } from '@mui/material';\nimport { Search, TrendingUp, People, Topic, Explore as ExploreIcon } from '@mui/icons-material';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Explore = () => {\n  _s();\n  const [searchQuery, setSearchQuery] = useState('');\n  const [activeTab, setActiveTab] = useState(0);\n  const [trendingTopics, setTrendingTopics] = useState([]);\n  const [suggestedUsers, setSuggestedUsers] = useState([]);\n  const [popularPosts, setPopularPosts] = useState([]);\n  useEffect(() => {\n    // Sample trending topics\n    setTrendingTopics([{\n      id: '1',\n      name: 'ClimateChange',\n      postCount: 1247,\n      category: 'Environment'\n    }, {\n      id: '2',\n      name: 'AI',\n      postCount: 892,\n      category: 'Technology'\n    }, {\n      id: '3',\n      name: 'WorkFromHome',\n      postCount: 634,\n      category: 'Lifestyle'\n    }, {\n      id: '4',\n      name: 'MentalHealth',\n      postCount: 567,\n      category: 'Wellness'\n    }, {\n      id: '5',\n      name: 'Cryptocurrency',\n      postCount: 445,\n      category: 'Finance'\n    }, {\n      id: '6',\n      name: 'SustainableLiving',\n      postCount: 389,\n      category: 'Environment'\n    }]);\n\n    // Sample suggested users\n    setSuggestedUsers([{\n      id: '1',\n      name: 'Dr. Sarah Chen',\n      bio: 'Climate scientist sharing insights on environmental issues',\n      followers: 15420,\n      isFollowing: false\n    }, {\n      id: '2',\n      name: 'Tech Innovator Mike',\n      bio: 'AI researcher and startup founder',\n      followers: 8930,\n      isFollowing: false\n    }, {\n      id: '3',\n      name: 'Wellness Coach Emma',\n      bio: 'Mental health advocate and mindfulness expert',\n      followers: 12100,\n      isFollowing: false\n    }]);\n\n    // Sample popular posts\n    setPopularPosts([{\n      id: '1',\n      authorName: 'Alex Rivera',\n      content: 'The intersection of technology and sustainability is where the future lies. We need more green tech innovations!',\n      likes: 156,\n      comments: 23,\n      tags: ['technology', 'sustainability', 'future']\n    }, {\n      id: '2',\n      authorName: 'Jordan Kim',\n      content: 'Remote work has changed how we think about productivity and work-life balance. What\\'s your experience?',\n      likes: 89,\n      comments: 34,\n      tags: ['remote', 'productivity', 'worklife']\n    }]);\n  }, []);\n  const handleTabChange = (event, newValue) => {\n    setActiveTab(newValue);\n  };\n  const handleFollow = userId => {\n    setSuggestedUsers(users => users.map(user => user.id === userId ? {\n      ...user,\n      isFollowing: !user.isFollowing\n    } : user));\n  };\n  const formatNumber = num => {\n    if (num >= 1000) {\n      return (num / 1000).toFixed(1) + 'K';\n    }\n    return num.toString();\n  };\n  return /*#__PURE__*/_jsxDEV(Container, {\n    maxWidth: \"lg\",\n    children: /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        py: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h4\",\n        gutterBottom: true,\n        sx: {\n          display: 'flex',\n          alignItems: 'center',\n          gap: 1\n        },\n        children: [/*#__PURE__*/_jsxDEV(ExploreIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 142,\n          columnNumber: 11\n        }, this), \"Explore\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 141,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"body1\",\n        color: \"text.secondary\",\n        sx: {\n          mb: 4\n        },\n        children: \"Discover trending topics, connect with new people, and explore popular content\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 145,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: 2,\n          mb: 4\n        },\n        children: /*#__PURE__*/_jsxDEV(TextField, {\n          fullWidth: true,\n          placeholder: \"Search for topics, people, or posts...\",\n          value: searchQuery,\n          onChange: e => setSearchQuery(e.target.value),\n          InputProps: {\n            startAdornment: /*#__PURE__*/_jsxDEV(InputAdornment, {\n              position: \"start\",\n              children: /*#__PURE__*/_jsxDEV(Search, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 159,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 158,\n              columnNumber: 17\n            }, this)\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 151,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 150,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          borderBottom: 1,\n          borderColor: 'divider',\n          mb: 3\n        },\n        children: /*#__PURE__*/_jsxDEV(Tabs, {\n          value: activeTab,\n          onChange: handleTabChange,\n          children: [/*#__PURE__*/_jsxDEV(Tab, {\n            label: \"Trending\",\n            icon: /*#__PURE__*/_jsxDEV(TrendingUp, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 169,\n              columnNumber: 41\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 169,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Tab, {\n            label: \"People\",\n            icon: /*#__PURE__*/_jsxDEV(People, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 170,\n              columnNumber: 39\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 170,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Tab, {\n            label: \"Topics\",\n            icon: /*#__PURE__*/_jsxDEV(Topic, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 171,\n              columnNumber: 39\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 171,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 168,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 167,\n        columnNumber: 9\n      }, this), activeTab === 0 && /*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: 3,\n        children: [/*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          md: 8,\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            gutterBottom: true,\n            children: \"Popular Posts\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 179,\n            columnNumber: 15\n          }, this), popularPosts.map(post => /*#__PURE__*/_jsxDEV(Card, {\n            sx: {\n              mb: 2\n            },\n            children: /*#__PURE__*/_jsxDEV(CardContent, {\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  alignItems: 'center',\n                  mb: 2\n                },\n                children: [/*#__PURE__*/_jsxDEV(Avatar, {\n                  src: post.authorAvatar,\n                  sx: {\n                    mr: 2\n                  },\n                  children: post.authorName.split(' ').map(n => n[0]).join('')\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 186,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"subtitle1\",\n                  fontWeight: \"bold\",\n                  children: post.authorName\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 189,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 185,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body1\",\n                sx: {\n                  mb: 2\n                },\n                children: post.content\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 193,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  justifyContent: 'space-between',\n                  alignItems: 'center'\n                },\n                children: [/*#__PURE__*/_jsxDEV(Box, {\n                  children: post.tags.map(tag => /*#__PURE__*/_jsxDEV(Chip, {\n                    label: `#${tag}`,\n                    size: \"small\",\n                    sx: {\n                      mr: 1\n                    },\n                    color: \"primary\",\n                    variant: \"outlined\"\n                  }, tag, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 199,\n                    columnNumber: 27\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 197,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    display: 'flex',\n                    gap: 2\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"caption\",\n                    color: \"text.secondary\",\n                    children: [post.likes, \" likes\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 210,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"caption\",\n                    color: \"text.secondary\",\n                    children: [post.comments, \" comments\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 213,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 209,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 196,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 184,\n              columnNumber: 19\n            }, this)\n          }, post.id, false, {\n            fileName: _jsxFileName,\n            lineNumber: 183,\n            columnNumber: 17\n          }, this))]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 178,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          md: 4,\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            gutterBottom: true,\n            children: \"Trending Topics\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 224,\n            columnNumber: 15\n          }, this), trendingTopics.slice(0, 6).map((topic, index) => /*#__PURE__*/_jsxDEV(Card, {\n            sx: {\n              mb: 1\n            },\n            children: /*#__PURE__*/_jsxDEV(CardContent, {\n              sx: {\n                py: 2\n              },\n              children: /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  justifyContent: 'space-between',\n                  alignItems: 'center'\n                },\n                children: /*#__PURE__*/_jsxDEV(Box, {\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    color: \"text.secondary\",\n                    children: [\"#\", index + 1, \" Trending in \", topic.category]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 232,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"subtitle1\",\n                    fontWeight: \"bold\",\n                    children: [\"#\", topic.name]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 235,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"caption\",\n                    color: \"text.secondary\",\n                    children: [formatNumber(topic.postCount), \" posts\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 238,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 231,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 230,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 229,\n              columnNumber: 19\n            }, this)\n          }, topic.id, false, {\n            fileName: _jsxFileName,\n            lineNumber: 228,\n            columnNumber: 17\n          }, this))]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 223,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 177,\n        columnNumber: 11\n      }, this), activeTab === 1 && /*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: 3,\n        children: /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            gutterBottom: true,\n            children: \"Suggested People to Follow\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 254,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            container: true,\n            spacing: 2,\n            children: suggestedUsers.map(user => /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              md: 4,\n              children: /*#__PURE__*/_jsxDEV(Card, {\n                children: /*#__PURE__*/_jsxDEV(CardContent, {\n                  sx: {\n                    textAlign: 'center'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Avatar, {\n                    src: user.avatar,\n                    sx: {\n                      width: 80,\n                      height: 80,\n                      mx: 'auto',\n                      mb: 2\n                    },\n                    children: user.name.split(' ').map(n => n[0]).join('')\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 262,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"h6\",\n                    gutterBottom: true,\n                    children: user.name\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 268,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    color: \"text.secondary\",\n                    sx: {\n                      mb: 2\n                    },\n                    children: user.bio\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 271,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"caption\",\n                    color: \"text.secondary\",\n                    sx: {\n                      mb: 2,\n                      display: 'block'\n                    },\n                    children: [formatNumber(user.followers), \" followers\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 274,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(Button, {\n                    variant: user.isFollowing ? \"outlined\" : \"contained\",\n                    onClick: () => handleFollow(user.id),\n                    fullWidth: true,\n                    children: user.isFollowing ? 'Following' : 'Follow'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 277,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 261,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 260,\n                columnNumber: 21\n              }, this)\n            }, user.id, false, {\n              fileName: _jsxFileName,\n              lineNumber: 259,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 257,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 253,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 252,\n        columnNumber: 11\n      }, this), activeTab === 2 && /*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: 3,\n        children: /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            gutterBottom: true,\n            children: \"Browse by Category\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 297,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            container: true,\n            spacing: 2,\n            children: ['Technology', 'Environment', 'Lifestyle', 'Wellness', 'Finance', 'Education'].map(category => /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              md: 4,\n              children: /*#__PURE__*/_jsxDEV(Card, {\n                sx: {\n                  cursor: 'pointer',\n                  '&:hover': {\n                    elevation: 4\n                  }\n                },\n                children: /*#__PURE__*/_jsxDEV(CardContent, {\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"h6\",\n                    gutterBottom: true,\n                    children: category\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 305,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    color: \"text.secondary\",\n                    children: [trendingTopics.filter(t => t.category === category).length, \" trending topics\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 308,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      mt: 2\n                    },\n                    children: trendingTopics.filter(t => t.category === category).slice(0, 3).map(topic => /*#__PURE__*/_jsxDEV(Chip, {\n                      label: `#${topic.name}`,\n                      size: \"small\",\n                      sx: {\n                        mr: 1,\n                        mb: 1\n                      },\n                      variant: \"outlined\"\n                    }, topic.id, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 316,\n                      columnNumber: 31\n                    }, this))\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 311,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 304,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 303,\n                columnNumber: 21\n              }, this)\n            }, category, false, {\n              fileName: _jsxFileName,\n              lineNumber: 302,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 300,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 296,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 295,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 140,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 139,\n    columnNumber: 5\n  }, this);\n};\n_s(Explore, \"4KbbLGP+jxdhfarGAMSlTYiXMnA=\");\n_c = Explore;\nexport default Explore;\nvar _c;\n$RefreshReg$(_c, \"Explore\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Container", "Grid", "Card", "<PERSON><PERSON><PERSON><PERSON>", "Typography", "<PERSON><PERSON>", "TextField", "Box", "Avatar", "Chip", "Paper", "InputAdornment", "Tab", "Tabs", "Search", "TrendingUp", "People", "Topic", "Explore", "ExploreIcon", "jsxDEV", "_jsxDEV", "_s", "searchQuery", "setSearch<PERSON>uery", "activeTab", "setActiveTab", "trendingTopics", "setTrendingTopics", "suggestedUsers", "setSuggestedUsers", "popularPosts", "setPopularPosts", "id", "name", "postCount", "category", "bio", "followers", "isFollowing", "<PERSON><PERSON><PERSON>", "content", "likes", "comments", "tags", "handleTabChange", "event", "newValue", "handleFollow", "userId", "users", "map", "user", "formatNumber", "num", "toFixed", "toString", "max<PERSON><PERSON><PERSON>", "children", "sx", "py", "variant", "gutterBottom", "display", "alignItems", "gap", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "color", "mb", "p", "fullWidth", "placeholder", "value", "onChange", "e", "target", "InputProps", "startAdornment", "position", "borderBottom", "borderColor", "label", "icon", "container", "spacing", "item", "xs", "md", "post", "src", "<PERSON><PERSON><PERSON><PERSON>", "mr", "split", "n", "join", "fontWeight", "justifyContent", "tag", "size", "slice", "topic", "index", "sm", "textAlign", "avatar", "width", "height", "mx", "onClick", "cursor", "elevation", "filter", "t", "length", "mt", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/ドキュメント/Projects/StudyHub/studyhub-app/src/pages/Explore.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  <PERSON><PERSON><PERSON>,\n  Grid,\n  Card,\n  CardContent,\n  CardMedia,\n  Typography,\n  Button,\n  TextField,\n  Box,\n  Avatar,\n  Chip,\n  Paper,\n  InputAdornment,\n  Tab,\n  Tabs,\n} from '@mui/material';\nimport {\n  Search,\n  TrendingUp,\n  People,\n  Topic,\n  Explore as ExploreIcon,\n} from '@mui/icons-material';\n\ninterface TrendingTopic {\n  id: string;\n  name: string;\n  postCount: number;\n  category: string;\n}\n\ninterface SuggestedUser {\n  id: string;\n  name: string;\n  avatar?: string;\n  bio: string;\n  followers: number;\n  isFollowing: boolean;\n}\n\ninterface PopularPost {\n  id: string;\n  authorName: string;\n  authorAvatar?: string;\n  content: string;\n  likes: number;\n  comments: number;\n  tags: string[];\n}\n\nconst Explore: React.FC = () => {\n  const [searchQuery, setSearchQuery] = useState('');\n  const [activeTab, setActiveTab] = useState(0);\n  const [trendingTopics, setTrendingTopics] = useState<TrendingTopic[]>([]);\n  const [suggestedUsers, setSuggestedUsers] = useState<SuggestedUser[]>([]);\n  const [popularPosts, setPopularPosts] = useState<PopularPost[]>([]);\n\n  useEffect(() => {\n    // Sample trending topics\n    setTrendingTopics([\n      { id: '1', name: 'ClimateChange', postCount: 1247, category: 'Environment' },\n      { id: '2', name: 'AI', postCount: 892, category: 'Technology' },\n      { id: '3', name: 'WorkFromHome', postCount: 634, category: 'Lifestyle' },\n      { id: '4', name: 'MentalHealth', postCount: 567, category: 'Wellness' },\n      { id: '5', name: 'Cryptocurrency', postCount: 445, category: 'Finance' },\n      { id: '6', name: 'SustainableLiving', postCount: 389, category: 'Environment' },\n    ]);\n\n    // Sample suggested users\n    setSuggestedUsers([\n      {\n        id: '1',\n        name: 'Dr. Sarah Chen',\n        bio: 'Climate scientist sharing insights on environmental issues',\n        followers: 15420,\n        isFollowing: false,\n      },\n      {\n        id: '2',\n        name: 'Tech Innovator Mike',\n        bio: 'AI researcher and startup founder',\n        followers: 8930,\n        isFollowing: false,\n      },\n      {\n        id: '3',\n        name: 'Wellness Coach Emma',\n        bio: 'Mental health advocate and mindfulness expert',\n        followers: 12100,\n        isFollowing: false,\n      },\n    ]);\n\n    // Sample popular posts\n    setPopularPosts([\n      {\n        id: '1',\n        authorName: 'Alex Rivera',\n        content: 'The intersection of technology and sustainability is where the future lies. We need more green tech innovations!',\n        likes: 156,\n        comments: 23,\n        tags: ['technology', 'sustainability', 'future'],\n      },\n      {\n        id: '2',\n        authorName: 'Jordan Kim',\n        content: 'Remote work has changed how we think about productivity and work-life balance. What\\'s your experience?',\n        likes: 89,\n        comments: 34,\n        tags: ['remote', 'productivity', 'worklife'],\n      },\n    ]);\n  }, []);\n\n  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {\n    setActiveTab(newValue);\n  };\n\n  const handleFollow = (userId: string) => {\n    setSuggestedUsers(users =>\n      users.map(user =>\n        user.id === userId\n          ? { ...user, isFollowing: !user.isFollowing }\n          : user\n      )\n    );\n  };\n\n  const formatNumber = (num: number) => {\n    if (num >= 1000) {\n      return (num / 1000).toFixed(1) + 'K';\n    }\n    return num.toString();\n  };\n\n  return (\n    <Container maxWidth=\"lg\">\n      <Box sx={{ py: 3 }}>\n        <Typography variant=\"h4\" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\n          <ExploreIcon />\n          Explore\n        </Typography>\n        <Typography variant=\"body1\" color=\"text.secondary\" sx={{ mb: 4 }}>\n          Discover trending topics, connect with new people, and explore popular content\n        </Typography>\n\n        {/* Search Bar */}\n        <Paper sx={{ p: 2, mb: 4 }}>\n          <TextField\n            fullWidth\n            placeholder=\"Search for topics, people, or posts...\"\n            value={searchQuery}\n            onChange={(e) => setSearchQuery(e.target.value)}\n            InputProps={{\n              startAdornment: (\n                <InputAdornment position=\"start\">\n                  <Search />\n                </InputAdornment>\n              ),\n            }}\n          />\n        </Paper>\n\n        {/* Tabs */}\n        <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}>\n          <Tabs value={activeTab} onChange={handleTabChange}>\n            <Tab label=\"Trending\" icon={<TrendingUp />} />\n            <Tab label=\"People\" icon={<People />} />\n            <Tab label=\"Topics\" icon={<Topic />} />\n          </Tabs>\n        </Box>\n\n        {/* Trending Tab */}\n        {activeTab === 0 && (\n          <Grid container spacing={3}>\n            <Grid item xs={12} md={8}>\n              <Typography variant=\"h6\" gutterBottom>\n                Popular Posts\n              </Typography>\n              {popularPosts.map((post) => (\n                <Card key={post.id} sx={{ mb: 2 }}>\n                  <CardContent>\n                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>\n                      <Avatar src={post.authorAvatar} sx={{ mr: 2 }}>\n                        {post.authorName.split(' ').map(n => n[0]).join('')}\n                      </Avatar>\n                      <Typography variant=\"subtitle1\" fontWeight=\"bold\">\n                        {post.authorName}\n                      </Typography>\n                    </Box>\n                    <Typography variant=\"body1\" sx={{ mb: 2 }}>\n                      {post.content}\n                    </Typography>\n                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\n                      <Box>\n                        {post.tags.map((tag) => (\n                          <Chip\n                            key={tag}\n                            label={`#${tag}`}\n                            size=\"small\"\n                            sx={{ mr: 1 }}\n                            color=\"primary\"\n                            variant=\"outlined\"\n                          />\n                        ))}\n                      </Box>\n                      <Box sx={{ display: 'flex', gap: 2 }}>\n                        <Typography variant=\"caption\" color=\"text.secondary\">\n                          {post.likes} likes\n                        </Typography>\n                        <Typography variant=\"caption\" color=\"text.secondary\">\n                          {post.comments} comments\n                        </Typography>\n                      </Box>\n                    </Box>\n                  </CardContent>\n                </Card>\n              ))}\n            </Grid>\n\n            <Grid item xs={12} md={4}>\n              <Typography variant=\"h6\" gutterBottom>\n                Trending Topics\n              </Typography>\n              {trendingTopics.slice(0, 6).map((topic, index) => (\n                <Card key={topic.id} sx={{ mb: 1 }}>\n                  <CardContent sx={{ py: 2 }}>\n                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\n                      <Box>\n                        <Typography variant=\"body2\" color=\"text.secondary\">\n                          #{index + 1} Trending in {topic.category}\n                        </Typography>\n                        <Typography variant=\"subtitle1\" fontWeight=\"bold\">\n                          #{topic.name}\n                        </Typography>\n                        <Typography variant=\"caption\" color=\"text.secondary\">\n                          {formatNumber(topic.postCount)} posts\n                        </Typography>\n                      </Box>\n                    </Box>\n                  </CardContent>\n                </Card>\n              ))}\n            </Grid>\n          </Grid>\n        )}\n\n        {/* People Tab */}\n        {activeTab === 1 && (\n          <Grid container spacing={3}>\n            <Grid item xs={12}>\n              <Typography variant=\"h6\" gutterBottom>\n                Suggested People to Follow\n              </Typography>\n              <Grid container spacing={2}>\n                {suggestedUsers.map((user) => (\n                  <Grid item xs={12} sm={6} md={4} key={user.id}>\n                    <Card>\n                      <CardContent sx={{ textAlign: 'center' }}>\n                        <Avatar\n                          src={user.avatar}\n                          sx={{ width: 80, height: 80, mx: 'auto', mb: 2 }}\n                        >\n                          {user.name.split(' ').map(n => n[0]).join('')}\n                        </Avatar>\n                        <Typography variant=\"h6\" gutterBottom>\n                          {user.name}\n                        </Typography>\n                        <Typography variant=\"body2\" color=\"text.secondary\" sx={{ mb: 2 }}>\n                          {user.bio}\n                        </Typography>\n                        <Typography variant=\"caption\" color=\"text.secondary\" sx={{ mb: 2, display: 'block' }}>\n                          {formatNumber(user.followers)} followers\n                        </Typography>\n                        <Button\n                          variant={user.isFollowing ? \"outlined\" : \"contained\"}\n                          onClick={() => handleFollow(user.id)}\n                          fullWidth\n                        >\n                          {user.isFollowing ? 'Following' : 'Follow'}\n                        </Button>\n                      </CardContent>\n                    </Card>\n                  </Grid>\n                ))}\n              </Grid>\n            </Grid>\n          </Grid>\n        )}\n\n        {/* Topics Tab */}\n        {activeTab === 2 && (\n          <Grid container spacing={3}>\n            <Grid item xs={12}>\n              <Typography variant=\"h6\" gutterBottom>\n                Browse by Category\n              </Typography>\n              <Grid container spacing={2}>\n                {['Technology', 'Environment', 'Lifestyle', 'Wellness', 'Finance', 'Education'].map((category) => (\n                  <Grid item xs={12} sm={6} md={4} key={category}>\n                    <Card sx={{ cursor: 'pointer', '&:hover': { elevation: 4 } }}>\n                      <CardContent>\n                        <Typography variant=\"h6\" gutterBottom>\n                          {category}\n                        </Typography>\n                        <Typography variant=\"body2\" color=\"text.secondary\">\n                          {trendingTopics.filter(t => t.category === category).length} trending topics\n                        </Typography>\n                        <Box sx={{ mt: 2 }}>\n                          {trendingTopics\n                            .filter(t => t.category === category)\n                            .slice(0, 3)\n                            .map((topic) => (\n                              <Chip\n                                key={topic.id}\n                                label={`#${topic.name}`}\n                                size=\"small\"\n                                sx={{ mr: 1, mb: 1 }}\n                                variant=\"outlined\"\n                              />\n                            ))}\n                        </Box>\n                      </CardContent>\n                    </Card>\n                  </Grid>\n                ))}\n              </Grid>\n            </Grid>\n          </Grid>\n        )}\n      </Box>\n    </Container>\n  );\n};\n\nexport default Explore;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,SAAS,EACTC,IAAI,EACJC,IAAI,EACJC,WAAW,EAEXC,UAAU,EACVC,MAAM,EACNC,SAAS,EACTC,GAAG,EACHC,MAAM,EACNC,IAAI,EACJC,KAAK,EACLC,cAAc,EACdC,GAAG,EACHC,IAAI,QACC,eAAe;AACtB,SACEC,MAAM,EACNC,UAAU,EACVC,MAAM,EACNC,KAAK,EACLC,OAAO,IAAIC,WAAW,QACjB,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AA4B7B,MAAMH,OAAiB,GAAGA,CAAA,KAAM;EAAAI,EAAA;EAC9B,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAG1B,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAAC2B,SAAS,EAAEC,YAAY,CAAC,GAAG5B,QAAQ,CAAC,CAAC,CAAC;EAC7C,MAAM,CAAC6B,cAAc,EAAEC,iBAAiB,CAAC,GAAG9B,QAAQ,CAAkB,EAAE,CAAC;EACzE,MAAM,CAAC+B,cAAc,EAAEC,iBAAiB,CAAC,GAAGhC,QAAQ,CAAkB,EAAE,CAAC;EACzE,MAAM,CAACiC,YAAY,EAAEC,eAAe,CAAC,GAAGlC,QAAQ,CAAgB,EAAE,CAAC;EAEnEC,SAAS,CAAC,MAAM;IACd;IACA6B,iBAAiB,CAAC,CAChB;MAAEK,EAAE,EAAE,GAAG;MAAEC,IAAI,EAAE,eAAe;MAAEC,SAAS,EAAE,IAAI;MAAEC,QAAQ,EAAE;IAAc,CAAC,EAC5E;MAAEH,EAAE,EAAE,GAAG;MAAEC,IAAI,EAAE,IAAI;MAAEC,SAAS,EAAE,GAAG;MAAEC,QAAQ,EAAE;IAAa,CAAC,EAC/D;MAAEH,EAAE,EAAE,GAAG;MAAEC,IAAI,EAAE,cAAc;MAAEC,SAAS,EAAE,GAAG;MAAEC,QAAQ,EAAE;IAAY,CAAC,EACxE;MAAEH,EAAE,EAAE,GAAG;MAAEC,IAAI,EAAE,cAAc;MAAEC,SAAS,EAAE,GAAG;MAAEC,QAAQ,EAAE;IAAW,CAAC,EACvE;MAAEH,EAAE,EAAE,GAAG;MAAEC,IAAI,EAAE,gBAAgB;MAAEC,SAAS,EAAE,GAAG;MAAEC,QAAQ,EAAE;IAAU,CAAC,EACxE;MAAEH,EAAE,EAAE,GAAG;MAAEC,IAAI,EAAE,mBAAmB;MAAEC,SAAS,EAAE,GAAG;MAAEC,QAAQ,EAAE;IAAc,CAAC,CAChF,CAAC;;IAEF;IACAN,iBAAiB,CAAC,CAChB;MACEG,EAAE,EAAE,GAAG;MACPC,IAAI,EAAE,gBAAgB;MACtBG,GAAG,EAAE,4DAA4D;MACjEC,SAAS,EAAE,KAAK;MAChBC,WAAW,EAAE;IACf,CAAC,EACD;MACEN,EAAE,EAAE,GAAG;MACPC,IAAI,EAAE,qBAAqB;MAC3BG,GAAG,EAAE,mCAAmC;MACxCC,SAAS,EAAE,IAAI;MACfC,WAAW,EAAE;IACf,CAAC,EACD;MACEN,EAAE,EAAE,GAAG;MACPC,IAAI,EAAE,qBAAqB;MAC3BG,GAAG,EAAE,+CAA+C;MACpDC,SAAS,EAAE,KAAK;MAChBC,WAAW,EAAE;IACf,CAAC,CACF,CAAC;;IAEF;IACAP,eAAe,CAAC,CACd;MACEC,EAAE,EAAE,GAAG;MACPO,UAAU,EAAE,aAAa;MACzBC,OAAO,EAAE,kHAAkH;MAC3HC,KAAK,EAAE,GAAG;MACVC,QAAQ,EAAE,EAAE;MACZC,IAAI,EAAE,CAAC,YAAY,EAAE,gBAAgB,EAAE,QAAQ;IACjD,CAAC,EACD;MACEX,EAAE,EAAE,GAAG;MACPO,UAAU,EAAE,YAAY;MACxBC,OAAO,EAAE,yGAAyG;MAClHC,KAAK,EAAE,EAAE;MACTC,QAAQ,EAAE,EAAE;MACZC,IAAI,EAAE,CAAC,QAAQ,EAAE,cAAc,EAAE,UAAU;IAC7C,CAAC,CACF,CAAC;EACJ,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMC,eAAe,GAAGA,CAACC,KAA2B,EAAEC,QAAgB,KAAK;IACzErB,YAAY,CAACqB,QAAQ,CAAC;EACxB,CAAC;EAED,MAAMC,YAAY,GAAIC,MAAc,IAAK;IACvCnB,iBAAiB,CAACoB,KAAK,IACrBA,KAAK,CAACC,GAAG,CAACC,IAAI,IACZA,IAAI,CAACnB,EAAE,KAAKgB,MAAM,GACd;MAAE,GAAGG,IAAI;MAAEb,WAAW,EAAE,CAACa,IAAI,CAACb;IAAY,CAAC,GAC3Ca,IACN,CACF,CAAC;EACH,CAAC;EAED,MAAMC,YAAY,GAAIC,GAAW,IAAK;IACpC,IAAIA,GAAG,IAAI,IAAI,EAAE;MACf,OAAO,CAACA,GAAG,GAAG,IAAI,EAAEC,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;IACtC;IACA,OAAOD,GAAG,CAACE,QAAQ,CAAC,CAAC;EACvB,CAAC;EAED,oBACEnC,OAAA,CAACrB,SAAS;IAACyD,QAAQ,EAAC,IAAI;IAAAC,QAAA,eACtBrC,OAAA,CAACd,GAAG;MAACoD,EAAE,EAAE;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAF,QAAA,gBACjBrC,OAAA,CAACjB,UAAU;QAACyD,OAAO,EAAC,IAAI;QAACC,YAAY;QAACH,EAAE,EAAE;UAAEI,OAAO,EAAE,MAAM;UAAEC,UAAU,EAAE,QAAQ;UAAEC,GAAG,EAAE;QAAE,CAAE;QAAAP,QAAA,gBAC1FrC,OAAA,CAACF,WAAW;UAAA+C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,WAEjB;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACbhD,OAAA,CAACjB,UAAU;QAACyD,OAAO,EAAC,OAAO;QAACS,KAAK,EAAC,gBAAgB;QAACX,EAAE,EAAE;UAAEY,EAAE,EAAE;QAAE,CAAE;QAAAb,QAAA,EAAC;MAElE;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAGbhD,OAAA,CAACX,KAAK;QAACiD,EAAE,EAAE;UAAEa,CAAC,EAAE,CAAC;UAAED,EAAE,EAAE;QAAE,CAAE;QAAAb,QAAA,eACzBrC,OAAA,CAACf,SAAS;UACRmE,SAAS;UACTC,WAAW,EAAC,wCAAwC;UACpDC,KAAK,EAAEpD,WAAY;UACnBqD,QAAQ,EAAGC,CAAC,IAAKrD,cAAc,CAACqD,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;UAChDI,UAAU,EAAE;YACVC,cAAc,eACZ3D,OAAA,CAACV,cAAc;cAACsE,QAAQ,EAAC,OAAO;cAAAvB,QAAA,eAC9BrC,OAAA,CAACP,MAAM;gBAAAoD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI;UAEpB;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC,eAGRhD,OAAA,CAACd,GAAG;QAACoD,EAAE,EAAE;UAAEuB,YAAY,EAAE,CAAC;UAAEC,WAAW,EAAE,SAAS;UAAEZ,EAAE,EAAE;QAAE,CAAE;QAAAb,QAAA,eAC1DrC,OAAA,CAACR,IAAI;UAAC8D,KAAK,EAAElD,SAAU;UAACmD,QAAQ,EAAE/B,eAAgB;UAAAa,QAAA,gBAChDrC,OAAA,CAACT,GAAG;YAACwE,KAAK,EAAC,UAAU;YAACC,IAAI,eAAEhE,OAAA,CAACN,UAAU;cAAAmD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC9ChD,OAAA,CAACT,GAAG;YAACwE,KAAK,EAAC,QAAQ;YAACC,IAAI,eAAEhE,OAAA,CAACL,MAAM;cAAAkD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACxChD,OAAA,CAACT,GAAG;YAACwE,KAAK,EAAC,QAAQ;YAACC,IAAI,eAAEhE,OAAA,CAACJ,KAAK;cAAAiD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,EAGL5C,SAAS,KAAK,CAAC,iBACdJ,OAAA,CAACpB,IAAI;QAACqF,SAAS;QAACC,OAAO,EAAE,CAAE;QAAA7B,QAAA,gBACzBrC,OAAA,CAACpB,IAAI;UAACuF,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAAhC,QAAA,gBACvBrC,OAAA,CAACjB,UAAU;YAACyD,OAAO,EAAC,IAAI;YAACC,YAAY;YAAAJ,QAAA,EAAC;UAEtC;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,EACZtC,YAAY,CAACoB,GAAG,CAAEwC,IAAI,iBACrBtE,OAAA,CAACnB,IAAI;YAAeyD,EAAE,EAAE;cAAEY,EAAE,EAAE;YAAE,CAAE;YAAAb,QAAA,eAChCrC,OAAA,CAAClB,WAAW;cAAAuD,QAAA,gBACVrC,OAAA,CAACd,GAAG;gBAACoD,EAAE,EAAE;kBAAEI,OAAO,EAAE,MAAM;kBAAEC,UAAU,EAAE,QAAQ;kBAAEO,EAAE,EAAE;gBAAE,CAAE;gBAAAb,QAAA,gBACxDrC,OAAA,CAACb,MAAM;kBAACoF,GAAG,EAAED,IAAI,CAACE,YAAa;kBAAClC,EAAE,EAAE;oBAAEmC,EAAE,EAAE;kBAAE,CAAE;kBAAApC,QAAA,EAC3CiC,IAAI,CAACnD,UAAU,CAACuD,KAAK,CAAC,GAAG,CAAC,CAAC5C,GAAG,CAAC6C,CAAC,IAAIA,CAAC,CAAC,CAAC,CAAC,CAAC,CAACC,IAAI,CAAC,EAAE;gBAAC;kBAAA/B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7C,CAAC,eACThD,OAAA,CAACjB,UAAU;kBAACyD,OAAO,EAAC,WAAW;kBAACqC,UAAU,EAAC,MAAM;kBAAAxC,QAAA,EAC9CiC,IAAI,CAACnD;gBAAU;kBAAA0B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC,eACNhD,OAAA,CAACjB,UAAU;gBAACyD,OAAO,EAAC,OAAO;gBAACF,EAAE,EAAE;kBAAEY,EAAE,EAAE;gBAAE,CAAE;gBAAAb,QAAA,EACvCiC,IAAI,CAAClD;cAAO;gBAAAyB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACbhD,OAAA,CAACd,GAAG;gBAACoD,EAAE,EAAE;kBAAEI,OAAO,EAAE,MAAM;kBAAEoC,cAAc,EAAE,eAAe;kBAAEnC,UAAU,EAAE;gBAAS,CAAE;gBAAAN,QAAA,gBAClFrC,OAAA,CAACd,GAAG;kBAAAmD,QAAA,EACDiC,IAAI,CAAC/C,IAAI,CAACO,GAAG,CAAEiD,GAAG,iBACjB/E,OAAA,CAACZ,IAAI;oBAEH2E,KAAK,EAAE,IAAIgB,GAAG,EAAG;oBACjBC,IAAI,EAAC,OAAO;oBACZ1C,EAAE,EAAE;sBAAEmC,EAAE,EAAE;oBAAE,CAAE;oBACdxB,KAAK,EAAC,SAAS;oBACfT,OAAO,EAAC;kBAAU,GALbuC,GAAG;oBAAAlC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAMT,CACF;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eACNhD,OAAA,CAACd,GAAG;kBAACoD,EAAE,EAAE;oBAAEI,OAAO,EAAE,MAAM;oBAAEE,GAAG,EAAE;kBAAE,CAAE;kBAAAP,QAAA,gBACnCrC,OAAA,CAACjB,UAAU;oBAACyD,OAAO,EAAC,SAAS;oBAACS,KAAK,EAAC,gBAAgB;oBAAAZ,QAAA,GACjDiC,IAAI,CAACjD,KAAK,EAAC,QACd;kBAAA;oBAAAwB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACbhD,OAAA,CAACjB,UAAU;oBAACyD,OAAO,EAAC,SAAS;oBAACS,KAAK,EAAC,gBAAgB;oBAAAZ,QAAA,GACjDiC,IAAI,CAAChD,QAAQ,EAAC,WACjB;kBAAA;oBAAAuB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK;UAAC,GAnCLsB,IAAI,CAAC1D,EAAE;YAAAiC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAoCZ,CACP,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAEPhD,OAAA,CAACpB,IAAI;UAACuF,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAAhC,QAAA,gBACvBrC,OAAA,CAACjB,UAAU;YAACyD,OAAO,EAAC,IAAI;YAACC,YAAY;YAAAJ,QAAA,EAAC;UAEtC;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,EACZ1C,cAAc,CAAC2E,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACnD,GAAG,CAAC,CAACoD,KAAK,EAAEC,KAAK,kBAC3CnF,OAAA,CAACnB,IAAI;YAAgByD,EAAE,EAAE;cAAEY,EAAE,EAAE;YAAE,CAAE;YAAAb,QAAA,eACjCrC,OAAA,CAAClB,WAAW;cAACwD,EAAE,EAAE;gBAAEC,EAAE,EAAE;cAAE,CAAE;cAAAF,QAAA,eACzBrC,OAAA,CAACd,GAAG;gBAACoD,EAAE,EAAE;kBAAEI,OAAO,EAAE,MAAM;kBAAEoC,cAAc,EAAE,eAAe;kBAAEnC,UAAU,EAAE;gBAAS,CAAE;gBAAAN,QAAA,eAClFrC,OAAA,CAACd,GAAG;kBAAAmD,QAAA,gBACFrC,OAAA,CAACjB,UAAU;oBAACyD,OAAO,EAAC,OAAO;oBAACS,KAAK,EAAC,gBAAgB;oBAAAZ,QAAA,GAAC,GAChD,EAAC8C,KAAK,GAAG,CAAC,EAAC,eAAa,EAACD,KAAK,CAACnE,QAAQ;kBAAA;oBAAA8B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9B,CAAC,eACbhD,OAAA,CAACjB,UAAU;oBAACyD,OAAO,EAAC,WAAW;oBAACqC,UAAU,EAAC,MAAM;oBAAAxC,QAAA,GAAC,GAC/C,EAAC6C,KAAK,CAACrE,IAAI;kBAAA;oBAAAgC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CAAC,eACbhD,OAAA,CAACjB,UAAU;oBAACyD,OAAO,EAAC,SAAS;oBAACS,KAAK,EAAC,gBAAgB;oBAAAZ,QAAA,GACjDL,YAAY,CAACkD,KAAK,CAACpE,SAAS,CAAC,EAAC,QACjC;kBAAA;oBAAA+B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK;UAAC,GAfLkC,KAAK,CAACtE,EAAE;YAAAiC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAgBb,CACP,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACP,EAGA5C,SAAS,KAAK,CAAC,iBACdJ,OAAA,CAACpB,IAAI;QAACqF,SAAS;QAACC,OAAO,EAAE,CAAE;QAAA7B,QAAA,eACzBrC,OAAA,CAACpB,IAAI;UAACuF,IAAI;UAACC,EAAE,EAAE,EAAG;UAAA/B,QAAA,gBAChBrC,OAAA,CAACjB,UAAU;YAACyD,OAAO,EAAC,IAAI;YAACC,YAAY;YAAAJ,QAAA,EAAC;UAEtC;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbhD,OAAA,CAACpB,IAAI;YAACqF,SAAS;YAACC,OAAO,EAAE,CAAE;YAAA7B,QAAA,EACxB7B,cAAc,CAACsB,GAAG,CAAEC,IAAI,iBACvB/B,OAAA,CAACpB,IAAI;cAACuF,IAAI;cAACC,EAAE,EAAE,EAAG;cAACgB,EAAE,EAAE,CAAE;cAACf,EAAE,EAAE,CAAE;cAAAhC,QAAA,eAC9BrC,OAAA,CAACnB,IAAI;gBAAAwD,QAAA,eACHrC,OAAA,CAAClB,WAAW;kBAACwD,EAAE,EAAE;oBAAE+C,SAAS,EAAE;kBAAS,CAAE;kBAAAhD,QAAA,gBACvCrC,OAAA,CAACb,MAAM;oBACLoF,GAAG,EAAExC,IAAI,CAACuD,MAAO;oBACjBhD,EAAE,EAAE;sBAAEiD,KAAK,EAAE,EAAE;sBAAEC,MAAM,EAAE,EAAE;sBAAEC,EAAE,EAAE,MAAM;sBAAEvC,EAAE,EAAE;oBAAE,CAAE;oBAAAb,QAAA,EAEhDN,IAAI,CAAClB,IAAI,CAAC6D,KAAK,CAAC,GAAG,CAAC,CAAC5C,GAAG,CAAC6C,CAAC,IAAIA,CAAC,CAAC,CAAC,CAAC,CAAC,CAACC,IAAI,CAAC,EAAE;kBAAC;oBAAA/B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvC,CAAC,eACThD,OAAA,CAACjB,UAAU;oBAACyD,OAAO,EAAC,IAAI;oBAACC,YAAY;oBAAAJ,QAAA,EAClCN,IAAI,CAAClB;kBAAI;oBAAAgC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACA,CAAC,eACbhD,OAAA,CAACjB,UAAU;oBAACyD,OAAO,EAAC,OAAO;oBAACS,KAAK,EAAC,gBAAgB;oBAACX,EAAE,EAAE;sBAAEY,EAAE,EAAE;oBAAE,CAAE;oBAAAb,QAAA,EAC9DN,IAAI,CAACf;kBAAG;oBAAA6B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC,eACbhD,OAAA,CAACjB,UAAU;oBAACyD,OAAO,EAAC,SAAS;oBAACS,KAAK,EAAC,gBAAgB;oBAACX,EAAE,EAAE;sBAAEY,EAAE,EAAE,CAAC;sBAAER,OAAO,EAAE;oBAAQ,CAAE;oBAAAL,QAAA,GAClFL,YAAY,CAACD,IAAI,CAACd,SAAS,CAAC,EAAC,YAChC;kBAAA;oBAAA4B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACbhD,OAAA,CAAChB,MAAM;oBACLwD,OAAO,EAAET,IAAI,CAACb,WAAW,GAAG,UAAU,GAAG,WAAY;oBACrDwE,OAAO,EAAEA,CAAA,KAAM/D,YAAY,CAACI,IAAI,CAACnB,EAAE,CAAE;oBACrCwC,SAAS;oBAAAf,QAAA,EAERN,IAAI,CAACb,WAAW,GAAG,WAAW,GAAG;kBAAQ;oBAAA2B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV;YAAC,GA1B6BjB,IAAI,CAACnB,EAAE;cAAAiC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OA2BvC,CACP;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACP,EAGA5C,SAAS,KAAK,CAAC,iBACdJ,OAAA,CAACpB,IAAI;QAACqF,SAAS;QAACC,OAAO,EAAE,CAAE;QAAA7B,QAAA,eACzBrC,OAAA,CAACpB,IAAI;UAACuF,IAAI;UAACC,EAAE,EAAE,EAAG;UAAA/B,QAAA,gBAChBrC,OAAA,CAACjB,UAAU;YAACyD,OAAO,EAAC,IAAI;YAACC,YAAY;YAAAJ,QAAA,EAAC;UAEtC;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbhD,OAAA,CAACpB,IAAI;YAACqF,SAAS;YAACC,OAAO,EAAE,CAAE;YAAA7B,QAAA,EACxB,CAAC,YAAY,EAAE,aAAa,EAAE,WAAW,EAAE,UAAU,EAAE,SAAS,EAAE,WAAW,CAAC,CAACP,GAAG,CAAEf,QAAQ,iBAC3Ff,OAAA,CAACpB,IAAI;cAACuF,IAAI;cAACC,EAAE,EAAE,EAAG;cAACgB,EAAE,EAAE,CAAE;cAACf,EAAE,EAAE,CAAE;cAAAhC,QAAA,eAC9BrC,OAAA,CAACnB,IAAI;gBAACyD,EAAE,EAAE;kBAAEqD,MAAM,EAAE,SAAS;kBAAE,SAAS,EAAE;oBAAEC,SAAS,EAAE;kBAAE;gBAAE,CAAE;gBAAAvD,QAAA,eAC3DrC,OAAA,CAAClB,WAAW;kBAAAuD,QAAA,gBACVrC,OAAA,CAACjB,UAAU;oBAACyD,OAAO,EAAC,IAAI;oBAACC,YAAY;oBAAAJ,QAAA,EAClCtB;kBAAQ;oBAAA8B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC,eACbhD,OAAA,CAACjB,UAAU;oBAACyD,OAAO,EAAC,OAAO;oBAACS,KAAK,EAAC,gBAAgB;oBAAAZ,QAAA,GAC/C/B,cAAc,CAACuF,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAC/E,QAAQ,KAAKA,QAAQ,CAAC,CAACgF,MAAM,EAAC,kBAC9D;kBAAA;oBAAAlD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACbhD,OAAA,CAACd,GAAG;oBAACoD,EAAE,EAAE;sBAAE0D,EAAE,EAAE;oBAAE,CAAE;oBAAA3D,QAAA,EAChB/B,cAAc,CACZuF,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAC/E,QAAQ,KAAKA,QAAQ,CAAC,CACpCkE,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CACXnD,GAAG,CAAEoD,KAAK,iBACTlF,OAAA,CAACZ,IAAI;sBAEH2E,KAAK,EAAE,IAAImB,KAAK,CAACrE,IAAI,EAAG;sBACxBmE,IAAI,EAAC,OAAO;sBACZ1C,EAAE,EAAE;wBAAEmC,EAAE,EAAE,CAAC;wBAAEvB,EAAE,EAAE;sBAAE,CAAE;sBACrBV,OAAO,EAAC;oBAAU,GAJb0C,KAAK,CAACtE,EAAE;sBAAAiC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAKd,CACF;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACK;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV;YAAC,GAxB6BjC,QAAQ;cAAA8B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAyBxC,CACP;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACP;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACG,CAAC;AAEhB,CAAC;AAAC/C,EAAA,CA3RIJ,OAAiB;AAAAoG,EAAA,GAAjBpG,OAAiB;AA6RvB,eAAeA,OAAO;AAAC,IAAAoG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}