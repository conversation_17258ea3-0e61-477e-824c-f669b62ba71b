export interface StudyGroup {
  id: string;
  name: string;
  description: string;
  subject: string;
  category: string;
  maxMembers: number;
  currentMembers: number;
  isPrivate: boolean;
  createdBy: string;
  createdAt: any;
  updatedAt: any;
  members: StudyGroupMember[];
  tags: string[];
  meetingSchedule?: {
    day: string;
    time: string;
    timezone: string;
    recurring: boolean;
  };
  resources: string[]; // Array of resource IDs
  chatId: string; // Reference to chat collection
}

export interface StudyGroupMember {
  uid: string;
  displayName: string;
  email: string;
  profilePicture?: string;
  role: 'admin' | 'moderator' | 'member';
  joinedAt: any;
  isActive: boolean;
}

export interface StudyGroupInvitation {
  id: string;
  groupId: string;
  groupName: string;
  invitedBy: string;
  invitedByName: string;
  invitedUser: string;
  invitedUserEmail: string;
  status: 'pending' | 'accepted' | 'declined';
  createdAt: any;
  expiresAt: any;
}

export interface StudyGroupJoinRequest {
  id: string;
  groupId: string;
  groupName: string;
  requestedBy: string;
  requestedByName: string;
  requestedByEmail: string;
  message?: string;
  status: 'pending' | 'approved' | 'rejected';
  createdAt: any;
  reviewedBy?: string;
  reviewedAt?: any;
}

export interface CreateStudyGroupData {
  name: string;
  description: string;
  subject: string;
  category: string;
  maxMembers: number;
  isPrivate: boolean;
  tags: string[];
  meetingSchedule?: {
    day: string;
    time: string;
    timezone: string;
    recurring: boolean;
  };
}
