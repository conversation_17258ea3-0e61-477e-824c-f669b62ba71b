{"ast": null, "code": "import _objectSpread from \"C:/Users/<USER>/OneDrive/\\u30C9\\u30AD\\u30E5\\u30E1\\u30F3\\u30C8/Projects/StudyHub/studyhub-app/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nimport _objectWithoutProperties from \"C:/Users/<USER>/OneDrive/\\u30C9\\u30AD\\u30E5\\u30E1\\u30F3\\u30C8/Projects/StudyHub/studyhub-app/node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js\";\nconst _excluded = [\"sx\"];\nimport { isPlainObject } from '@mui/utils/deepmerge';\nimport defaultSxConfig from \"./defaultSxConfig.js\";\nconst splitProps = props => {\n  var _props$theme$unstable, _props$theme;\n  const result = {\n    systemProps: {},\n    otherProps: {}\n  };\n  const config = (_props$theme$unstable = props === null || props === void 0 || (_props$theme = props.theme) === null || _props$theme === void 0 ? void 0 : _props$theme.unstable_sxConfig) !== null && _props$theme$unstable !== void 0 ? _props$theme$unstable : defaultSxConfig;\n  Object.keys(props).forEach(prop => {\n    if (config[prop]) {\n      result.systemProps[prop] = props[prop];\n    } else {\n      result.otherProps[prop] = props[prop];\n    }\n  });\n  return result;\n};\nexport default function extendSxProp(props) {\n  const {\n      sx: inSx\n    } = props,\n    other = _objectWithoutProperties(props, _excluded);\n  const {\n    systemProps,\n    otherProps\n  } = splitProps(other);\n  let finalSx;\n  if (Array.isArray(inSx)) {\n    finalSx = [systemProps, ...inSx];\n  } else if (typeof inSx === 'function') {\n    finalSx = function () {\n      const result = inSx(...arguments);\n      if (!isPlainObject(result)) {\n        return systemProps;\n      }\n      return _objectSpread(_objectSpread({}, systemProps), result);\n    };\n  } else {\n    finalSx = _objectSpread(_objectSpread({}, systemProps), inSx);\n  }\n  return _objectSpread(_objectSpread({}, otherProps), {}, {\n    sx: finalSx\n  });\n}", "map": {"version": 3, "names": ["isPlainObject", "defaultSxConfig", "splitProps", "props", "_props$theme$unstable", "_props$theme", "result", "systemProps", "otherProps", "config", "theme", "unstable_sxConfig", "Object", "keys", "for<PERSON>ach", "prop", "extendSxProp", "sx", "inSx", "other", "_objectWithoutProperties", "_excluded", "finalSx", "Array", "isArray", "arguments", "_objectSpread"], "sources": ["C:/Users/<USER>/OneDrive/ドキュメント/Projects/StudyHub/studyhub-app/node_modules/@mui/system/esm/styleFunctionSx/extendSxProp.js"], "sourcesContent": ["import { isPlainObject } from '@mui/utils/deepmerge';\nimport defaultSxConfig from \"./defaultSxConfig.js\";\nconst splitProps = props => {\n  const result = {\n    systemProps: {},\n    otherProps: {}\n  };\n  const config = props?.theme?.unstable_sxConfig ?? defaultSxConfig;\n  Object.keys(props).forEach(prop => {\n    if (config[prop]) {\n      result.systemProps[prop] = props[prop];\n    } else {\n      result.otherProps[prop] = props[prop];\n    }\n  });\n  return result;\n};\nexport default function extendSxProp(props) {\n  const {\n    sx: inSx,\n    ...other\n  } = props;\n  const {\n    systemProps,\n    otherProps\n  } = splitProps(other);\n  let finalSx;\n  if (Array.isArray(inSx)) {\n    finalSx = [systemProps, ...inSx];\n  } else if (typeof inSx === 'function') {\n    finalSx = (...args) => {\n      const result = inSx(...args);\n      if (!isPlainObject(result)) {\n        return systemProps;\n      }\n      return {\n        ...systemProps,\n        ...result\n      };\n    };\n  } else {\n    finalSx = {\n      ...systemProps,\n      ...inSx\n    };\n  }\n  return {\n    ...otherProps,\n    sx: finalSx\n  };\n}"], "mappings": ";;;AAAA,SAASA,aAAa,QAAQ,sBAAsB;AACpD,OAAOC,eAAe,MAAM,sBAAsB;AAClD,MAAMC,UAAU,GAAGC,KAAK,IAAI;EAAA,IAAAC,qBAAA,EAAAC,YAAA;EAC1B,MAAMC,MAAM,GAAG;IACbC,WAAW,EAAE,CAAC,CAAC;IACfC,UAAU,EAAE,CAAC;EACf,CAAC;EACD,MAAMC,MAAM,IAAAL,qBAAA,GAAGD,KAAK,aAALA,KAAK,gBAAAE,YAAA,GAALF,KAAK,CAAEO,KAAK,cAAAL,YAAA,uBAAZA,YAAA,CAAcM,iBAAiB,cAAAP,qBAAA,cAAAA,qBAAA,GAAIH,eAAe;EACjEW,MAAM,CAACC,IAAI,CAACV,KAAK,CAAC,CAACW,OAAO,CAACC,IAAI,IAAI;IACjC,IAAIN,MAAM,CAACM,IAAI,CAAC,EAAE;MAChBT,MAAM,CAACC,WAAW,CAACQ,IAAI,CAAC,GAAGZ,KAAK,CAACY,IAAI,CAAC;IACxC,CAAC,MAAM;MACLT,MAAM,CAACE,UAAU,CAACO,IAAI,CAAC,GAAGZ,KAAK,CAACY,IAAI,CAAC;IACvC;EACF,CAAC,CAAC;EACF,OAAOT,MAAM;AACf,CAAC;AACD,eAAe,SAASU,YAAYA,CAACb,KAAK,EAAE;EAC1C,MAAM;MACJc,EAAE,EAAEC;IAEN,CAAC,GAAGf,KAAK;IADJgB,KAAK,GAAAC,wBAAA,CACNjB,KAAK,EAAAkB,SAAA;EACT,MAAM;IACJd,WAAW;IACXC;EACF,CAAC,GAAGN,UAAU,CAACiB,KAAK,CAAC;EACrB,IAAIG,OAAO;EACX,IAAIC,KAAK,CAACC,OAAO,CAACN,IAAI,CAAC,EAAE;IACvBI,OAAO,GAAG,CAACf,WAAW,EAAE,GAAGW,IAAI,CAAC;EAClC,CAAC,MAAM,IAAI,OAAOA,IAAI,KAAK,UAAU,EAAE;IACrCI,OAAO,GAAG,SAAAA,CAAA,EAAa;MACrB,MAAMhB,MAAM,GAAGY,IAAI,CAAC,GAAAO,SAAO,CAAC;MAC5B,IAAI,CAACzB,aAAa,CAACM,MAAM,CAAC,EAAE;QAC1B,OAAOC,WAAW;MACpB;MACA,OAAAmB,aAAA,CAAAA,aAAA,KACKnB,WAAW,GACXD,MAAM;IAEb,CAAC;EACH,CAAC,MAAM;IACLgB,OAAO,GAAAI,aAAA,CAAAA,aAAA,KACFnB,WAAW,GACXW,IAAI,CACR;EACH;EACA,OAAAQ,aAAA,CAAAA,aAAA,KACKlB,UAAU;IACbS,EAAE,EAAEK;EAAO;AAEf", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}