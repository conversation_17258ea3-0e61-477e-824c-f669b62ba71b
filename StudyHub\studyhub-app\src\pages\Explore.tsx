import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>rid,
  <PERSON>,
  CardContent,
  <PERSON><PERSON><PERSON>,
  Button,
  TextField,
  Box,
  Avatar,
  Chip,
  Paper,
  InputAdornment,
  Tab,
  Tabs,
} from '@mui/material';
import {
  Search,
  TrendingUp,
  People,
  Topic,
  Explore as ExploreIcon,
} from '@mui/icons-material';

interface TrendingTopic {
  id: string;
  name: string;
  postCount: number;
  category: string;
}

interface SuggestedUser {
  id: string;
  name: string;
  avatar?: string;
  bio: string;
  followers: number;
  isFollowing: boolean;
}

interface PopularPost {
  id: string;
  authorName: string;
  authorAvatar?: string;
  content: string;
  likes: number;
  comments: number;
  tags: string[];
}

const Explore: React.FC = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [activeTab, setActiveTab] = useState(0);
  const [trendingTopics, setTrendingTopics] = useState<TrendingTopic[]>([]);
  const [suggestedUsers, setSuggestedUsers] = useState<SuggestedUser[]>([]);
  const [popularPosts, setPopularPosts] = useState<PopularPost[]>([]);

  useEffect(() => {
    // Sample trending topics
    setTrendingTopics([
      { id: '1', name: 'ClimateChange', postCount: 1247, category: 'Environment' },
      { id: '2', name: 'AI', postCount: 892, category: 'Technology' },
      { id: '3', name: 'WorkFromHome', postCount: 634, category: 'Lifestyle' },
      { id: '4', name: 'MentalHealth', postCount: 567, category: 'Wellness' },
      { id: '5', name: 'Cryptocurrency', postCount: 445, category: 'Finance' },
      { id: '6', name: 'SustainableLiving', postCount: 389, category: 'Environment' },
    ]);

    // Sample suggested users
    setSuggestedUsers([
      {
        id: '1',
        name: 'Dr. Sarah Chen',
        bio: 'Climate scientist sharing insights on environmental issues',
        followers: 15420,
        isFollowing: false,
      },
      {
        id: '2',
        name: 'Tech Innovator Mike',
        bio: 'AI researcher and startup founder',
        followers: 8930,
        isFollowing: false,
      },
      {
        id: '3',
        name: 'Wellness Coach Emma',
        bio: 'Mental health advocate and mindfulness expert',
        followers: 12100,
        isFollowing: false,
      },
    ]);

    // Sample popular posts
    setPopularPosts([
      {
        id: '1',
        authorName: 'Alex Rivera',
        content: 'The intersection of technology and sustainability is where the future lies. We need more green tech innovations!',
        likes: 156,
        comments: 23,
        tags: ['technology', 'sustainability', 'future'],
      },
      {
        id: '2',
        authorName: 'Jordan Kim',
        content: 'Remote work has changed how we think about productivity and work-life balance. What\'s your experience?',
        likes: 89,
        comments: 34,
        tags: ['remote', 'productivity', 'worklife'],
      },
    ]);
  }, []);

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue);
  };

  const handleFollow = (userId: string) => {
    setSuggestedUsers(users =>
      users.map(user =>
        user.id === userId
          ? { ...user, isFollowing: !user.isFollowing }
          : user
      )
    );
  };

  const formatNumber = (num: number) => {
    if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K';
    }
    return num.toString();
  };

  return (
    <Container maxWidth="lg">
      <Box sx={{ py: 3 }}>
        <Typography variant="h4" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <ExploreIcon />
          Explore
        </Typography>
        <Typography variant="body1" color="text.secondary" sx={{ mb: 4 }}>
          Discover trending topics, connect with new people, and explore popular content
        </Typography>

        {/* Search Bar */}
        <Paper sx={{ p: 2, mb: 4 }}>
          <TextField
            fullWidth
            placeholder="Search for topics, people, or posts..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <Search />
                </InputAdornment>
              ),
            }}
          />
        </Paper>

        {/* Tabs */}
        <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}>
          <Tabs value={activeTab} onChange={handleTabChange}>
            <Tab label="Trending" icon={<TrendingUp />} />
            <Tab label="People" icon={<People />} />
            <Tab label="Topics" icon={<Topic />} />
          </Tabs>
        </Box>

        {/* Trending Tab */}
        {activeTab === 0 && (
          <Grid container spacing={3}>
            <Grid item xs={12} md={8}>
              <Typography variant="h6" gutterBottom>
                Popular Posts
              </Typography>
              {popularPosts.map((post) => (
                <Card key={post.id} sx={{ mb: 2 }}>
                  <CardContent>
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                      <Avatar src={post.authorAvatar} sx={{ mr: 2 }}>
                        {post.authorName.split(' ').map(n => n[0]).join('')}
                      </Avatar>
                      <Typography variant="subtitle1" fontWeight="bold">
                        {post.authorName}
                      </Typography>
                    </Box>
                    <Typography variant="body1" sx={{ mb: 2 }}>
                      {post.content}
                    </Typography>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                      <Box>
                        {post.tags.map((tag) => (
                          <Chip
                            key={tag}
                            label={`#${tag}`}
                            size="small"
                            sx={{ mr: 1 }}
                            color="primary"
                            variant="outlined"
                          />
                        ))}
                      </Box>
                      <Box sx={{ display: 'flex', gap: 2 }}>
                        <Typography variant="caption" color="text.secondary">
                          {post.likes} likes
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          {post.comments} comments
                        </Typography>
                      </Box>
                    </Box>
                  </CardContent>
                </Card>
              ))}
            </Grid>

            <Grid item xs={12} md={4}>
              <Typography variant="h6" gutterBottom>
                Trending Topics
              </Typography>
              {trendingTopics.slice(0, 6).map((topic, index) => (
                <Card key={topic.id} sx={{ mb: 1 }}>
                  <CardContent sx={{ py: 2 }}>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                      <Box>
                        <Typography variant="body2" color="text.secondary">
                          #{index + 1} Trending in {topic.category}
                        </Typography>
                        <Typography variant="subtitle1" fontWeight="bold">
                          #{topic.name}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          {formatNumber(topic.postCount)} posts
                        </Typography>
                      </Box>
                    </Box>
                  </CardContent>
                </Card>
              ))}
            </Grid>
          </Grid>
        )}

        {/* People Tab */}
        {activeTab === 1 && (
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <Typography variant="h6" gutterBottom>
                Suggested People to Follow
              </Typography>
              <Grid container spacing={2}>
                {suggestedUsers.map((user) => (
                  <Grid item xs={12} sm={6} md={4} key={user.id}>
                    <Card>
                      <CardContent sx={{ textAlign: 'center' }}>
                        <Avatar
                          src={user.avatar}
                          sx={{ width: 80, height: 80, mx: 'auto', mb: 2 }}
                        >
                          {user.name.split(' ').map(n => n[0]).join('')}
                        </Avatar>
                        <Typography variant="h6" gutterBottom>
                          {user.name}
                        </Typography>
                        <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                          {user.bio}
                        </Typography>
                        <Typography variant="caption" color="text.secondary" sx={{ mb: 2, display: 'block' }}>
                          {formatNumber(user.followers)} followers
                        </Typography>
                        <Button
                          variant={user.isFollowing ? "outlined" : "contained"}
                          onClick={() => handleFollow(user.id)}
                          fullWidth
                        >
                          {user.isFollowing ? 'Following' : 'Follow'}
                        </Button>
                      </CardContent>
                    </Card>
                  </Grid>
                ))}
              </Grid>
            </Grid>
          </Grid>
        )}

        {/* Topics Tab */}
        {activeTab === 2 && (
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <Typography variant="h6" gutterBottom>
                Browse by Category
              </Typography>
              <Grid container spacing={2}>
                {['Technology', 'Environment', 'Lifestyle', 'Wellness', 'Finance', 'Education'].map((category) => (
                  <Grid item xs={12} sm={6} md={4} key={category}>
                    <Card sx={{ cursor: 'pointer', '&:hover': { elevation: 4 } }}>
                      <CardContent>
                        <Typography variant="h6" gutterBottom>
                          {category}
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          {trendingTopics.filter(t => t.category === category).length} trending topics
                        </Typography>
                        <Box sx={{ mt: 2 }}>
                          {trendingTopics
                            .filter(t => t.category === category)
                            .slice(0, 3)
                            .map((topic) => (
                              <Chip
                                key={topic.id}
                                label={`#${topic.name}`}
                                size="small"
                                sx={{ mr: 1, mb: 1 }}
                                variant="outlined"
                              />
                            ))}
                        </Box>
                      </CardContent>
                    </Card>
                  </Grid>
                ))}
              </Grid>
            </Grid>
          </Grid>
        )}
      </Box>
    </Container>
  );
};

export default Explore;
