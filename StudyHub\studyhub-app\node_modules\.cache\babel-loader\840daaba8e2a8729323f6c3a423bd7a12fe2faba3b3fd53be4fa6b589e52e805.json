{"ast": null, "code": "import React,{useState,useEffect,useRef}from'react';import{Box,Paper,Typography,TextField,IconButton,Avatar,List,ListItem,Chip,Menu,MenuItem,Divider,CircularProgress,InputAdornment}from'@mui/material';import{Send,AttachFile,EmojiEmotions,Reply,Delete,Edit,Close}from'@mui/icons-material';import{formatDistanceToNow}from'date-fns';import{ChatService}from'../../services/chatService';import{useAuth}from'../../contexts/AuthContext';import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const GroupChat=_ref=>{var _chat$name;let{chatId,chat,onClose}=_ref;const{user}=useAuth();const[messages,setMessages]=useState([]);const[newMessage,setNewMessage]=useState('');const[loading,setLoading]=useState(true);const[sending,setSending]=useState(false);const[typingUsers,setTypingUsers]=useState([]);const[anchorEl,setAnchorEl]=useState(null);const[selectedMessage,setSelectedMessage]=useState(null);const[replyTo,setReplyTo]=useState(null);const[editingMessage,setEditingMessage]=useState(null);const messagesEndRef=useRef(null);const typingTimeoutRef=useRef(null);useEffect(()=>{if(!chatId||!user)return;// Subscribe to messages\nconst unsubscribeMessages=ChatService.subscribeToMessages(chatId,newMessages=>{setMessages(newMessages);setLoading(false);scrollToBottom();});// Subscribe to typing indicators\nconst unsubscribeTyping=ChatService.subscribeToTyping(chatId,user.uid,setTypingUsers);// Mark messages as read\nChatService.markAsRead(chatId,user.uid);return()=>{unsubscribeMessages();unsubscribeTyping();};},[chatId,user]);const scrollToBottom=()=>{var _messagesEndRef$curre;(_messagesEndRef$curre=messagesEndRef.current)===null||_messagesEndRef$curre===void 0?void 0:_messagesEndRef$curre.scrollIntoView({behavior:'smooth'});};const handleSendMessage=async()=>{if(!newMessage.trim()||!user||sending)return;setSending(true);try{var _user$email;await ChatService.sendMessage(chatId,user.uid,{content:newMessage.trim(),type:'text',replyTo:replyTo===null||replyTo===void 0?void 0:replyTo.id},user.displayName||((_user$email=user.email)===null||_user$email===void 0?void 0:_user$email.split('@')[0])||'User');setNewMessage('');setReplyTo(null);// Stop typing indicator\nawait ChatService.setTyping(chatId,user.uid,user.displayName||'',false);}catch(error){console.error('Error sending message:',error);}finally{setSending(false);}};const handleTyping=async value=>{setNewMessage(value);if(!user)return;// Clear existing timeout\nif(typingTimeoutRef.current){clearTimeout(typingTimeoutRef.current);}if(value.trim()){// Set typing indicator\nawait ChatService.setTyping(chatId,user.uid,user.displayName||'',true);// Clear typing after 3 seconds of inactivity\ntypingTimeoutRef.current=setTimeout(async()=>{await ChatService.setTyping(chatId,user.uid,user.displayName||'',false);},3000);}else{// Clear typing immediately if input is empty\nawait ChatService.setTyping(chatId,user.uid,user.displayName||'',false);}};const handleMessageAction=(event,message)=>{setAnchorEl(event.currentTarget);setSelectedMessage(message);};const handleCloseMenu=()=>{setAnchorEl(null);setSelectedMessage(null);};const handleReply=()=>{setReplyTo(selectedMessage);handleCloseMenu();};const handleAddReaction=async emoji=>{if(!selectedMessage||!user)return;try{await ChatService.addReaction(chatId,selectedMessage.id,user.uid,emoji);handleCloseMenu();}catch(error){console.error('Error adding reaction:',error);}};const formatMessageTime=timestamp=>{if(!timestamp)return'';const date=timestamp.toDate?timestamp.toDate():new Date(timestamp);return formatDistanceToNow(date,{addSuffix:true});};const renderMessage=(message,index)=>{var _messages;const isOwnMessage=message.senderId===(user===null||user===void 0?void 0:user.uid);const showAvatar=index===0||((_messages=messages[index-1])===null||_messages===void 0?void 0:_messages.senderId)!==message.senderId;const replyToMessage=message.replyTo?messages.find(m=>m.id===message.replyTo):null;return/*#__PURE__*/_jsx(ListItem,{sx:{flexDirection:'column',alignItems:isOwnMessage?'flex-end':'flex-start',py:0.5},children:/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',flexDirection:isOwnMessage?'row-reverse':'row',alignItems:'flex-end',gap:1,maxWidth:'70%'},children:[showAvatar&&!isOwnMessage&&/*#__PURE__*/_jsx(Avatar,{src:message.senderAvatar,sx:{width:32,height:32},children:message.senderName[0]}),/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',flexDirection:'column',gap:0.5},children:[showAvatar&&!isOwnMessage&&/*#__PURE__*/_jsx(Typography,{variant:\"caption\",color:\"text.secondary\",sx:{px:1},children:message.senderName}),replyToMessage&&/*#__PURE__*/_jsxs(Paper,{sx:{p:1,bgcolor:'action.hover',borderLeft:3,borderColor:'primary.main',mb:0.5},children:[/*#__PURE__*/_jsxs(Typography,{variant:\"caption\",color:\"text.secondary\",children:[\"Replying to \",replyToMessage.senderName]}),/*#__PURE__*/_jsx(Typography,{variant:\"body2\",sx:{opacity:0.7},children:replyToMessage.content.length>50?\"\".concat(replyToMessage.content.substring(0,50),\"...\"):replyToMessage.content})]}),/*#__PURE__*/_jsxs(Paper,{sx:{p:1.5,bgcolor:isOwnMessage?'primary.main':'background.paper',color:isOwnMessage?'primary.contrastText':'text.primary',borderRadius:2,position:'relative'},onClick:e=>handleMessageAction(e,message),children:[/*#__PURE__*/_jsx(Typography,{variant:\"body2\",children:message.content}),message.reactions&&message.reactions.length>0&&/*#__PURE__*/_jsx(Box,{sx:{display:'flex',gap:0.5,mt:1,flexWrap:'wrap'},children:message.reactions.map((reaction,idx)=>/*#__PURE__*/_jsx(Chip,{label:\"\".concat(reaction.emoji,\" \").concat(reaction.count),size:\"small\",variant:\"outlined\",onClick:()=>handleAddReaction(reaction.emoji),sx:{height:20,fontSize:'0.7rem'}},idx))}),/*#__PURE__*/_jsx(Typography,{variant:\"caption\",sx:{display:'block',mt:0.5,opacity:0.7,fontSize:'0.7rem'},children:formatMessageTime(message.timestamp)})]})]})]})},message.id);};if(loading){return/*#__PURE__*/_jsx(Box,{display:\"flex\",justifyContent:\"center\",alignItems:\"center\",height:\"400px\",children:/*#__PURE__*/_jsx(CircularProgress,{})});}return/*#__PURE__*/_jsxs(Box,{sx:{height:'100%',display:'flex',flexDirection:'column'},children:[/*#__PURE__*/_jsxs(Paper,{sx:{p:2,borderRadius:0,borderBottom:1,borderColor:'divider',display:'flex',alignItems:'center',justifyContent:'space-between'},children:[/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center',gap:2},children:[/*#__PURE__*/_jsx(Avatar,{children:((_chat$name=chat.name)===null||_chat$name===void 0?void 0:_chat$name[0])||'G'}),/*#__PURE__*/_jsxs(Box,{children:[/*#__PURE__*/_jsx(Typography,{variant:\"h6\",children:chat.name||'Group Chat'}),/*#__PURE__*/_jsxs(Typography,{variant:\"caption\",color:\"text.secondary\",children:[chat.participants.length,\" members\",typingUsers.length>0&&/*#__PURE__*/_jsxs(\"span\",{children:[\" \\u2022 \",typingUsers.map(t=>t.userName).join(', '),\" typing...\"]})]})]})]}),onClose&&/*#__PURE__*/_jsx(IconButton,{onClick:onClose,children:/*#__PURE__*/_jsx(Close,{})})]}),/*#__PURE__*/_jsxs(Box,{sx:{flexGrow:1,overflow:'auto',p:1},children:[/*#__PURE__*/_jsx(List,{sx:{py:0},children:messages.map((message,index)=>renderMessage(message,index))}),/*#__PURE__*/_jsx(\"div\",{ref:messagesEndRef})]}),replyTo&&/*#__PURE__*/_jsx(Paper,{sx:{p:1,m:1,bgcolor:'action.hover'},children:/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',justifyContent:'space-between',alignItems:'center'},children:[/*#__PURE__*/_jsxs(Box,{children:[/*#__PURE__*/_jsxs(Typography,{variant:\"caption\",color:\"text.secondary\",children:[\"Replying to \",replyTo.senderName]}),/*#__PURE__*/_jsx(Typography,{variant:\"body2\",children:replyTo.content.length>50?\"\".concat(replyTo.content.substring(0,50),\"...\"):replyTo.content})]}),/*#__PURE__*/_jsx(IconButton,{size:\"small\",onClick:()=>setReplyTo(null),children:/*#__PURE__*/_jsx(Close,{})})]})}),/*#__PURE__*/_jsx(Paper,{sx:{p:2,borderRadius:0},children:/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',gap:1,alignItems:'flex-end'},children:[/*#__PURE__*/_jsx(TextField,{fullWidth:true,multiline:true,maxRows:4,placeholder:\"Type a message...\",value:newMessage,onChange:e=>handleTyping(e.target.value),onKeyPress:e=>{if(e.key==='Enter'&&!e.shiftKey){e.preventDefault();handleSendMessage();}},InputProps:{endAdornment:/*#__PURE__*/_jsxs(InputAdornment,{position:\"end\",children:[/*#__PURE__*/_jsx(IconButton,{size:\"small\",children:/*#__PURE__*/_jsx(AttachFile,{})}),/*#__PURE__*/_jsx(IconButton,{size:\"small\",children:/*#__PURE__*/_jsx(EmojiEmotions,{})})]})}}),/*#__PURE__*/_jsx(IconButton,{color:\"primary\",onClick:handleSendMessage,disabled:!newMessage.trim()||sending,children:sending?/*#__PURE__*/_jsx(CircularProgress,{size:20}):/*#__PURE__*/_jsx(Send,{})})]})}),/*#__PURE__*/_jsxs(Menu,{anchorEl:anchorEl,open:Boolean(anchorEl),onClose:handleCloseMenu,children:[/*#__PURE__*/_jsxs(MenuItem,{onClick:handleReply,children:[/*#__PURE__*/_jsx(Reply,{sx:{mr:1}}),\"Reply\"]}),/*#__PURE__*/_jsx(MenuItem,{onClick:()=>handleAddReaction('👍'),children:\"\\uD83D\\uDC4D Like\"}),/*#__PURE__*/_jsx(MenuItem,{onClick:()=>handleAddReaction('❤️'),children:\"\\u2764\\uFE0F Love\"}),/*#__PURE__*/_jsx(MenuItem,{onClick:()=>handleAddReaction('😂'),children:\"\\uD83D\\uDE02 Laugh\"}),(selectedMessage===null||selectedMessage===void 0?void 0:selectedMessage.senderId)===(user===null||user===void 0?void 0:user.uid)&&/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(Divider,{}),/*#__PURE__*/_jsxs(MenuItem,{onClick:()=>setEditingMessage(selectedMessage),children:[/*#__PURE__*/_jsx(Edit,{sx:{mr:1}}),\"Edit\"]}),/*#__PURE__*/_jsxs(MenuItem,{sx:{color:'error.main'},children:[/*#__PURE__*/_jsx(Delete,{sx:{mr:1}}),\"Delete\"]})]})]})]});};export default GroupChat;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "Box", "Paper", "Typography", "TextField", "IconButton", "Avatar", "List", "ListItem", "Chip", "<PERSON><PERSON>", "MenuItem", "Divider", "CircularProgress", "InputAdornment", "Send", "AttachFile", "EmojiEmotions", "Reply", "Delete", "Edit", "Close", "formatDistanceToNow", "ChatService", "useAuth", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "GroupChat", "_ref", "_chat$name", "chatId", "chat", "onClose", "user", "messages", "setMessages", "newMessage", "setNewMessage", "loading", "setLoading", "sending", "setSending", "typingUsers", "setTypingUsers", "anchorEl", "setAnchorEl", "selectedMessage", "setSelectedMessage", "replyTo", "setReplyTo", "editingMessage", "setEditingMessage", "messagesEndRef", "typingTimeoutRef", "unsubscribeMessages", "subscribeToMessages", "newMessages", "scrollToBottom", "unsubscribeTyping", "subscribeToTyping", "uid", "mark<PERSON><PERSON><PERSON>", "_messagesEndRef$curre", "current", "scrollIntoView", "behavior", "handleSendMessage", "trim", "_user$email", "sendMessage", "content", "type", "id", "displayName", "email", "split", "setTyping", "error", "console", "handleTyping", "value", "clearTimeout", "setTimeout", "handleMessageAction", "event", "message", "currentTarget", "handleCloseMenu", "handleReply", "handleAddReaction", "emoji", "addReaction", "formatMessageTime", "timestamp", "date", "toDate", "Date", "addSuffix", "renderMessage", "index", "_messages", "isOwnMessage", "senderId", "showAvatar", "replyToMessage", "find", "m", "sx", "flexDirection", "alignItems", "py", "children", "display", "gap", "max<PERSON><PERSON><PERSON>", "src", "senderAvatar", "width", "height", "sender<PERSON>ame", "variant", "color", "px", "p", "bgcolor", "borderLeft", "borderColor", "mb", "opacity", "length", "concat", "substring", "borderRadius", "position", "onClick", "e", "reactions", "mt", "flexWrap", "map", "reaction", "idx", "label", "count", "size", "fontSize", "justifyContent", "borderBottom", "name", "participants", "t", "userName", "join", "flexGrow", "overflow", "ref", "fullWidth", "multiline", "maxRows", "placeholder", "onChange", "target", "onKeyPress", "key", "shift<PERSON>ey", "preventDefault", "InputProps", "endAdornment", "disabled", "open", "Boolean", "mr"], "sources": ["C:/Users/<USER>/OneDrive/ドキュメント/Projects/StudyHub/studyhub-app/src/components/Chat/GroupChat.tsx"], "sourcesContent": ["import React, { useState, useEffect, useRef } from 'react';\nimport {\n  Box,\n  Paper,\n  Typography,\n  TextField,\n  IconButton,\n  Avatar,\n  List,\n  ListItem,\n  Chip,\n  Menu,\n  MenuItem,\n  Divider,\n  CircularProgress,\n  InputAdornment,\n} from '@mui/material';\nimport {\n  Send,\n  AttachFile,\n  EmojiEmotions,\n  MoreVert,\n  Reply,\n  Delete,\n  Edit,\n  Close,\n} from '@mui/icons-material';\nimport { formatDistanceToNow } from 'date-fns';\nimport { ChatService } from '../../services/chatService';\nimport { ChatMessage, Chat, ChatTypingIndicator } from '../../types/chat';\nimport { useAuth } from '../../contexts/AuthContext';\n\ninterface GroupChatProps {\n  chatId: string;\n  chat: Chat;\n  onClose?: () => void;\n}\n\nconst GroupChat: React.FC<GroupChatProps> = ({ chatId, chat, onClose }) => {\n  const { user } = useAuth();\n  const [messages, setMessages] = useState<ChatMessage[]>([]);\n  const [newMessage, setNewMessage] = useState('');\n  const [loading, setLoading] = useState(true);\n  const [sending, setSending] = useState(false);\n  const [typingUsers, setTypingUsers] = useState<ChatTypingIndicator[]>([]);\n  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);\n  const [selectedMessage, setSelectedMessage] = useState<ChatMessage | null>(null);\n  const [replyTo, setReplyTo] = useState<ChatMessage | null>(null);\n  const [editingMessage, setEditingMessage] = useState<ChatMessage | null>(null);\n  const messagesEndRef = useRef<HTMLDivElement>(null);\n  const typingTimeoutRef = useRef<NodeJS.Timeout | null>(null);\n\n  useEffect(() => {\n    if (!chatId || !user) return;\n\n    // Subscribe to messages\n    const unsubscribeMessages = ChatService.subscribeToMessages(\n      chatId,\n      (newMessages) => {\n        setMessages(newMessages);\n        setLoading(false);\n        scrollToBottom();\n      }\n    );\n\n    // Subscribe to typing indicators\n    const unsubscribeTyping = ChatService.subscribeToTyping(\n      chatId,\n      user.uid,\n      setTypingUsers\n    );\n\n    // Mark messages as read\n    ChatService.markAsRead(chatId, user.uid);\n\n    return () => {\n      unsubscribeMessages();\n      unsubscribeTyping();\n    };\n  }, [chatId, user]);\n\n  const scrollToBottom = () => {\n    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });\n  };\n\n  const handleSendMessage = async () => {\n    if (!newMessage.trim() || !user || sending) return;\n\n    setSending(true);\n    try {\n      await ChatService.sendMessage(\n        chatId,\n        user.uid,\n        {\n          content: newMessage.trim(),\n          type: 'text',\n          replyTo: replyTo?.id,\n        },\n        user.displayName || user.email?.split('@')[0] || 'User'\n      );\n\n      setNewMessage('');\n      setReplyTo(null);\n\n      // Stop typing indicator\n      await ChatService.setTyping(chatId, user.uid, user.displayName || '', false);\n    } catch (error) {\n      console.error('Error sending message:', error);\n    } finally {\n      setSending(false);\n    }\n  };\n\n  const handleTyping = async (value: string) => {\n    setNewMessage(value);\n\n    if (!user) return;\n\n    // Clear existing timeout\n    if (typingTimeoutRef.current) {\n      clearTimeout(typingTimeoutRef.current);\n    }\n\n    if (value.trim()) {\n      // Set typing indicator\n      await ChatService.setTyping(chatId, user.uid, user.displayName || '', true);\n\n      // Clear typing after 3 seconds of inactivity\n      typingTimeoutRef.current = setTimeout(async () => {\n        await ChatService.setTyping(chatId, user.uid, user.displayName || '', false);\n      }, 3000);\n    } else {\n      // Clear typing immediately if input is empty\n      await ChatService.setTyping(chatId, user.uid, user.displayName || '', false);\n    }\n  };\n\n  const handleMessageAction = (event: React.MouseEvent<HTMLElement>, message: ChatMessage) => {\n    setAnchorEl(event.currentTarget);\n    setSelectedMessage(message);\n  };\n\n  const handleCloseMenu = () => {\n    setAnchorEl(null);\n    setSelectedMessage(null);\n  };\n\n  const handleReply = () => {\n    setReplyTo(selectedMessage);\n    handleCloseMenu();\n  };\n\n  const handleAddReaction = async (emoji: string) => {\n    if (!selectedMessage || !user) return;\n\n    try {\n      await ChatService.addReaction(chatId, selectedMessage.id, user.uid, emoji);\n      handleCloseMenu();\n    } catch (error) {\n      console.error('Error adding reaction:', error);\n    }\n  };\n\n  const formatMessageTime = (timestamp: any) => {\n    if (!timestamp) return '';\n    \n    const date = timestamp.toDate ? timestamp.toDate() : new Date(timestamp);\n    return formatDistanceToNow(date, { addSuffix: true });\n  };\n\n  const renderMessage = (message: ChatMessage, index: number) => {\n    const isOwnMessage = message.senderId === user?.uid;\n    const showAvatar = index === 0 || messages[index - 1]?.senderId !== message.senderId;\n    const replyToMessage = message.replyTo ? messages.find(m => m.id === message.replyTo) : null;\n\n    return (\n      <ListItem\n        key={message.id}\n        sx={{\n          flexDirection: 'column',\n          alignItems: isOwnMessage ? 'flex-end' : 'flex-start',\n          py: 0.5,\n        }}\n      >\n        <Box\n          sx={{\n            display: 'flex',\n            flexDirection: isOwnMessage ? 'row-reverse' : 'row',\n            alignItems: 'flex-end',\n            gap: 1,\n            maxWidth: '70%',\n          }}\n        >\n          {showAvatar && !isOwnMessage && (\n            <Avatar\n              src={message.senderAvatar}\n              sx={{ width: 32, height: 32 }}\n            >\n              {message.senderName[0]}\n            </Avatar>\n          )}\n          \n          <Box\n            sx={{\n              display: 'flex',\n              flexDirection: 'column',\n              gap: 0.5,\n            }}\n          >\n            {showAvatar && !isOwnMessage && (\n              <Typography variant=\"caption\" color=\"text.secondary\" sx={{ px: 1 }}>\n                {message.senderName}\n              </Typography>\n            )}\n            \n            {replyToMessage && (\n              <Paper\n                sx={{\n                  p: 1,\n                  bgcolor: 'action.hover',\n                  borderLeft: 3,\n                  borderColor: 'primary.main',\n                  mb: 0.5,\n                }}\n              >\n                <Typography variant=\"caption\" color=\"text.secondary\">\n                  Replying to {replyToMessage.senderName}\n                </Typography>\n                <Typography variant=\"body2\" sx={{ opacity: 0.7 }}>\n                  {replyToMessage.content.length > 50\n                    ? `${replyToMessage.content.substring(0, 50)}...`\n                    : replyToMessage.content}\n                </Typography>\n              </Paper>\n            )}\n            \n            <Paper\n              sx={{\n                p: 1.5,\n                bgcolor: isOwnMessage ? 'primary.main' : 'background.paper',\n                color: isOwnMessage ? 'primary.contrastText' : 'text.primary',\n                borderRadius: 2,\n                position: 'relative',\n              }}\n              onClick={(e) => handleMessageAction(e, message)}\n            >\n              <Typography variant=\"body2\">{message.content}</Typography>\n              \n              {message.reactions && message.reactions.length > 0 && (\n                <Box sx={{ display: 'flex', gap: 0.5, mt: 1, flexWrap: 'wrap' }}>\n                  {message.reactions.map((reaction, idx) => (\n                    <Chip\n                      key={idx}\n                      label={`${reaction.emoji} ${reaction.count}`}\n                      size=\"small\"\n                      variant=\"outlined\"\n                      onClick={() => handleAddReaction(reaction.emoji)}\n                      sx={{ height: 20, fontSize: '0.7rem' }}\n                    />\n                  ))}\n                </Box>\n              )}\n              \n              <Typography\n                variant=\"caption\"\n                sx={{\n                  display: 'block',\n                  mt: 0.5,\n                  opacity: 0.7,\n                  fontSize: '0.7rem',\n                }}\n              >\n                {formatMessageTime(message.timestamp)}\n              </Typography>\n            </Paper>\n          </Box>\n        </Box>\n      </ListItem>\n    );\n  };\n\n  if (loading) {\n    return (\n      <Box display=\"flex\" justifyContent=\"center\" alignItems=\"center\" height=\"400px\">\n        <CircularProgress />\n      </Box>\n    );\n  }\n\n  return (\n    <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>\n      {/* Chat Header */}\n      <Paper\n        sx={{\n          p: 2,\n          borderRadius: 0,\n          borderBottom: 1,\n          borderColor: 'divider',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'space-between',\n        }}\n      >\n        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>\n          <Avatar>{chat.name?.[0] || 'G'}</Avatar>\n          <Box>\n            <Typography variant=\"h6\">{chat.name || 'Group Chat'}</Typography>\n            <Typography variant=\"caption\" color=\"text.secondary\">\n              {chat.participants.length} members\n              {typingUsers.length > 0 && (\n                <span> • {typingUsers.map(t => t.userName).join(', ')} typing...</span>\n              )}\n            </Typography>\n          </Box>\n        </Box>\n        \n        {onClose && (\n          <IconButton onClick={onClose}>\n            <Close />\n          </IconButton>\n        )}\n      </Paper>\n\n      {/* Messages */}\n      <Box sx={{ flexGrow: 1, overflow: 'auto', p: 1 }}>\n        <List sx={{ py: 0 }}>\n          {messages.map((message, index) => renderMessage(message, index))}\n        </List>\n        <div ref={messagesEndRef} />\n      </Box>\n\n      {/* Reply Preview */}\n      {replyTo && (\n        <Paper sx={{ p: 1, m: 1, bgcolor: 'action.hover' }}>\n          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\n            <Box>\n              <Typography variant=\"caption\" color=\"text.secondary\">\n                Replying to {replyTo.senderName}\n              </Typography>\n              <Typography variant=\"body2\">\n                {replyTo.content.length > 50\n                  ? `${replyTo.content.substring(0, 50)}...`\n                  : replyTo.content}\n              </Typography>\n            </Box>\n            <IconButton size=\"small\" onClick={() => setReplyTo(null)}>\n              <Close />\n            </IconButton>\n          </Box>\n        </Paper>\n      )}\n\n      {/* Message Input */}\n      <Paper sx={{ p: 2, borderRadius: 0 }}>\n        <Box sx={{ display: 'flex', gap: 1, alignItems: 'flex-end' }}>\n          <TextField\n            fullWidth\n            multiline\n            maxRows={4}\n            placeholder=\"Type a message...\"\n            value={newMessage}\n            onChange={(e) => handleTyping(e.target.value)}\n            onKeyPress={(e) => {\n              if (e.key === 'Enter' && !e.shiftKey) {\n                e.preventDefault();\n                handleSendMessage();\n              }\n            }}\n            InputProps={{\n              endAdornment: (\n                <InputAdornment position=\"end\">\n                  <IconButton size=\"small\">\n                    <AttachFile />\n                  </IconButton>\n                  <IconButton size=\"small\">\n                    <EmojiEmotions />\n                  </IconButton>\n                </InputAdornment>\n              ),\n            }}\n          />\n          <IconButton\n            color=\"primary\"\n            onClick={handleSendMessage}\n            disabled={!newMessage.trim() || sending}\n          >\n            {sending ? <CircularProgress size={20} /> : <Send />}\n          </IconButton>\n        </Box>\n      </Paper>\n\n      {/* Message Actions Menu */}\n      <Menu\n        anchorEl={anchorEl}\n        open={Boolean(anchorEl)}\n        onClose={handleCloseMenu}\n      >\n        <MenuItem onClick={handleReply}>\n          <Reply sx={{ mr: 1 }} />\n          Reply\n        </MenuItem>\n        <MenuItem onClick={() => handleAddReaction('👍')}>\n          👍 Like\n        </MenuItem>\n        <MenuItem onClick={() => handleAddReaction('❤️')}>\n          ❤️ Love\n        </MenuItem>\n        <MenuItem onClick={() => handleAddReaction('😂')}>\n          😂 Laugh\n        </MenuItem>\n        {selectedMessage?.senderId === user?.uid && (\n          <>\n            <Divider />\n            <MenuItem onClick={() => setEditingMessage(selectedMessage)}>\n              <Edit sx={{ mr: 1 }} />\n              Edit\n            </MenuItem>\n            <MenuItem sx={{ color: 'error.main' }}>\n              <Delete sx={{ mr: 1 }} />\n              Delete\n            </MenuItem>\n          </>\n        )}\n      </Menu>\n    </Box>\n  );\n};\n\nexport default GroupChat;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,CAAEC,MAAM,KAAQ,OAAO,CAC1D,OACEC,GAAG,CACHC,KAAK,CACLC,UAAU,CACVC,SAAS,CACTC,UAAU,CACVC,MAAM,CACNC,IAAI,CACJC,QAAQ,CACRC,IAAI,CACJC,IAAI,CACJC,QAAQ,CACRC,OAAO,CACPC,gBAAgB,CAChBC,cAAc,KACT,eAAe,CACtB,OACEC,IAAI,CACJC,UAAU,CACVC,aAAa,CAEbC,KAAK,CACLC,MAAM,CACNC,IAAI,CACJC,KAAK,KACA,qBAAqB,CAC5B,OAASC,mBAAmB,KAAQ,UAAU,CAC9C,OAASC,WAAW,KAAQ,4BAA4B,CAExD,OAASC,OAAO,KAAQ,4BAA4B,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBAQrD,KAAM,CAAAC,SAAmC,CAAGC,IAAA,EAA+B,KAAAC,UAAA,IAA9B,CAAEC,MAAM,CAAEC,IAAI,CAAEC,OAAQ,CAAC,CAAAJ,IAAA,CACpE,KAAM,CAAEK,IAAK,CAAC,CAAGb,OAAO,CAAC,CAAC,CAC1B,KAAM,CAACc,QAAQ,CAAEC,WAAW,CAAC,CAAGzC,QAAQ,CAAgB,EAAE,CAAC,CAC3D,KAAM,CAAC0C,UAAU,CAAEC,aAAa,CAAC,CAAG3C,QAAQ,CAAC,EAAE,CAAC,CAChD,KAAM,CAAC4C,OAAO,CAAEC,UAAU,CAAC,CAAG7C,QAAQ,CAAC,IAAI,CAAC,CAC5C,KAAM,CAAC8C,OAAO,CAAEC,UAAU,CAAC,CAAG/C,QAAQ,CAAC,KAAK,CAAC,CAC7C,KAAM,CAACgD,WAAW,CAAEC,cAAc,CAAC,CAAGjD,QAAQ,CAAwB,EAAE,CAAC,CACzE,KAAM,CAACkD,QAAQ,CAAEC,WAAW,CAAC,CAAGnD,QAAQ,CAAqB,IAAI,CAAC,CAClE,KAAM,CAACoD,eAAe,CAAEC,kBAAkB,CAAC,CAAGrD,QAAQ,CAAqB,IAAI,CAAC,CAChF,KAAM,CAACsD,OAAO,CAAEC,UAAU,CAAC,CAAGvD,QAAQ,CAAqB,IAAI,CAAC,CAChE,KAAM,CAACwD,cAAc,CAAEC,iBAAiB,CAAC,CAAGzD,QAAQ,CAAqB,IAAI,CAAC,CAC9E,KAAM,CAAA0D,cAAc,CAAGxD,MAAM,CAAiB,IAAI,CAAC,CACnD,KAAM,CAAAyD,gBAAgB,CAAGzD,MAAM,CAAwB,IAAI,CAAC,CAE5DD,SAAS,CAAC,IAAM,CACd,GAAI,CAACmC,MAAM,EAAI,CAACG,IAAI,CAAE,OAEtB;AACA,KAAM,CAAAqB,mBAAmB,CAAGnC,WAAW,CAACoC,mBAAmB,CACzDzB,MAAM,CACL0B,WAAW,EAAK,CACfrB,WAAW,CAACqB,WAAW,CAAC,CACxBjB,UAAU,CAAC,KAAK,CAAC,CACjBkB,cAAc,CAAC,CAAC,CAClB,CACF,CAAC,CAED;AACA,KAAM,CAAAC,iBAAiB,CAAGvC,WAAW,CAACwC,iBAAiB,CACrD7B,MAAM,CACNG,IAAI,CAAC2B,GAAG,CACRjB,cACF,CAAC,CAED;AACAxB,WAAW,CAAC0C,UAAU,CAAC/B,MAAM,CAAEG,IAAI,CAAC2B,GAAG,CAAC,CAExC,MAAO,IAAM,CACXN,mBAAmB,CAAC,CAAC,CACrBI,iBAAiB,CAAC,CAAC,CACrB,CAAC,CACH,CAAC,CAAE,CAAC5B,MAAM,CAAEG,IAAI,CAAC,CAAC,CAElB,KAAM,CAAAwB,cAAc,CAAGA,CAAA,GAAM,KAAAK,qBAAA,CAC3B,CAAAA,qBAAA,CAAAV,cAAc,CAACW,OAAO,UAAAD,qBAAA,iBAAtBA,qBAAA,CAAwBE,cAAc,CAAC,CAAEC,QAAQ,CAAE,QAAS,CAAC,CAAC,CAChE,CAAC,CAED,KAAM,CAAAC,iBAAiB,CAAG,KAAAA,CAAA,GAAY,CACpC,GAAI,CAAC9B,UAAU,CAAC+B,IAAI,CAAC,CAAC,EAAI,CAAClC,IAAI,EAAIO,OAAO,CAAE,OAE5CC,UAAU,CAAC,IAAI,CAAC,CAChB,GAAI,KAAA2B,WAAA,CACF,KAAM,CAAAjD,WAAW,CAACkD,WAAW,CAC3BvC,MAAM,CACNG,IAAI,CAAC2B,GAAG,CACR,CACEU,OAAO,CAAElC,UAAU,CAAC+B,IAAI,CAAC,CAAC,CAC1BI,IAAI,CAAE,MAAM,CACZvB,OAAO,CAAEA,OAAO,SAAPA,OAAO,iBAAPA,OAAO,CAAEwB,EACpB,CAAC,CACDvC,IAAI,CAACwC,WAAW,IAAAL,WAAA,CAAInC,IAAI,CAACyC,KAAK,UAAAN,WAAA,iBAAVA,WAAA,CAAYO,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAI,MACnD,CAAC,CAEDtC,aAAa,CAAC,EAAE,CAAC,CACjBY,UAAU,CAAC,IAAI,CAAC,CAEhB;AACA,KAAM,CAAA9B,WAAW,CAACyD,SAAS,CAAC9C,MAAM,CAAEG,IAAI,CAAC2B,GAAG,CAAE3B,IAAI,CAACwC,WAAW,EAAI,EAAE,CAAE,KAAK,CAAC,CAC9E,CAAE,MAAOI,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,wBAAwB,CAAEA,KAAK,CAAC,CAChD,CAAC,OAAS,CACRpC,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED,KAAM,CAAAsC,YAAY,CAAG,KAAO,CAAAC,KAAa,EAAK,CAC5C3C,aAAa,CAAC2C,KAAK,CAAC,CAEpB,GAAI,CAAC/C,IAAI,CAAE,OAEX;AACA,GAAIoB,gBAAgB,CAACU,OAAO,CAAE,CAC5BkB,YAAY,CAAC5B,gBAAgB,CAACU,OAAO,CAAC,CACxC,CAEA,GAAIiB,KAAK,CAACb,IAAI,CAAC,CAAC,CAAE,CAChB;AACA,KAAM,CAAAhD,WAAW,CAACyD,SAAS,CAAC9C,MAAM,CAAEG,IAAI,CAAC2B,GAAG,CAAE3B,IAAI,CAACwC,WAAW,EAAI,EAAE,CAAE,IAAI,CAAC,CAE3E;AACApB,gBAAgB,CAACU,OAAO,CAAGmB,UAAU,CAAC,SAAY,CAChD,KAAM,CAAA/D,WAAW,CAACyD,SAAS,CAAC9C,MAAM,CAAEG,IAAI,CAAC2B,GAAG,CAAE3B,IAAI,CAACwC,WAAW,EAAI,EAAE,CAAE,KAAK,CAAC,CAC9E,CAAC,CAAE,IAAI,CAAC,CACV,CAAC,IAAM,CACL;AACA,KAAM,CAAAtD,WAAW,CAACyD,SAAS,CAAC9C,MAAM,CAAEG,IAAI,CAAC2B,GAAG,CAAE3B,IAAI,CAACwC,WAAW,EAAI,EAAE,CAAE,KAAK,CAAC,CAC9E,CACF,CAAC,CAED,KAAM,CAAAU,mBAAmB,CAAGA,CAACC,KAAoC,CAAEC,OAAoB,GAAK,CAC1FxC,WAAW,CAACuC,KAAK,CAACE,aAAa,CAAC,CAChCvC,kBAAkB,CAACsC,OAAO,CAAC,CAC7B,CAAC,CAED,KAAM,CAAAE,eAAe,CAAGA,CAAA,GAAM,CAC5B1C,WAAW,CAAC,IAAI,CAAC,CACjBE,kBAAkB,CAAC,IAAI,CAAC,CAC1B,CAAC,CAED,KAAM,CAAAyC,WAAW,CAAGA,CAAA,GAAM,CACxBvC,UAAU,CAACH,eAAe,CAAC,CAC3ByC,eAAe,CAAC,CAAC,CACnB,CAAC,CAED,KAAM,CAAAE,iBAAiB,CAAG,KAAO,CAAAC,KAAa,EAAK,CACjD,GAAI,CAAC5C,eAAe,EAAI,CAACb,IAAI,CAAE,OAE/B,GAAI,CACF,KAAM,CAAAd,WAAW,CAACwE,WAAW,CAAC7D,MAAM,CAAEgB,eAAe,CAAC0B,EAAE,CAAEvC,IAAI,CAAC2B,GAAG,CAAE8B,KAAK,CAAC,CAC1EH,eAAe,CAAC,CAAC,CACnB,CAAE,MAAOV,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,wBAAwB,CAAEA,KAAK,CAAC,CAChD,CACF,CAAC,CAED,KAAM,CAAAe,iBAAiB,CAAIC,SAAc,EAAK,CAC5C,GAAI,CAACA,SAAS,CAAE,MAAO,EAAE,CAEzB,KAAM,CAAAC,IAAI,CAAGD,SAAS,CAACE,MAAM,CAAGF,SAAS,CAACE,MAAM,CAAC,CAAC,CAAG,GAAI,CAAAC,IAAI,CAACH,SAAS,CAAC,CACxE,MAAO,CAAA3E,mBAAmB,CAAC4E,IAAI,CAAE,CAAEG,SAAS,CAAE,IAAK,CAAC,CAAC,CACvD,CAAC,CAED,KAAM,CAAAC,aAAa,CAAGA,CAACb,OAAoB,CAAEc,KAAa,GAAK,KAAAC,SAAA,CAC7D,KAAM,CAAAC,YAAY,CAAGhB,OAAO,CAACiB,QAAQ,IAAKrE,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAE2B,GAAG,EACnD,KAAM,CAAA2C,UAAU,CAAGJ,KAAK,GAAK,CAAC,EAAI,EAAAC,SAAA,CAAAlE,QAAQ,CAACiE,KAAK,CAAG,CAAC,CAAC,UAAAC,SAAA,iBAAnBA,SAAA,CAAqBE,QAAQ,IAAKjB,OAAO,CAACiB,QAAQ,CACpF,KAAM,CAAAE,cAAc,CAAGnB,OAAO,CAACrC,OAAO,CAAGd,QAAQ,CAACuE,IAAI,CAACC,CAAC,EAAIA,CAAC,CAAClC,EAAE,GAAKa,OAAO,CAACrC,OAAO,CAAC,CAAG,IAAI,CAE5F,mBACE1B,IAAA,CAAClB,QAAQ,EAEPuG,EAAE,CAAE,CACFC,aAAa,CAAE,QAAQ,CACvBC,UAAU,CAAER,YAAY,CAAG,UAAU,CAAG,YAAY,CACpDS,EAAE,CAAE,GACN,CAAE,CAAAC,QAAA,cAEFvF,KAAA,CAAC3B,GAAG,EACF8G,EAAE,CAAE,CACFK,OAAO,CAAE,MAAM,CACfJ,aAAa,CAAEP,YAAY,CAAG,aAAa,CAAG,KAAK,CACnDQ,UAAU,CAAE,UAAU,CACtBI,GAAG,CAAE,CAAC,CACNC,QAAQ,CAAE,KACZ,CAAE,CAAAH,QAAA,EAEDR,UAAU,EAAI,CAACF,YAAY,eAC1B/E,IAAA,CAACpB,MAAM,EACLiH,GAAG,CAAE9B,OAAO,CAAC+B,YAAa,CAC1BT,EAAE,CAAE,CAAEU,KAAK,CAAE,EAAE,CAAEC,MAAM,CAAE,EAAG,CAAE,CAAAP,QAAA,CAE7B1B,OAAO,CAACkC,UAAU,CAAC,CAAC,CAAC,CAChB,CACT,cAED/F,KAAA,CAAC3B,GAAG,EACF8G,EAAE,CAAE,CACFK,OAAO,CAAE,MAAM,CACfJ,aAAa,CAAE,QAAQ,CACvBK,GAAG,CAAE,GACP,CAAE,CAAAF,QAAA,EAEDR,UAAU,EAAI,CAACF,YAAY,eAC1B/E,IAAA,CAACvB,UAAU,EAACyH,OAAO,CAAC,SAAS,CAACC,KAAK,CAAC,gBAAgB,CAACd,EAAE,CAAE,CAAEe,EAAE,CAAE,CAAE,CAAE,CAAAX,QAAA,CAChE1B,OAAO,CAACkC,UAAU,CACT,CACb,CAEAf,cAAc,eACbhF,KAAA,CAAC1B,KAAK,EACJ6G,EAAE,CAAE,CACFgB,CAAC,CAAE,CAAC,CACJC,OAAO,CAAE,cAAc,CACvBC,UAAU,CAAE,CAAC,CACbC,WAAW,CAAE,cAAc,CAC3BC,EAAE,CAAE,GACN,CAAE,CAAAhB,QAAA,eAEFvF,KAAA,CAACzB,UAAU,EAACyH,OAAO,CAAC,SAAS,CAACC,KAAK,CAAC,gBAAgB,CAAAV,QAAA,EAAC,cACvC,CAACP,cAAc,CAACe,UAAU,EAC5B,CAAC,cACbjG,IAAA,CAACvB,UAAU,EAACyH,OAAO,CAAC,OAAO,CAACb,EAAE,CAAE,CAAEqB,OAAO,CAAE,GAAI,CAAE,CAAAjB,QAAA,CAC9CP,cAAc,CAAClC,OAAO,CAAC2D,MAAM,CAAG,EAAE,IAAAC,MAAA,CAC5B1B,cAAc,CAAClC,OAAO,CAAC6D,SAAS,CAAC,CAAC,CAAE,EAAE,CAAC,QAC1C3B,cAAc,CAAClC,OAAO,CAChB,CAAC,EACR,CACR,cAED9C,KAAA,CAAC1B,KAAK,EACJ6G,EAAE,CAAE,CACFgB,CAAC,CAAE,GAAG,CACNC,OAAO,CAAEvB,YAAY,CAAG,cAAc,CAAG,kBAAkB,CAC3DoB,KAAK,CAAEpB,YAAY,CAAG,sBAAsB,CAAG,cAAc,CAC7D+B,YAAY,CAAE,CAAC,CACfC,QAAQ,CAAE,UACZ,CAAE,CACFC,OAAO,CAAGC,CAAC,EAAKpD,mBAAmB,CAACoD,CAAC,CAAElD,OAAO,CAAE,CAAA0B,QAAA,eAEhDzF,IAAA,CAACvB,UAAU,EAACyH,OAAO,CAAC,OAAO,CAAAT,QAAA,CAAE1B,OAAO,CAACf,OAAO,CAAa,CAAC,CAEzDe,OAAO,CAACmD,SAAS,EAAInD,OAAO,CAACmD,SAAS,CAACP,MAAM,CAAG,CAAC,eAChD3G,IAAA,CAACzB,GAAG,EAAC8G,EAAE,CAAE,CAAEK,OAAO,CAAE,MAAM,CAAEC,GAAG,CAAE,GAAG,CAAEwB,EAAE,CAAE,CAAC,CAAEC,QAAQ,CAAE,MAAO,CAAE,CAAA3B,QAAA,CAC7D1B,OAAO,CAACmD,SAAS,CAACG,GAAG,CAAC,CAACC,QAAQ,CAAEC,GAAG,gBACnCvH,IAAA,CAACjB,IAAI,EAEHyI,KAAK,IAAAZ,MAAA,CAAKU,QAAQ,CAAClD,KAAK,MAAAwC,MAAA,CAAIU,QAAQ,CAACG,KAAK,CAAG,CAC7CC,IAAI,CAAC,OAAO,CACZxB,OAAO,CAAC,UAAU,CAClBc,OAAO,CAAEA,CAAA,GAAM7C,iBAAiB,CAACmD,QAAQ,CAAClD,KAAK,CAAE,CACjDiB,EAAE,CAAE,CAAEW,MAAM,CAAE,EAAE,CAAE2B,QAAQ,CAAE,QAAS,CAAE,EALlCJ,GAMN,CACF,CAAC,CACC,CACN,cAEDvH,IAAA,CAACvB,UAAU,EACTyH,OAAO,CAAC,SAAS,CACjBb,EAAE,CAAE,CACFK,OAAO,CAAE,OAAO,CAChByB,EAAE,CAAE,GAAG,CACPT,OAAO,CAAE,GAAG,CACZiB,QAAQ,CAAE,QACZ,CAAE,CAAAlC,QAAA,CAEDnB,iBAAiB,CAACP,OAAO,CAACQ,SAAS,CAAC,CAC3B,CAAC,EACR,CAAC,EACL,CAAC,EACH,CAAC,EAnGDR,OAAO,CAACb,EAoGL,CAAC,CAEf,CAAC,CAED,GAAIlC,OAAO,CAAE,CACX,mBACEhB,IAAA,CAACzB,GAAG,EAACmH,OAAO,CAAC,MAAM,CAACkC,cAAc,CAAC,QAAQ,CAACrC,UAAU,CAAC,QAAQ,CAACS,MAAM,CAAC,OAAO,CAAAP,QAAA,cAC5EzF,IAAA,CAACb,gBAAgB,GAAE,CAAC,CACjB,CAAC,CAEV,CAEA,mBACEe,KAAA,CAAC3B,GAAG,EAAC8G,EAAE,CAAE,CAAEW,MAAM,CAAE,MAAM,CAAEN,OAAO,CAAE,MAAM,CAAEJ,aAAa,CAAE,QAAS,CAAE,CAAAG,QAAA,eAEpEvF,KAAA,CAAC1B,KAAK,EACJ6G,EAAE,CAAE,CACFgB,CAAC,CAAE,CAAC,CACJS,YAAY,CAAE,CAAC,CACfe,YAAY,CAAE,CAAC,CACfrB,WAAW,CAAE,SAAS,CACtBd,OAAO,CAAE,MAAM,CACfH,UAAU,CAAE,QAAQ,CACpBqC,cAAc,CAAE,eAClB,CAAE,CAAAnC,QAAA,eAEFvF,KAAA,CAAC3B,GAAG,EAAC8G,EAAE,CAAE,CAAEK,OAAO,CAAE,MAAM,CAAEH,UAAU,CAAE,QAAQ,CAAEI,GAAG,CAAE,CAAE,CAAE,CAAAF,QAAA,eACzDzF,IAAA,CAACpB,MAAM,EAAA6G,QAAA,CAAE,EAAAlF,UAAA,CAAAE,IAAI,CAACqH,IAAI,UAAAvH,UAAA,iBAATA,UAAA,CAAY,CAAC,CAAC,GAAI,GAAG,CAAS,CAAC,cACxCL,KAAA,CAAC3B,GAAG,EAAAkH,QAAA,eACFzF,IAAA,CAACvB,UAAU,EAACyH,OAAO,CAAC,IAAI,CAAAT,QAAA,CAAEhF,IAAI,CAACqH,IAAI,EAAI,YAAY,CAAa,CAAC,cACjE5H,KAAA,CAACzB,UAAU,EAACyH,OAAO,CAAC,SAAS,CAACC,KAAK,CAAC,gBAAgB,CAAAV,QAAA,EACjDhF,IAAI,CAACsH,YAAY,CAACpB,MAAM,CAAC,UAC1B,CAACvF,WAAW,CAACuF,MAAM,CAAG,CAAC,eACrBzG,KAAA,SAAAuF,QAAA,EAAM,UAAG,CAACrE,WAAW,CAACiG,GAAG,CAACW,CAAC,EAAIA,CAAC,CAACC,QAAQ,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC,YAAU,EAAM,CACvE,EACS,CAAC,EACV,CAAC,EACH,CAAC,CAELxH,OAAO,eACNV,IAAA,CAACrB,UAAU,EAACqI,OAAO,CAAEtG,OAAQ,CAAA+E,QAAA,cAC3BzF,IAAA,CAACL,KAAK,GAAE,CAAC,CACC,CACb,EACI,CAAC,cAGRO,KAAA,CAAC3B,GAAG,EAAC8G,EAAE,CAAE,CAAE8C,QAAQ,CAAE,CAAC,CAAEC,QAAQ,CAAE,MAAM,CAAE/B,CAAC,CAAE,CAAE,CAAE,CAAAZ,QAAA,eAC/CzF,IAAA,CAACnB,IAAI,EAACwG,EAAE,CAAE,CAAEG,EAAE,CAAE,CAAE,CAAE,CAAAC,QAAA,CACjB7E,QAAQ,CAACyG,GAAG,CAAC,CAACtD,OAAO,CAAEc,KAAK,GAAKD,aAAa,CAACb,OAAO,CAAEc,KAAK,CAAC,CAAC,CAC5D,CAAC,cACP7E,IAAA,QAAKqI,GAAG,CAAEvG,cAAe,CAAE,CAAC,EACzB,CAAC,CAGLJ,OAAO,eACN1B,IAAA,CAACxB,KAAK,EAAC6G,EAAE,CAAE,CAAEgB,CAAC,CAAE,CAAC,CAAEjB,CAAC,CAAE,CAAC,CAAEkB,OAAO,CAAE,cAAe,CAAE,CAAAb,QAAA,cACjDvF,KAAA,CAAC3B,GAAG,EAAC8G,EAAE,CAAE,CAAEK,OAAO,CAAE,MAAM,CAAEkC,cAAc,CAAE,eAAe,CAAErC,UAAU,CAAE,QAAS,CAAE,CAAAE,QAAA,eAClFvF,KAAA,CAAC3B,GAAG,EAAAkH,QAAA,eACFvF,KAAA,CAACzB,UAAU,EAACyH,OAAO,CAAC,SAAS,CAACC,KAAK,CAAC,gBAAgB,CAAAV,QAAA,EAAC,cACvC,CAAC/D,OAAO,CAACuE,UAAU,EACrB,CAAC,cACbjG,IAAA,CAACvB,UAAU,EAACyH,OAAO,CAAC,OAAO,CAAAT,QAAA,CACxB/D,OAAO,CAACsB,OAAO,CAAC2D,MAAM,CAAG,EAAE,IAAAC,MAAA,CACrBlF,OAAO,CAACsB,OAAO,CAAC6D,SAAS,CAAC,CAAC,CAAE,EAAE,CAAC,QACnCnF,OAAO,CAACsB,OAAO,CACT,CAAC,EACV,CAAC,cACNhD,IAAA,CAACrB,UAAU,EAAC+I,IAAI,CAAC,OAAO,CAACV,OAAO,CAAEA,CAAA,GAAMrF,UAAU,CAAC,IAAI,CAAE,CAAA8D,QAAA,cACvDzF,IAAA,CAACL,KAAK,GAAE,CAAC,CACC,CAAC,EACV,CAAC,CACD,CACR,cAGDK,IAAA,CAACxB,KAAK,EAAC6G,EAAE,CAAE,CAAEgB,CAAC,CAAE,CAAC,CAAES,YAAY,CAAE,CAAE,CAAE,CAAArB,QAAA,cACnCvF,KAAA,CAAC3B,GAAG,EAAC8G,EAAE,CAAE,CAAEK,OAAO,CAAE,MAAM,CAAEC,GAAG,CAAE,CAAC,CAAEJ,UAAU,CAAE,UAAW,CAAE,CAAAE,QAAA,eAC3DzF,IAAA,CAACtB,SAAS,EACR4J,SAAS,MACTC,SAAS,MACTC,OAAO,CAAE,CAAE,CACXC,WAAW,CAAC,mBAAmB,CAC/B/E,KAAK,CAAE5C,UAAW,CAClB4H,QAAQ,CAAGzB,CAAC,EAAKxD,YAAY,CAACwD,CAAC,CAAC0B,MAAM,CAACjF,KAAK,CAAE,CAC9CkF,UAAU,CAAG3B,CAAC,EAAK,CACjB,GAAIA,CAAC,CAAC4B,GAAG,GAAK,OAAO,EAAI,CAAC5B,CAAC,CAAC6B,QAAQ,CAAE,CACpC7B,CAAC,CAAC8B,cAAc,CAAC,CAAC,CAClBnG,iBAAiB,CAAC,CAAC,CACrB,CACF,CAAE,CACFoG,UAAU,CAAE,CACVC,YAAY,cACV/I,KAAA,CAACd,cAAc,EAAC2H,QAAQ,CAAC,KAAK,CAAAtB,QAAA,eAC5BzF,IAAA,CAACrB,UAAU,EAAC+I,IAAI,CAAC,OAAO,CAAAjC,QAAA,cACtBzF,IAAA,CAACV,UAAU,GAAE,CAAC,CACJ,CAAC,cACbU,IAAA,CAACrB,UAAU,EAAC+I,IAAI,CAAC,OAAO,CAAAjC,QAAA,cACtBzF,IAAA,CAACT,aAAa,GAAE,CAAC,CACP,CAAC,EACC,CAEpB,CAAE,CACH,CAAC,cACFS,IAAA,CAACrB,UAAU,EACTwH,KAAK,CAAC,SAAS,CACfa,OAAO,CAAEpE,iBAAkB,CAC3BsG,QAAQ,CAAE,CAACpI,UAAU,CAAC+B,IAAI,CAAC,CAAC,EAAI3B,OAAQ,CAAAuE,QAAA,CAEvCvE,OAAO,cAAGlB,IAAA,CAACb,gBAAgB,EAACuI,IAAI,CAAE,EAAG,CAAE,CAAC,cAAG1H,IAAA,CAACX,IAAI,GAAE,CAAC,CAC1C,CAAC,EACV,CAAC,CACD,CAAC,cAGRa,KAAA,CAAClB,IAAI,EACHsC,QAAQ,CAAEA,QAAS,CACnB6H,IAAI,CAAEC,OAAO,CAAC9H,QAAQ,CAAE,CACxBZ,OAAO,CAAEuD,eAAgB,CAAAwB,QAAA,eAEzBvF,KAAA,CAACjB,QAAQ,EAAC+H,OAAO,CAAE9C,WAAY,CAAAuB,QAAA,eAC7BzF,IAAA,CAACR,KAAK,EAAC6F,EAAE,CAAE,CAAEgE,EAAE,CAAE,CAAE,CAAE,CAAE,CAAC,QAE1B,EAAU,CAAC,cACXrJ,IAAA,CAACf,QAAQ,EAAC+H,OAAO,CAAEA,CAAA,GAAM7C,iBAAiB,CAAC,IAAI,CAAE,CAAAsB,QAAA,CAAC,mBAElD,CAAU,CAAC,cACXzF,IAAA,CAACf,QAAQ,EAAC+H,OAAO,CAAEA,CAAA,GAAM7C,iBAAiB,CAAC,IAAI,CAAE,CAAAsB,QAAA,CAAC,mBAElD,CAAU,CAAC,cACXzF,IAAA,CAACf,QAAQ,EAAC+H,OAAO,CAAEA,CAAA,GAAM7C,iBAAiB,CAAC,IAAI,CAAE,CAAAsB,QAAA,CAAC,oBAElD,CAAU,CAAC,CACV,CAAAjE,eAAe,SAAfA,eAAe,iBAAfA,eAAe,CAAEwD,QAAQ,KAAKrE,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAE2B,GAAG,gBACtCpC,KAAA,CAAAE,SAAA,EAAAqF,QAAA,eACEzF,IAAA,CAACd,OAAO,GAAE,CAAC,cACXgB,KAAA,CAACjB,QAAQ,EAAC+H,OAAO,CAAEA,CAAA,GAAMnF,iBAAiB,CAACL,eAAe,CAAE,CAAAiE,QAAA,eAC1DzF,IAAA,CAACN,IAAI,EAAC2F,EAAE,CAAE,CAAEgE,EAAE,CAAE,CAAE,CAAE,CAAE,CAAC,OAEzB,EAAU,CAAC,cACXnJ,KAAA,CAACjB,QAAQ,EAACoG,EAAE,CAAE,CAAEc,KAAK,CAAE,YAAa,CAAE,CAAAV,QAAA,eACpCzF,IAAA,CAACP,MAAM,EAAC4F,EAAE,CAAE,CAAEgE,EAAE,CAAE,CAAE,CAAE,CAAE,CAAC,SAE3B,EAAU,CAAC,EACX,CACH,EACG,CAAC,EACJ,CAAC,CAEV,CAAC,CAED,cAAe,CAAAhJ,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}