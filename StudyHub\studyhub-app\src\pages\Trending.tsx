import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON><PERSON>,
  Grid,
  Card,
  CardContent,
  Typography,
  Box,
  Avatar,
  Chip,
  Paper,
  LinearProgress,
  Tab,
  Tabs,
  List,
  ListItem,
  ListItemAvatar,
  ListItemText,
  Divider,
} from '@mui/material';
import {
  TrendingUp,
  Whatshot,
  EmojiEvents,
  Timeline,
} from '@mui/icons-material';

interface TrendingTopic {
  id: string;
  name: string;
  postCount: number;
  growth: number;
  category: string;
  rank: number;
}

interface TrendingPost {
  id: string;
  authorName: string;
  authorAvatar?: string;
  content: string;
  likes: number;
  comments: number;
  shares: number;
  engagement: number;
  tags: string[];
  timestamp: Date;
}

interface TrendingUser {
  id: string;
  name: string;
  avatar?: string;
  followers: number;
  growthRate: number;
  category: string;
}

const Trending: React.FC = () => {
  const [activeTab, setActiveTab] = useState(0);
  const [trendingTopics, setTrendingTopics] = useState<TrendingTopic[]>([]);
  const [trendingPosts, setTrendingPosts] = useState<TrendingPost[]>([]);
  const [trendingUsers, setTrendingUsers] = useState<TrendingUser[]>([]);

  useEffect(() => {
    // Sample trending topics
    setTrendingTopics([
      { id: '1', name: 'ClimateChange', postCount: 1247, growth: 45.2, category: 'Environment', rank: 1 },
      { id: '2', name: 'AI', postCount: 892, growth: 38.7, category: 'Technology', rank: 2 },
      { id: '3', name: 'WorkFromHome', postCount: 634, growth: 28.3, category: 'Lifestyle', rank: 3 },
      { id: '4', name: 'MentalHealth', postCount: 567, growth: 25.1, category: 'Wellness', rank: 4 },
      { id: '5', name: 'Cryptocurrency', postCount: 445, growth: 22.8, category: 'Finance', rank: 5 },
      { id: '6', name: 'SustainableLiving', postCount: 389, growth: 19.4, category: 'Environment', rank: 6 },
      { id: '7', name: 'RemoteLearning', postCount: 356, growth: 17.2, category: 'Education', rank: 7 },
      { id: '8', name: 'DigitalArt', postCount: 298, growth: 15.8, category: 'Arts', rank: 8 },
      { id: '9', name: 'Mindfulness', postCount: 267, growth: 14.3, category: 'Wellness', rank: 9 },
      { id: '10', name: 'TechStartups', postCount: 234, growth: 12.7, category: 'Technology', rank: 10 },
    ]);

    // Sample trending posts
    setTrendingPosts([
      {
        id: '1',
        authorName: 'Dr. Sarah Chen',
        content: 'The latest IPCC report shows we have less time than we thought. Here\'s what we can do about it...',
        likes: 1247,
        comments: 89,
        shares: 156,
        engagement: 92.5,
        tags: ['climate', 'science', 'action'],
        timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000),
      },
      {
        id: '2',
        authorName: 'Tech Innovator Mike',
        content: 'GPT-5 rumors are heating up. What features do you think will be game-changers?',
        likes: 892,
        comments: 234,
        shares: 67,
        engagement: 87.3,
        tags: ['AI', 'GPT', 'technology'],
        timestamp: new Date(Date.now() - 4 * 60 * 60 * 1000),
      },
      {
        id: '3',
        authorName: 'Wellness Coach Emma',
        content: 'Remote work burnout is real. Here are 5 strategies that actually work...',
        likes: 634,
        comments: 145,
        shares: 89,
        engagement: 78.9,
        tags: ['wellness', 'remote', 'productivity'],
        timestamp: new Date(Date.now() - 6 * 60 * 60 * 1000),
      },
    ]);

    // Sample trending users
    setTrendingUsers([
      {
        id: '1',
        name: 'Dr. Sarah Chen',
        followers: 15420,
        growthRate: 23.4,
        category: 'Science',
      },
      {
        id: '2',
        name: 'Tech Innovator Mike',
        followers: 8930,
        growthRate: 18.7,
        category: 'Technology',
      },
      {
        id: '3',
        name: 'Wellness Coach Emma',
        followers: 12100,
        growthRate: 16.2,
        category: 'Wellness',
      },
    ]);
  }, []);

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue);
  };

  const formatNumber = (num: number) => {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M';
    }
    if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K';
    }
    return num.toString();
  };

  const formatTimeAgo = (timestamp: Date) => {
    const now = new Date();
    const diffInHours = Math.floor((now.getTime() - timestamp.getTime()) / (1000 * 60 * 60));
    
    if (diffInHours < 1) return 'Just now';
    if (diffInHours < 24) return `${diffInHours}h ago`;
    return `${Math.floor(diffInHours / 24)}d ago`;
  };

  const getGrowthColor = (growth: number) => {
    if (growth >= 30) return 'success';
    if (growth >= 20) return 'warning';
    return 'info';
  };

  return (
    <Container maxWidth="lg">
      <Box sx={{ py: 3 }}>
        <Typography variant="h4" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <TrendingUp />
          Trending Now
        </Typography>
        <Typography variant="body1" color="text.secondary" sx={{ mb: 4 }}>
          Discover what's hot and trending across all communities
        </Typography>

        {/* Tabs */}
        <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}>
          <Tabs value={activeTab} onChange={handleTabChange}>
            <Tab label="Topics" icon={<Whatshot />} />
            <Tab label="Posts" icon={<EmojiEvents />} />
            <Tab label="People" icon={<Timeline />} />
          </Tabs>
        </Box>

        {/* Topics Tab */}
        {activeTab === 0 && (
          <Grid container spacing={3}>
            <Grid item xs={12} md={8}>
              <Typography variant="h6" gutterBottom>
                Trending Topics
              </Typography>
              {trendingTopics.map((topic) => (
                <Card key={topic.id} sx={{ mb: 2 }}>
                  <CardContent>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                        <Typography variant="h4" color="primary" fontWeight="bold">
                          #{topic.rank}
                        </Typography>
                        <Box>
                          <Typography variant="h6" fontWeight="bold">
                            #{topic.name}
                          </Typography>
                          <Typography variant="body2" color="text.secondary">
                            {topic.category} • {formatNumber(topic.postCount)} posts
                          </Typography>
                        </Box>
                      </Box>
                      <Box sx={{ textAlign: 'right' }}>
                        <Chip
                          label={`+${topic.growth}%`}
                          color={getGrowthColor(topic.growth)}
                          size="small"
                          icon={<TrendingUp />}
                        />
                        <Typography variant="caption" color="text.secondary" sx={{ display: 'block', mt: 1 }}>
                          24h growth
                        </Typography>
                      </Box>
                    </Box>
                  </CardContent>
                </Card>
              ))}
            </Grid>

            <Grid item xs={12} md={4}>
              <Paper sx={{ p: 3 }}>
                <Typography variant="h6" gutterBottom>
                  Category Breakdown
                </Typography>
                {['Technology', 'Environment', 'Wellness', 'Finance', 'Education'].map((category) => {
                  const categoryTopics = trendingTopics.filter(t => t.category === category);
                  const percentage = (categoryTopics.length / trendingTopics.length) * 100;
                  
                  return (
                    <Box key={category} sx={{ mb: 2 }}>
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                        <Typography variant="body2">{category}</Typography>
                        <Typography variant="body2" color="text.secondary">
                          {percentage.toFixed(0)}%
                        </Typography>
                      </Box>
                      <LinearProgress
                        variant="determinate"
                        value={percentage}
                        sx={{ height: 8, borderRadius: 4 }}
                      />
                    </Box>
                  );
                })}
              </Paper>
            </Grid>
          </Grid>
        )}

        {/* Posts Tab */}
        {activeTab === 1 && (
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <Typography variant="h6" gutterBottom>
                Most Engaging Posts
              </Typography>
              {trendingPosts.map((post) => (
                <Card key={post.id} sx={{ mb: 2 }}>
                  <CardContent>
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                      <Avatar src={post.authorAvatar} sx={{ mr: 2 }}>
                        {post.authorName.split(' ').map(n => n[0]).join('')}
                      </Avatar>
                      <Box sx={{ flexGrow: 1 }}>
                        <Typography variant="subtitle1" fontWeight="bold">
                          {post.authorName}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          {formatTimeAgo(post.timestamp)}
                        </Typography>
                      </Box>
                      <Chip
                        label={`${post.engagement}% engagement`}
                        color="primary"
                        size="small"
                      />
                    </Box>

                    <Typography variant="body1" sx={{ mb: 2 }}>
                      {post.content}
                    </Typography>

                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                      <Box>
                        {post.tags.map((tag) => (
                          <Chip
                            key={tag}
                            label={`#${tag}`}
                            size="small"
                            sx={{ mr: 1 }}
                            color="primary"
                            variant="outlined"
                          />
                        ))}
                      </Box>
                      <Box sx={{ display: 'flex', gap: 3 }}>
                        <Typography variant="caption" color="text.secondary">
                          {formatNumber(post.likes)} likes
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          {formatNumber(post.comments)} comments
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          {formatNumber(post.shares)} shares
                        </Typography>
                      </Box>
                    </Box>
                  </CardContent>
                </Card>
              ))}
            </Grid>
          </Grid>
        )}

        {/* People Tab */}
        {activeTab === 2 && (
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <Typography variant="h6" gutterBottom>
                Fastest Growing Creators
              </Typography>
              <Card>
                <List>
                  {trendingUsers.map((user, index) => (
                    <React.Fragment key={user.id}>
                      <ListItem>
                        <ListItemAvatar>
                          <Avatar src={user.avatar}>
                            {user.name.split(' ').map(n => n[0]).join('')}
                          </Avatar>
                        </ListItemAvatar>
                        <ListItemText
                          primary={
                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                              <Typography variant="subtitle1" fontWeight="bold">
                                {user.name}
                              </Typography>
                              <Chip
                                label={user.category}
                                size="small"
                                color="primary"
                                variant="outlined"
                              />
                            </Box>
                          }
                          secondary={`${formatNumber(user.followers)} followers`}
                        />
                        <Box sx={{ textAlign: 'right' }}>
                          <Chip
                            label={`+${user.growthRate}%`}
                            color="success"
                            size="small"
                            icon={<TrendingUp />}
                          />
                          <Typography variant="caption" color="text.secondary" sx={{ display: 'block', mt: 1 }}>
                            7d growth
                          </Typography>
                        </Box>
                      </ListItem>
                      {index < trendingUsers.length - 1 && <Divider />}
                    </React.Fragment>
                  ))}
                </List>
              </Card>
            </Grid>
          </Grid>
        )}
      </Box>
    </Container>
  );
};

export default Trending;
