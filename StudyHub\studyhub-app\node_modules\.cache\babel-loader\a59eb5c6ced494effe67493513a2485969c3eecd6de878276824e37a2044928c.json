{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\\\u30C9\\u30AD\\u30E5\\u30E1\\u30F3\\u30C8\\\\Projects\\\\StudyHub\\\\studyhub-app\\\\src\\\\components\\\\Chat\\\\GroupChat.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from 'react';\nimport { Box, Paper, Typography, TextField, IconButton, Avatar, List, ListItem, Chip, Menu, MenuItem, Divider, CircularProgress, InputAdornment } from '@mui/material';\nimport { Send, AttachFile, EmojiEmotions, Reply, Delete, Edit, Close } from '@mui/icons-material';\nimport { formatDistanceToNow } from 'date-fns';\nimport { ChatService } from '../../services/chatService';\nimport { useAuth } from '../../contexts/AuthContext';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst GroupChat = ({\n  chatId,\n  chat,\n  onClose\n}) => {\n  _s();\n  var _chat$name;\n  const {\n    user\n  } = useAuth();\n  const [messages, setMessages] = useState([]);\n  const [newMessage, setNewMessage] = useState('');\n  const [loading, setLoading] = useState(true);\n  const [sending, setSending] = useState(false);\n  const [typingUsers, setTypingUsers] = useState([]);\n  const [anchorEl, setAnchorEl] = useState(null);\n  const [selectedMessage, setSelectedMessage] = useState(null);\n  const [replyTo, setReplyTo] = useState(null);\n  const [editingMessage, setEditingMessage] = useState(null);\n  const messagesEndRef = useRef(null);\n  const typingTimeoutRef = useRef(null);\n  useEffect(() => {\n    if (!chatId || !user) return;\n\n    // Subscribe to messages\n    const unsubscribeMessages = ChatService.subscribeToMessages(chatId, newMessages => {\n      setMessages(newMessages);\n      setLoading(false);\n      scrollToBottom();\n    });\n\n    // Subscribe to typing indicators\n    const unsubscribeTyping = ChatService.subscribeToTyping(chatId, user.uid, setTypingUsers);\n\n    // Mark messages as read\n    ChatService.markAsRead(chatId, user.uid);\n    return () => {\n      unsubscribeMessages();\n      unsubscribeTyping();\n    };\n  }, [chatId, user]);\n  const scrollToBottom = () => {\n    var _messagesEndRef$curre;\n    (_messagesEndRef$curre = messagesEndRef.current) === null || _messagesEndRef$curre === void 0 ? void 0 : _messagesEndRef$curre.scrollIntoView({\n      behavior: 'smooth'\n    });\n  };\n  const handleSendMessage = async () => {\n    if (!newMessage.trim() || !user || sending) return;\n    setSending(true);\n    try {\n      var _user$email;\n      await ChatService.sendMessage(chatId, user.uid, {\n        content: newMessage.trim(),\n        type: 'text',\n        replyTo: replyTo === null || replyTo === void 0 ? void 0 : replyTo.id\n      }, user.displayName || ((_user$email = user.email) === null || _user$email === void 0 ? void 0 : _user$email.split('@')[0]) || 'User');\n      setNewMessage('');\n      setReplyTo(null);\n\n      // Stop typing indicator\n      await ChatService.setTyping(chatId, user.uid, user.displayName || '', false);\n    } catch (error) {\n      console.error('Error sending message:', error);\n    } finally {\n      setSending(false);\n    }\n  };\n  const handleTyping = async value => {\n    setNewMessage(value);\n    if (!user) return;\n\n    // Clear existing timeout\n    if (typingTimeoutRef.current) {\n      clearTimeout(typingTimeoutRef.current);\n    }\n    if (value.trim()) {\n      // Set typing indicator\n      await ChatService.setTyping(chatId, user.uid, user.displayName || '', true);\n\n      // Clear typing after 3 seconds of inactivity\n      typingTimeoutRef.current = setTimeout(async () => {\n        await ChatService.setTyping(chatId, user.uid, user.displayName || '', false);\n      }, 3000);\n    } else {\n      // Clear typing immediately if input is empty\n      await ChatService.setTyping(chatId, user.uid, user.displayName || '', false);\n    }\n  };\n  const handleMessageAction = (event, message) => {\n    setAnchorEl(event.currentTarget);\n    setSelectedMessage(message);\n  };\n  const handleCloseMenu = () => {\n    setAnchorEl(null);\n    setSelectedMessage(null);\n  };\n  const handleReply = () => {\n    setReplyTo(selectedMessage);\n    handleCloseMenu();\n  };\n  const handleAddReaction = async emoji => {\n    if (!selectedMessage || !user) return;\n    try {\n      await ChatService.addReaction(chatId, selectedMessage.id, user.uid, emoji);\n      handleCloseMenu();\n    } catch (error) {\n      console.error('Error adding reaction:', error);\n    }\n  };\n  const formatMessageTime = timestamp => {\n    if (!timestamp) return '';\n    const date = timestamp.toDate ? timestamp.toDate() : new Date(timestamp);\n    return formatDistanceToNow(date, {\n      addSuffix: true\n    });\n  };\n  const renderMessage = (message, index) => {\n    var _messages;\n    const isOwnMessage = message.senderId === (user === null || user === void 0 ? void 0 : user.uid);\n    const showAvatar = index === 0 || ((_messages = messages[index - 1]) === null || _messages === void 0 ? void 0 : _messages.senderId) !== message.senderId;\n    const replyToMessage = message.replyTo ? messages.find(m => m.id === message.replyTo) : null;\n    return /*#__PURE__*/_jsxDEV(ListItem, {\n      sx: {\n        flexDirection: 'column',\n        alignItems: isOwnMessage ? 'flex-end' : 'flex-start',\n        py: 0.5\n      },\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          flexDirection: isOwnMessage ? 'row-reverse' : 'row',\n          alignItems: 'flex-end',\n          gap: 1,\n          maxWidth: '70%'\n        },\n        children: [showAvatar && !isOwnMessage && /*#__PURE__*/_jsxDEV(Avatar, {\n          src: message.senderAvatar,\n          sx: {\n            width: 32,\n            height: 32\n          },\n          children: message.senderName[0]\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 195,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            flexDirection: 'column',\n            gap: 0.5\n          },\n          children: [showAvatar && !isOwnMessage && /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"caption\",\n            color: \"text.secondary\",\n            sx: {\n              px: 1\n            },\n            children: message.senderName\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 211,\n            columnNumber: 15\n          }, this), replyToMessage && /*#__PURE__*/_jsxDEV(Paper, {\n            sx: {\n              p: 1,\n              bgcolor: 'action.hover',\n              borderLeft: 3,\n              borderColor: 'primary.main',\n              mb: 0.5\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              color: \"text.secondary\",\n              children: [\"Replying to \", replyToMessage.senderName]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 226,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              sx: {\n                opacity: 0.7\n              },\n              children: replyToMessage.content.length > 50 ? `${replyToMessage.content.substring(0, 50)}...` : replyToMessage.content\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 229,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 217,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Paper, {\n            sx: {\n              p: 1.5,\n              bgcolor: isOwnMessage ? 'primary.main' : 'background.paper',\n              color: isOwnMessage ? 'primary.contrastText' : 'text.primary',\n              borderRadius: 2,\n              position: 'relative'\n            },\n            onClick: e => handleMessageAction(e, message),\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              children: message.content\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 247,\n              columnNumber: 15\n            }, this), message.reactions && message.reactions.length > 0 && /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                gap: 0.5,\n                mt: 1,\n                flexWrap: 'wrap'\n              },\n              children: message.reactions.map((reaction, idx) => /*#__PURE__*/_jsxDEV(Chip, {\n                label: `${reaction.emoji} ${reaction.count}`,\n                size: \"small\",\n                variant: \"outlined\",\n                onClick: () => handleAddReaction(reaction.emoji),\n                sx: {\n                  height: 20,\n                  fontSize: '0.7rem'\n                }\n              }, idx, false, {\n                fileName: _jsxFileName,\n                lineNumber: 252,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 250,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              sx: {\n                display: 'block',\n                mt: 0.5,\n                opacity: 0.7,\n                fontSize: '0.7rem'\n              },\n              children: formatMessageTime(message.timestamp)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 264,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 237,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 203,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 185,\n        columnNumber: 9\n      }, this)\n    }, message.id, false, {\n      fileName: _jsxFileName,\n      lineNumber: 177,\n      columnNumber: 7\n    }, this);\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(Box, {\n      display: \"flex\",\n      justifyContent: \"center\",\n      alignItems: \"center\",\n      height: \"400px\",\n      children: /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 285,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 284,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      height: '100%',\n      display: 'flex',\n      flexDirection: 'column'\n    },\n    children: [/*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        p: 2,\n        borderRadius: 0,\n        borderBottom: 1,\n        borderColor: 'divider',\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'space-between'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          alignItems: 'center',\n          gap: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(Avatar, {\n          children: ((_chat$name = chat.name) === null || _chat$name === void 0 ? void 0 : _chat$name[0]) || 'G'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 305,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            children: chat.name || 'Group Chat'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 307,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"caption\",\n            color: \"text.secondary\",\n            children: [chat.participants.length, \" members\", typingUsers.length > 0 && /*#__PURE__*/_jsxDEV(\"span\", {\n              children: [\" \\u2022 \", typingUsers.map(t => t.userName).join(', '), \" typing...\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 311,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 308,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 306,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 304,\n        columnNumber: 9\n      }, this), onClose && /*#__PURE__*/_jsxDEV(IconButton, {\n        onClick: onClose,\n        children: /*#__PURE__*/_jsxDEV(Close, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 319,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 318,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 293,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        flexGrow: 1,\n        overflow: 'auto',\n        p: 1\n      },\n      children: [/*#__PURE__*/_jsxDEV(List, {\n        sx: {\n          py: 0\n        },\n        children: messages.map((message, index) => renderMessage(message, index))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 326,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        ref: messagesEndRef\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 329,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 325,\n      columnNumber: 7\n    }, this), replyTo && /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        p: 1,\n        m: 1,\n        bgcolor: 'action.hover'\n      },\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          justifyContent: 'space-between',\n          alignItems: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"caption\",\n            color: \"text.secondary\",\n            children: [\"Replying to \", replyTo.senderName]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 337,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            children: replyTo.content.length > 50 ? `${replyTo.content.substring(0, 50)}...` : replyTo.content\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 340,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 336,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n          size: \"small\",\n          onClick: () => setReplyTo(null),\n          children: /*#__PURE__*/_jsxDEV(Close, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 347,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 346,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 335,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 334,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        p: 2,\n        borderRadius: 0\n      },\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          gap: 1,\n          alignItems: 'flex-end'\n        },\n        children: [/*#__PURE__*/_jsxDEV(TextField, {\n          fullWidth: true,\n          multiline: true,\n          maxRows: 4,\n          placeholder: \"Type a message...\",\n          value: newMessage,\n          onChange: e => handleTyping(e.target.value),\n          onKeyPress: e => {\n            if (e.key === 'Enter' && !e.shiftKey) {\n              e.preventDefault();\n              handleSendMessage();\n            }\n          },\n          InputProps: {\n            endAdornment: /*#__PURE__*/_jsxDEV(InputAdornment, {\n              position: \"end\",\n              children: [/*#__PURE__*/_jsxDEV(IconButton, {\n                size: \"small\",\n                children: /*#__PURE__*/_jsxDEV(AttachFile, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 373,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 372,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n                size: \"small\",\n                children: /*#__PURE__*/_jsxDEV(EmojiEmotions, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 376,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 375,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 371,\n              columnNumber: 17\n            }, this)\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 356,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n          color: \"primary\",\n          onClick: handleSendMessage,\n          disabled: !newMessage.trim() || sending,\n          children: sending ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n            size: 20\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 387,\n            columnNumber: 24\n          }, this) : /*#__PURE__*/_jsxDEV(Send, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 387,\n            columnNumber: 57\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 382,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 355,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 354,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Menu, {\n      anchorEl: anchorEl,\n      open: Boolean(anchorEl),\n      onClose: handleCloseMenu,\n      children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n        onClick: handleReply,\n        children: [/*#__PURE__*/_jsxDEV(Reply, {\n          sx: {\n            mr: 1\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 399,\n          columnNumber: 11\n        }, this), \"Reply\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 398,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n        onClick: () => handleAddReaction('👍'),\n        children: \"\\uD83D\\uDC4D Like\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 402,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n        onClick: () => handleAddReaction('❤️'),\n        children: \"\\u2764\\uFE0F Love\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 405,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n        onClick: () => handleAddReaction('😂'),\n        children: \"\\uD83D\\uDE02 Laugh\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 408,\n        columnNumber: 9\n      }, this), (selectedMessage === null || selectedMessage === void 0 ? void 0 : selectedMessage.senderId) === (user === null || user === void 0 ? void 0 : user.uid) && /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 413,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n          onClick: () => setEditingMessage(selectedMessage),\n          children: [/*#__PURE__*/_jsxDEV(Edit, {\n            sx: {\n              mr: 1\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 415,\n            columnNumber: 15\n          }, this), \"Edit\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 414,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n          sx: {\n            color: 'error.main'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Delete, {\n            sx: {\n              mr: 1\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 419,\n            columnNumber: 15\n          }, this), \"Delete\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 418,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 393,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 291,\n    columnNumber: 5\n  }, this);\n};\n_s(GroupChat, \"BOHKR0xrBb8+5vOYMpO5lHTY66M=\", false, function () {\n  return [useAuth];\n});\n_c = GroupChat;\nexport default GroupChat;\nvar _c;\n$RefreshReg$(_c, \"GroupChat\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "Box", "Paper", "Typography", "TextField", "IconButton", "Avatar", "List", "ListItem", "Chip", "<PERSON><PERSON>", "MenuItem", "Divider", "CircularProgress", "InputAdornment", "Send", "AttachFile", "EmojiEmotions", "Reply", "Delete", "Edit", "Close", "formatDistanceToNow", "ChatService", "useAuth", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "GroupChat", "chatId", "chat", "onClose", "_s", "_chat$name", "user", "messages", "setMessages", "newMessage", "setNewMessage", "loading", "setLoading", "sending", "setSending", "typingUsers", "setTypingUsers", "anchorEl", "setAnchorEl", "selectedMessage", "setSelectedMessage", "replyTo", "setReplyTo", "editingMessage", "setEditingMessage", "messagesEndRef", "typingTimeoutRef", "unsubscribeMessages", "subscribeToMessages", "newMessages", "scrollToBottom", "unsubscribeTyping", "subscribeToTyping", "uid", "mark<PERSON><PERSON><PERSON>", "_messagesEndRef$curre", "current", "scrollIntoView", "behavior", "handleSendMessage", "trim", "_user$email", "sendMessage", "content", "type", "id", "displayName", "email", "split", "setTyping", "error", "console", "handleTyping", "value", "clearTimeout", "setTimeout", "handleMessageAction", "event", "message", "currentTarget", "handleCloseMenu", "handleReply", "handleAddReaction", "emoji", "addReaction", "formatMessageTime", "timestamp", "date", "toDate", "Date", "addSuffix", "renderMessage", "index", "_messages", "isOwnMessage", "senderId", "showAvatar", "replyToMessage", "find", "m", "sx", "flexDirection", "alignItems", "py", "children", "display", "gap", "max<PERSON><PERSON><PERSON>", "src", "senderAvatar", "width", "height", "sender<PERSON>ame", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "variant", "color", "px", "p", "bgcolor", "borderLeft", "borderColor", "mb", "opacity", "length", "substring", "borderRadius", "position", "onClick", "e", "reactions", "mt", "flexWrap", "map", "reaction", "idx", "label", "count", "size", "fontSize", "justifyContent", "borderBottom", "name", "participants", "t", "userName", "join", "flexGrow", "overflow", "ref", "fullWidth", "multiline", "maxRows", "placeholder", "onChange", "target", "onKeyPress", "key", "shift<PERSON>ey", "preventDefault", "InputProps", "endAdornment", "disabled", "open", "Boolean", "mr", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/ドキュメント/Projects/StudyHub/studyhub-app/src/components/Chat/GroupChat.tsx"], "sourcesContent": ["import React, { useState, useEffect, useRef } from 'react';\nimport {\n  Box,\n  Paper,\n  Typography,\n  TextField,\n  IconButton,\n  Avatar,\n  List,\n  ListItem,\n  Chip,\n  Menu,\n  MenuItem,\n  Divider,\n  CircularProgress,\n  InputAdornment,\n} from '@mui/material';\nimport {\n  Send,\n  AttachFile,\n  EmojiEmotions,\n  MoreVert,\n  Reply,\n  Delete,\n  Edit,\n  Close,\n} from '@mui/icons-material';\nimport { formatDistanceToNow } from 'date-fns';\nimport { ChatService } from '../../services/chatService';\nimport { ChatMessage, Chat, ChatTypingIndicator } from '../../types/chat';\nimport { useAuth } from '../../contexts/AuthContext';\n\ninterface GroupChatProps {\n  chatId: string;\n  chat: Chat;\n  onClose?: () => void;\n}\n\nconst GroupChat: React.FC<GroupChatProps> = ({ chatId, chat, onClose }) => {\n  const { user } = useAuth();\n  const [messages, setMessages] = useState<ChatMessage[]>([]);\n  const [newMessage, setNewMessage] = useState('');\n  const [loading, setLoading] = useState(true);\n  const [sending, setSending] = useState(false);\n  const [typingUsers, setTypingUsers] = useState<ChatTypingIndicator[]>([]);\n  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);\n  const [selectedMessage, setSelectedMessage] = useState<ChatMessage | null>(null);\n  const [replyTo, setReplyTo] = useState<ChatMessage | null>(null);\n  const [editingMessage, setEditingMessage] = useState<ChatMessage | null>(null);\n  const messagesEndRef = useRef<HTMLDivElement>(null);\n  const typingTimeoutRef = useRef<NodeJS.Timeout | null>(null);\n\n  useEffect(() => {\n    if (!chatId || !user) return;\n\n    // Subscribe to messages\n    const unsubscribeMessages = ChatService.subscribeToMessages(\n      chatId,\n      (newMessages) => {\n        setMessages(newMessages);\n        setLoading(false);\n        scrollToBottom();\n      }\n    );\n\n    // Subscribe to typing indicators\n    const unsubscribeTyping = ChatService.subscribeToTyping(\n      chatId,\n      user.uid,\n      setTypingUsers\n    );\n\n    // Mark messages as read\n    ChatService.markAsRead(chatId, user.uid);\n\n    return () => {\n      unsubscribeMessages();\n      unsubscribeTyping();\n    };\n  }, [chatId, user]);\n\n  const scrollToBottom = () => {\n    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });\n  };\n\n  const handleSendMessage = async () => {\n    if (!newMessage.trim() || !user || sending) return;\n\n    setSending(true);\n    try {\n      await ChatService.sendMessage(\n        chatId,\n        user.uid,\n        {\n          content: newMessage.trim(),\n          type: 'text',\n          replyTo: replyTo?.id,\n        },\n        user.displayName || user.email?.split('@')[0] || 'User'\n      );\n\n      setNewMessage('');\n      setReplyTo(null);\n\n      // Stop typing indicator\n      await ChatService.setTyping(chatId, user.uid, user.displayName || '', false);\n    } catch (error) {\n      console.error('Error sending message:', error);\n    } finally {\n      setSending(false);\n    }\n  };\n\n  const handleTyping = async (value: string) => {\n    setNewMessage(value);\n\n    if (!user) return;\n\n    // Clear existing timeout\n    if (typingTimeoutRef.current) {\n      clearTimeout(typingTimeoutRef.current);\n    }\n\n    if (value.trim()) {\n      // Set typing indicator\n      await ChatService.setTyping(chatId, user.uid, user.displayName || '', true);\n\n      // Clear typing after 3 seconds of inactivity\n      typingTimeoutRef.current = setTimeout(async () => {\n        await ChatService.setTyping(chatId, user.uid, user.displayName || '', false);\n      }, 3000);\n    } else {\n      // Clear typing immediately if input is empty\n      await ChatService.setTyping(chatId, user.uid, user.displayName || '', false);\n    }\n  };\n\n  const handleMessageAction = (event: React.MouseEvent<HTMLElement>, message: ChatMessage) => {\n    setAnchorEl(event.currentTarget);\n    setSelectedMessage(message);\n  };\n\n  const handleCloseMenu = () => {\n    setAnchorEl(null);\n    setSelectedMessage(null);\n  };\n\n  const handleReply = () => {\n    setReplyTo(selectedMessage);\n    handleCloseMenu();\n  };\n\n  const handleAddReaction = async (emoji: string) => {\n    if (!selectedMessage || !user) return;\n\n    try {\n      await ChatService.addReaction(chatId, selectedMessage.id, user.uid, emoji);\n      handleCloseMenu();\n    } catch (error) {\n      console.error('Error adding reaction:', error);\n    }\n  };\n\n  const formatMessageTime = (timestamp: any) => {\n    if (!timestamp) return '';\n    \n    const date = timestamp.toDate ? timestamp.toDate() : new Date(timestamp);\n    return formatDistanceToNow(date, { addSuffix: true });\n  };\n\n  const renderMessage = (message: ChatMessage, index: number) => {\n    const isOwnMessage = message.senderId === user?.uid;\n    const showAvatar = index === 0 || messages[index - 1]?.senderId !== message.senderId;\n    const replyToMessage = message.replyTo ? messages.find(m => m.id === message.replyTo) : null;\n\n    return (\n      <ListItem\n        key={message.id}\n        sx={{\n          flexDirection: 'column',\n          alignItems: isOwnMessage ? 'flex-end' : 'flex-start',\n          py: 0.5,\n        }}\n      >\n        <Box\n          sx={{\n            display: 'flex',\n            flexDirection: isOwnMessage ? 'row-reverse' : 'row',\n            alignItems: 'flex-end',\n            gap: 1,\n            maxWidth: '70%',\n          }}\n        >\n          {showAvatar && !isOwnMessage && (\n            <Avatar\n              src={message.senderAvatar}\n              sx={{ width: 32, height: 32 }}\n            >\n              {message.senderName[0]}\n            </Avatar>\n          )}\n          \n          <Box\n            sx={{\n              display: 'flex',\n              flexDirection: 'column',\n              gap: 0.5,\n            }}\n          >\n            {showAvatar && !isOwnMessage && (\n              <Typography variant=\"caption\" color=\"text.secondary\" sx={{ px: 1 }}>\n                {message.senderName}\n              </Typography>\n            )}\n            \n            {replyToMessage && (\n              <Paper\n                sx={{\n                  p: 1,\n                  bgcolor: 'action.hover',\n                  borderLeft: 3,\n                  borderColor: 'primary.main',\n                  mb: 0.5,\n                }}\n              >\n                <Typography variant=\"caption\" color=\"text.secondary\">\n                  Replying to {replyToMessage.senderName}\n                </Typography>\n                <Typography variant=\"body2\" sx={{ opacity: 0.7 }}>\n                  {replyToMessage.content.length > 50\n                    ? `${replyToMessage.content.substring(0, 50)}...`\n                    : replyToMessage.content}\n                </Typography>\n              </Paper>\n            )}\n            \n            <Paper\n              sx={{\n                p: 1.5,\n                bgcolor: isOwnMessage ? 'primary.main' : 'background.paper',\n                color: isOwnMessage ? 'primary.contrastText' : 'text.primary',\n                borderRadius: 2,\n                position: 'relative',\n              }}\n              onClick={(e) => handleMessageAction(e, message)}\n            >\n              <Typography variant=\"body2\">{message.content}</Typography>\n              \n              {message.reactions && message.reactions.length > 0 && (\n                <Box sx={{ display: 'flex', gap: 0.5, mt: 1, flexWrap: 'wrap' }}>\n                  {message.reactions.map((reaction, idx) => (\n                    <Chip\n                      key={idx}\n                      label={`${reaction.emoji} ${reaction.count}`}\n                      size=\"small\"\n                      variant=\"outlined\"\n                      onClick={() => handleAddReaction(reaction.emoji)}\n                      sx={{ height: 20, fontSize: '0.7rem' }}\n                    />\n                  ))}\n                </Box>\n              )}\n              \n              <Typography\n                variant=\"caption\"\n                sx={{\n                  display: 'block',\n                  mt: 0.5,\n                  opacity: 0.7,\n                  fontSize: '0.7rem',\n                }}\n              >\n                {formatMessageTime(message.timestamp)}\n              </Typography>\n            </Paper>\n          </Box>\n        </Box>\n      </ListItem>\n    );\n  };\n\n  if (loading) {\n    return (\n      <Box display=\"flex\" justifyContent=\"center\" alignItems=\"center\" height=\"400px\">\n        <CircularProgress />\n      </Box>\n    );\n  }\n\n  return (\n    <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>\n      {/* Chat Header */}\n      <Paper\n        sx={{\n          p: 2,\n          borderRadius: 0,\n          borderBottom: 1,\n          borderColor: 'divider',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'space-between',\n        }}\n      >\n        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>\n          <Avatar>{chat.name?.[0] || 'G'}</Avatar>\n          <Box>\n            <Typography variant=\"h6\">{chat.name || 'Group Chat'}</Typography>\n            <Typography variant=\"caption\" color=\"text.secondary\">\n              {chat.participants.length} members\n              {typingUsers.length > 0 && (\n                <span> • {typingUsers.map(t => t.userName).join(', ')} typing...</span>\n              )}\n            </Typography>\n          </Box>\n        </Box>\n        \n        {onClose && (\n          <IconButton onClick={onClose}>\n            <Close />\n          </IconButton>\n        )}\n      </Paper>\n\n      {/* Messages */}\n      <Box sx={{ flexGrow: 1, overflow: 'auto', p: 1 }}>\n        <List sx={{ py: 0 }}>\n          {messages.map((message, index) => renderMessage(message, index))}\n        </List>\n        <div ref={messagesEndRef} />\n      </Box>\n\n      {/* Reply Preview */}\n      {replyTo && (\n        <Paper sx={{ p: 1, m: 1, bgcolor: 'action.hover' }}>\n          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\n            <Box>\n              <Typography variant=\"caption\" color=\"text.secondary\">\n                Replying to {replyTo.senderName}\n              </Typography>\n              <Typography variant=\"body2\">\n                {replyTo.content.length > 50\n                  ? `${replyTo.content.substring(0, 50)}...`\n                  : replyTo.content}\n              </Typography>\n            </Box>\n            <IconButton size=\"small\" onClick={() => setReplyTo(null)}>\n              <Close />\n            </IconButton>\n          </Box>\n        </Paper>\n      )}\n\n      {/* Message Input */}\n      <Paper sx={{ p: 2, borderRadius: 0 }}>\n        <Box sx={{ display: 'flex', gap: 1, alignItems: 'flex-end' }}>\n          <TextField\n            fullWidth\n            multiline\n            maxRows={4}\n            placeholder=\"Type a message...\"\n            value={newMessage}\n            onChange={(e) => handleTyping(e.target.value)}\n            onKeyPress={(e) => {\n              if (e.key === 'Enter' && !e.shiftKey) {\n                e.preventDefault();\n                handleSendMessage();\n              }\n            }}\n            InputProps={{\n              endAdornment: (\n                <InputAdornment position=\"end\">\n                  <IconButton size=\"small\">\n                    <AttachFile />\n                  </IconButton>\n                  <IconButton size=\"small\">\n                    <EmojiEmotions />\n                  </IconButton>\n                </InputAdornment>\n              ),\n            }}\n          />\n          <IconButton\n            color=\"primary\"\n            onClick={handleSendMessage}\n            disabled={!newMessage.trim() || sending}\n          >\n            {sending ? <CircularProgress size={20} /> : <Send />}\n          </IconButton>\n        </Box>\n      </Paper>\n\n      {/* Message Actions Menu */}\n      <Menu\n        anchorEl={anchorEl}\n        open={Boolean(anchorEl)}\n        onClose={handleCloseMenu}\n      >\n        <MenuItem onClick={handleReply}>\n          <Reply sx={{ mr: 1 }} />\n          Reply\n        </MenuItem>\n        <MenuItem onClick={() => handleAddReaction('👍')}>\n          👍 Like\n        </MenuItem>\n        <MenuItem onClick={() => handleAddReaction('❤️')}>\n          ❤️ Love\n        </MenuItem>\n        <MenuItem onClick={() => handleAddReaction('😂')}>\n          😂 Laugh\n        </MenuItem>\n        {selectedMessage?.senderId === user?.uid && (\n          <>\n            <Divider />\n            <MenuItem onClick={() => setEditingMessage(selectedMessage)}>\n              <Edit sx={{ mr: 1 }} />\n              Edit\n            </MenuItem>\n            <MenuItem sx={{ color: 'error.main' }}>\n              <Delete sx={{ mr: 1 }} />\n              Delete\n            </MenuItem>\n          </>\n        )}\n      </Menu>\n    </Box>\n  );\n};\n\nexport default GroupChat;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,SACEC,GAAG,EACHC,KAAK,EACLC,UAAU,EACVC,SAAS,EACTC,UAAU,EACVC,MAAM,EACNC,IAAI,EACJC,QAAQ,EACRC,IAAI,EACJC,IAAI,EACJC,QAAQ,EACRC,OAAO,EACPC,gBAAgB,EAChBC,cAAc,QACT,eAAe;AACtB,SACEC,IAAI,EACJC,UAAU,EACVC,aAAa,EAEbC,KAAK,EACLC,MAAM,EACNC,IAAI,EACJC,KAAK,QACA,qBAAqB;AAC5B,SAASC,mBAAmB,QAAQ,UAAU;AAC9C,SAASC,WAAW,QAAQ,4BAA4B;AAExD,SAASC,OAAO,QAAQ,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAQrD,MAAMC,SAAmC,GAAGA,CAAC;EAAEC,MAAM;EAAEC,IAAI;EAAEC;AAAQ,CAAC,KAAK;EAAAC,EAAA;EAAA,IAAAC,UAAA;EACzE,MAAM;IAAEC;EAAK,CAAC,GAAGX,OAAO,CAAC,CAAC;EAC1B,MAAM,CAACY,QAAQ,EAAEC,WAAW,CAAC,GAAGvC,QAAQ,CAAgB,EAAE,CAAC;EAC3D,MAAM,CAACwC,UAAU,EAAEC,aAAa,CAAC,GAAGzC,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC0C,OAAO,EAAEC,UAAU,CAAC,GAAG3C,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAAC4C,OAAO,EAAEC,UAAU,CAAC,GAAG7C,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC8C,WAAW,EAAEC,cAAc,CAAC,GAAG/C,QAAQ,CAAwB,EAAE,CAAC;EACzE,MAAM,CAACgD,QAAQ,EAAEC,WAAW,CAAC,GAAGjD,QAAQ,CAAqB,IAAI,CAAC;EAClE,MAAM,CAACkD,eAAe,EAAEC,kBAAkB,CAAC,GAAGnD,QAAQ,CAAqB,IAAI,CAAC;EAChF,MAAM,CAACoD,OAAO,EAAEC,UAAU,CAAC,GAAGrD,QAAQ,CAAqB,IAAI,CAAC;EAChE,MAAM,CAACsD,cAAc,EAAEC,iBAAiB,CAAC,GAAGvD,QAAQ,CAAqB,IAAI,CAAC;EAC9E,MAAMwD,cAAc,GAAGtD,MAAM,CAAiB,IAAI,CAAC;EACnD,MAAMuD,gBAAgB,GAAGvD,MAAM,CAAwB,IAAI,CAAC;EAE5DD,SAAS,CAAC,MAAM;IACd,IAAI,CAAC+B,MAAM,IAAI,CAACK,IAAI,EAAE;;IAEtB;IACA,MAAMqB,mBAAmB,GAAGjC,WAAW,CAACkC,mBAAmB,CACzD3B,MAAM,EACL4B,WAAW,IAAK;MACfrB,WAAW,CAACqB,WAAW,CAAC;MACxBjB,UAAU,CAAC,KAAK,CAAC;MACjBkB,cAAc,CAAC,CAAC;IAClB,CACF,CAAC;;IAED;IACA,MAAMC,iBAAiB,GAAGrC,WAAW,CAACsC,iBAAiB,CACrD/B,MAAM,EACNK,IAAI,CAAC2B,GAAG,EACRjB,cACF,CAAC;;IAED;IACAtB,WAAW,CAACwC,UAAU,CAACjC,MAAM,EAAEK,IAAI,CAAC2B,GAAG,CAAC;IAExC,OAAO,MAAM;MACXN,mBAAmB,CAAC,CAAC;MACrBI,iBAAiB,CAAC,CAAC;IACrB,CAAC;EACH,CAAC,EAAE,CAAC9B,MAAM,EAAEK,IAAI,CAAC,CAAC;EAElB,MAAMwB,cAAc,GAAGA,CAAA,KAAM;IAAA,IAAAK,qBAAA;IAC3B,CAAAA,qBAAA,GAAAV,cAAc,CAACW,OAAO,cAAAD,qBAAA,uBAAtBA,qBAAA,CAAwBE,cAAc,CAAC;MAAEC,QAAQ,EAAE;IAAS,CAAC,CAAC;EAChE,CAAC;EAED,MAAMC,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI,CAAC9B,UAAU,CAAC+B,IAAI,CAAC,CAAC,IAAI,CAAClC,IAAI,IAAIO,OAAO,EAAE;IAE5CC,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MAAA,IAAA2B,WAAA;MACF,MAAM/C,WAAW,CAACgD,WAAW,CAC3BzC,MAAM,EACNK,IAAI,CAAC2B,GAAG,EACR;QACEU,OAAO,EAAElC,UAAU,CAAC+B,IAAI,CAAC,CAAC;QAC1BI,IAAI,EAAE,MAAM;QACZvB,OAAO,EAAEA,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEwB;MACpB,CAAC,EACDvC,IAAI,CAACwC,WAAW,MAAAL,WAAA,GAAInC,IAAI,CAACyC,KAAK,cAAAN,WAAA,uBAAVA,WAAA,CAAYO,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,KAAI,MACnD,CAAC;MAEDtC,aAAa,CAAC,EAAE,CAAC;MACjBY,UAAU,CAAC,IAAI,CAAC;;MAEhB;MACA,MAAM5B,WAAW,CAACuD,SAAS,CAAChD,MAAM,EAAEK,IAAI,CAAC2B,GAAG,EAAE3B,IAAI,CAACwC,WAAW,IAAI,EAAE,EAAE,KAAK,CAAC;IAC9E,CAAC,CAAC,OAAOI,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;IAChD,CAAC,SAAS;MACRpC,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMsC,YAAY,GAAG,MAAOC,KAAa,IAAK;IAC5C3C,aAAa,CAAC2C,KAAK,CAAC;IAEpB,IAAI,CAAC/C,IAAI,EAAE;;IAEX;IACA,IAAIoB,gBAAgB,CAACU,OAAO,EAAE;MAC5BkB,YAAY,CAAC5B,gBAAgB,CAACU,OAAO,CAAC;IACxC;IAEA,IAAIiB,KAAK,CAACb,IAAI,CAAC,CAAC,EAAE;MAChB;MACA,MAAM9C,WAAW,CAACuD,SAAS,CAAChD,MAAM,EAAEK,IAAI,CAAC2B,GAAG,EAAE3B,IAAI,CAACwC,WAAW,IAAI,EAAE,EAAE,IAAI,CAAC;;MAE3E;MACApB,gBAAgB,CAACU,OAAO,GAAGmB,UAAU,CAAC,YAAY;QAChD,MAAM7D,WAAW,CAACuD,SAAS,CAAChD,MAAM,EAAEK,IAAI,CAAC2B,GAAG,EAAE3B,IAAI,CAACwC,WAAW,IAAI,EAAE,EAAE,KAAK,CAAC;MAC9E,CAAC,EAAE,IAAI,CAAC;IACV,CAAC,MAAM;MACL;MACA,MAAMpD,WAAW,CAACuD,SAAS,CAAChD,MAAM,EAAEK,IAAI,CAAC2B,GAAG,EAAE3B,IAAI,CAACwC,WAAW,IAAI,EAAE,EAAE,KAAK,CAAC;IAC9E;EACF,CAAC;EAED,MAAMU,mBAAmB,GAAGA,CAACC,KAAoC,EAAEC,OAAoB,KAAK;IAC1FxC,WAAW,CAACuC,KAAK,CAACE,aAAa,CAAC;IAChCvC,kBAAkB,CAACsC,OAAO,CAAC;EAC7B,CAAC;EAED,MAAME,eAAe,GAAGA,CAAA,KAAM;IAC5B1C,WAAW,CAAC,IAAI,CAAC;IACjBE,kBAAkB,CAAC,IAAI,CAAC;EAC1B,CAAC;EAED,MAAMyC,WAAW,GAAGA,CAAA,KAAM;IACxBvC,UAAU,CAACH,eAAe,CAAC;IAC3ByC,eAAe,CAAC,CAAC;EACnB,CAAC;EAED,MAAME,iBAAiB,GAAG,MAAOC,KAAa,IAAK;IACjD,IAAI,CAAC5C,eAAe,IAAI,CAACb,IAAI,EAAE;IAE/B,IAAI;MACF,MAAMZ,WAAW,CAACsE,WAAW,CAAC/D,MAAM,EAAEkB,eAAe,CAAC0B,EAAE,EAAEvC,IAAI,CAAC2B,GAAG,EAAE8B,KAAK,CAAC;MAC1EH,eAAe,CAAC,CAAC;IACnB,CAAC,CAAC,OAAOV,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;IAChD;EACF,CAAC;EAED,MAAMe,iBAAiB,GAAIC,SAAc,IAAK;IAC5C,IAAI,CAACA,SAAS,EAAE,OAAO,EAAE;IAEzB,MAAMC,IAAI,GAAGD,SAAS,CAACE,MAAM,GAAGF,SAAS,CAACE,MAAM,CAAC,CAAC,GAAG,IAAIC,IAAI,CAACH,SAAS,CAAC;IACxE,OAAOzE,mBAAmB,CAAC0E,IAAI,EAAE;MAAEG,SAAS,EAAE;IAAK,CAAC,CAAC;EACvD,CAAC;EAED,MAAMC,aAAa,GAAGA,CAACb,OAAoB,EAAEc,KAAa,KAAK;IAAA,IAAAC,SAAA;IAC7D,MAAMC,YAAY,GAAGhB,OAAO,CAACiB,QAAQ,MAAKrE,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE2B,GAAG;IACnD,MAAM2C,UAAU,GAAGJ,KAAK,KAAK,CAAC,IAAI,EAAAC,SAAA,GAAAlE,QAAQ,CAACiE,KAAK,GAAG,CAAC,CAAC,cAAAC,SAAA,uBAAnBA,SAAA,CAAqBE,QAAQ,MAAKjB,OAAO,CAACiB,QAAQ;IACpF,MAAME,cAAc,GAAGnB,OAAO,CAACrC,OAAO,GAAGd,QAAQ,CAACuE,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAClC,EAAE,KAAKa,OAAO,CAACrC,OAAO,CAAC,GAAG,IAAI;IAE5F,oBACExB,OAAA,CAAClB,QAAQ;MAEPqG,EAAE,EAAE;QACFC,aAAa,EAAE,QAAQ;QACvBC,UAAU,EAAER,YAAY,GAAG,UAAU,GAAG,YAAY;QACpDS,EAAE,EAAE;MACN,CAAE;MAAAC,QAAA,eAEFvF,OAAA,CAACzB,GAAG;QACF4G,EAAE,EAAE;UACFK,OAAO,EAAE,MAAM;UACfJ,aAAa,EAAEP,YAAY,GAAG,aAAa,GAAG,KAAK;UACnDQ,UAAU,EAAE,UAAU;UACtBI,GAAG,EAAE,CAAC;UACNC,QAAQ,EAAE;QACZ,CAAE;QAAAH,QAAA,GAEDR,UAAU,IAAI,CAACF,YAAY,iBAC1B7E,OAAA,CAACpB,MAAM;UACL+G,GAAG,EAAE9B,OAAO,CAAC+B,YAAa;UAC1BT,EAAE,EAAE;YAAEU,KAAK,EAAE,EAAE;YAAEC,MAAM,EAAE;UAAG,CAAE;UAAAP,QAAA,EAE7B1B,OAAO,CAACkC,UAAU,CAAC,CAAC;QAAC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB,CACT,eAEDnG,OAAA,CAACzB,GAAG;UACF4G,EAAE,EAAE;YACFK,OAAO,EAAE,MAAM;YACfJ,aAAa,EAAE,QAAQ;YACvBK,GAAG,EAAE;UACP,CAAE;UAAAF,QAAA,GAEDR,UAAU,IAAI,CAACF,YAAY,iBAC1B7E,OAAA,CAACvB,UAAU;YAAC2H,OAAO,EAAC,SAAS;YAACC,KAAK,EAAC,gBAAgB;YAAClB,EAAE,EAAE;cAAEmB,EAAE,EAAE;YAAE,CAAE;YAAAf,QAAA,EAChE1B,OAAO,CAACkC;UAAU;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CACb,EAEAnB,cAAc,iBACbhF,OAAA,CAACxB,KAAK;YACJ2G,EAAE,EAAE;cACFoB,CAAC,EAAE,CAAC;cACJC,OAAO,EAAE,cAAc;cACvBC,UAAU,EAAE,CAAC;cACbC,WAAW,EAAE,cAAc;cAC3BC,EAAE,EAAE;YACN,CAAE;YAAApB,QAAA,gBAEFvF,OAAA,CAACvB,UAAU;cAAC2H,OAAO,EAAC,SAAS;cAACC,KAAK,EAAC,gBAAgB;cAAAd,QAAA,GAAC,cACvC,EAACP,cAAc,CAACe,UAAU;YAAA;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5B,CAAC,eACbnG,OAAA,CAACvB,UAAU;cAAC2H,OAAO,EAAC,OAAO;cAACjB,EAAE,EAAE;gBAAEyB,OAAO,EAAE;cAAI,CAAE;cAAArB,QAAA,EAC9CP,cAAc,CAAClC,OAAO,CAAC+D,MAAM,GAAG,EAAE,GAC/B,GAAG7B,cAAc,CAAClC,OAAO,CAACgE,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,KAAK,GAC/C9B,cAAc,CAAClC;YAAO;cAAAkD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CACR,eAEDnG,OAAA,CAACxB,KAAK;YACJ2G,EAAE,EAAE;cACFoB,CAAC,EAAE,GAAG;cACNC,OAAO,EAAE3B,YAAY,GAAG,cAAc,GAAG,kBAAkB;cAC3DwB,KAAK,EAAExB,YAAY,GAAG,sBAAsB,GAAG,cAAc;cAC7DkC,YAAY,EAAE,CAAC;cACfC,QAAQ,EAAE;YACZ,CAAE;YACFC,OAAO,EAAGC,CAAC,IAAKvD,mBAAmB,CAACuD,CAAC,EAAErD,OAAO,CAAE;YAAA0B,QAAA,gBAEhDvF,OAAA,CAACvB,UAAU;cAAC2H,OAAO,EAAC,OAAO;cAAAb,QAAA,EAAE1B,OAAO,CAACf;YAAO;cAAAkD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAa,CAAC,EAEzDtC,OAAO,CAACsD,SAAS,IAAItD,OAAO,CAACsD,SAAS,CAACN,MAAM,GAAG,CAAC,iBAChD7G,OAAA,CAACzB,GAAG;cAAC4G,EAAE,EAAE;gBAAEK,OAAO,EAAE,MAAM;gBAAEC,GAAG,EAAE,GAAG;gBAAE2B,EAAE,EAAE,CAAC;gBAAEC,QAAQ,EAAE;cAAO,CAAE;cAAA9B,QAAA,EAC7D1B,OAAO,CAACsD,SAAS,CAACG,GAAG,CAAC,CAACC,QAAQ,EAAEC,GAAG,kBACnCxH,OAAA,CAACjB,IAAI;gBAEH0I,KAAK,EAAE,GAAGF,QAAQ,CAACrD,KAAK,IAAIqD,QAAQ,CAACG,KAAK,EAAG;gBAC7CC,IAAI,EAAC,OAAO;gBACZvB,OAAO,EAAC,UAAU;gBAClBa,OAAO,EAAEA,CAAA,KAAMhD,iBAAiB,CAACsD,QAAQ,CAACrD,KAAK,CAAE;gBACjDiB,EAAE,EAAE;kBAAEW,MAAM,EAAE,EAAE;kBAAE8B,QAAQ,EAAE;gBAAS;cAAE,GALlCJ,GAAG;gBAAAxB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAMT,CACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CACN,eAEDnG,OAAA,CAACvB,UAAU;cACT2H,OAAO,EAAC,SAAS;cACjBjB,EAAE,EAAE;gBACFK,OAAO,EAAE,OAAO;gBAChB4B,EAAE,EAAE,GAAG;gBACPR,OAAO,EAAE,GAAG;gBACZgB,QAAQ,EAAE;cACZ,CAAE;cAAArC,QAAA,EAEDnB,iBAAiB,CAACP,OAAO,CAACQ,SAAS;YAAC;cAAA2B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC,GAnGDtC,OAAO,CAACb,EAAE;MAAAgD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAoGP,CAAC;EAEf,CAAC;EAED,IAAIrF,OAAO,EAAE;IACX,oBACEd,OAAA,CAACzB,GAAG;MAACiH,OAAO,EAAC,MAAM;MAACqC,cAAc,EAAC,QAAQ;MAACxC,UAAU,EAAC,QAAQ;MAACS,MAAM,EAAC,OAAO;MAAAP,QAAA,eAC5EvF,OAAA,CAACb,gBAAgB;QAAA6G,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjB,CAAC;EAEV;EAEA,oBACEnG,OAAA,CAACzB,GAAG;IAAC4G,EAAE,EAAE;MAAEW,MAAM,EAAE,MAAM;MAAEN,OAAO,EAAE,MAAM;MAAEJ,aAAa,EAAE;IAAS,CAAE;IAAAG,QAAA,gBAEpEvF,OAAA,CAACxB,KAAK;MACJ2G,EAAE,EAAE;QACFoB,CAAC,EAAE,CAAC;QACJQ,YAAY,EAAE,CAAC;QACfe,YAAY,EAAE,CAAC;QACfpB,WAAW,EAAE,SAAS;QACtBlB,OAAO,EAAE,MAAM;QACfH,UAAU,EAAE,QAAQ;QACpBwC,cAAc,EAAE;MAClB,CAAE;MAAAtC,QAAA,gBAEFvF,OAAA,CAACzB,GAAG;QAAC4G,EAAE,EAAE;UAAEK,OAAO,EAAE,MAAM;UAAEH,UAAU,EAAE,QAAQ;UAAEI,GAAG,EAAE;QAAE,CAAE;QAAAF,QAAA,gBACzDvF,OAAA,CAACpB,MAAM;UAAA2G,QAAA,EAAE,EAAA/E,UAAA,GAAAH,IAAI,CAAC0H,IAAI,cAAAvH,UAAA,uBAATA,UAAA,CAAY,CAAC,CAAC,KAAI;QAAG;UAAAwF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAS,CAAC,eACxCnG,OAAA,CAACzB,GAAG;UAAAgH,QAAA,gBACFvF,OAAA,CAACvB,UAAU;YAAC2H,OAAO,EAAC,IAAI;YAAAb,QAAA,EAAElF,IAAI,CAAC0H,IAAI,IAAI;UAAY;YAAA/B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC,eACjEnG,OAAA,CAACvB,UAAU;YAAC2H,OAAO,EAAC,SAAS;YAACC,KAAK,EAAC,gBAAgB;YAAAd,QAAA,GACjDlF,IAAI,CAAC2H,YAAY,CAACnB,MAAM,EAAC,UAC1B,EAAC3F,WAAW,CAAC2F,MAAM,GAAG,CAAC,iBACrB7G,OAAA;cAAAuF,QAAA,GAAM,UAAG,EAACrE,WAAW,CAACoG,GAAG,CAACW,CAAC,IAAIA,CAAC,CAACC,QAAQ,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC,EAAC,YAAU;YAAA;cAAAnC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CACvE;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACS,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAEL7F,OAAO,iBACNN,OAAA,CAACrB,UAAU;QAACsI,OAAO,EAAE3G,OAAQ;QAAAiF,QAAA,eAC3BvF,OAAA,CAACL,KAAK;UAAAqG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CACb;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC,eAGRnG,OAAA,CAACzB,GAAG;MAAC4G,EAAE,EAAE;QAAEiD,QAAQ,EAAE,CAAC;QAAEC,QAAQ,EAAE,MAAM;QAAE9B,CAAC,EAAE;MAAE,CAAE;MAAAhB,QAAA,gBAC/CvF,OAAA,CAACnB,IAAI;QAACsG,EAAE,EAAE;UAAEG,EAAE,EAAE;QAAE,CAAE;QAAAC,QAAA,EACjB7E,QAAQ,CAAC4G,GAAG,CAAC,CAACzD,OAAO,EAAEc,KAAK,KAAKD,aAAa,CAACb,OAAO,EAAEc,KAAK,CAAC;MAAC;QAAAqB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5D,CAAC,eACPnG,OAAA;QAAKsI,GAAG,EAAE1G;MAAe;QAAAoE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACzB,CAAC,EAGL3E,OAAO,iBACNxB,OAAA,CAACxB,KAAK;MAAC2G,EAAE,EAAE;QAAEoB,CAAC,EAAE,CAAC;QAAErB,CAAC,EAAE,CAAC;QAAEsB,OAAO,EAAE;MAAe,CAAE;MAAAjB,QAAA,eACjDvF,OAAA,CAACzB,GAAG;QAAC4G,EAAE,EAAE;UAAEK,OAAO,EAAE,MAAM;UAAEqC,cAAc,EAAE,eAAe;UAAExC,UAAU,EAAE;QAAS,CAAE;QAAAE,QAAA,gBAClFvF,OAAA,CAACzB,GAAG;UAAAgH,QAAA,gBACFvF,OAAA,CAACvB,UAAU;YAAC2H,OAAO,EAAC,SAAS;YAACC,KAAK,EAAC,gBAAgB;YAAAd,QAAA,GAAC,cACvC,EAAC/D,OAAO,CAACuE,UAAU;UAAA;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrB,CAAC,eACbnG,OAAA,CAACvB,UAAU;YAAC2H,OAAO,EAAC,OAAO;YAAAb,QAAA,EACxB/D,OAAO,CAACsB,OAAO,CAAC+D,MAAM,GAAG,EAAE,GACxB,GAAGrF,OAAO,CAACsB,OAAO,CAACgE,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,KAAK,GACxCtF,OAAO,CAACsB;UAAO;YAAAkD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eACNnG,OAAA,CAACrB,UAAU;UAACgJ,IAAI,EAAC,OAAO;UAACV,OAAO,EAAEA,CAAA,KAAMxF,UAAU,CAAC,IAAI,CAAE;UAAA8D,QAAA,eACvDvF,OAAA,CAACL,KAAK;YAAAqG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CACR,eAGDnG,OAAA,CAACxB,KAAK;MAAC2G,EAAE,EAAE;QAAEoB,CAAC,EAAE,CAAC;QAAEQ,YAAY,EAAE;MAAE,CAAE;MAAAxB,QAAA,eACnCvF,OAAA,CAACzB,GAAG;QAAC4G,EAAE,EAAE;UAAEK,OAAO,EAAE,MAAM;UAAEC,GAAG,EAAE,CAAC;UAAEJ,UAAU,EAAE;QAAW,CAAE;QAAAE,QAAA,gBAC3DvF,OAAA,CAACtB,SAAS;UACR6J,SAAS;UACTC,SAAS;UACTC,OAAO,EAAE,CAAE;UACXC,WAAW,EAAC,mBAAmB;UAC/BlF,KAAK,EAAE5C,UAAW;UAClB+H,QAAQ,EAAGzB,CAAC,IAAK3D,YAAY,CAAC2D,CAAC,CAAC0B,MAAM,CAACpF,KAAK,CAAE;UAC9CqF,UAAU,EAAG3B,CAAC,IAAK;YACjB,IAAIA,CAAC,CAAC4B,GAAG,KAAK,OAAO,IAAI,CAAC5B,CAAC,CAAC6B,QAAQ,EAAE;cACpC7B,CAAC,CAAC8B,cAAc,CAAC,CAAC;cAClBtG,iBAAiB,CAAC,CAAC;YACrB;UACF,CAAE;UACFuG,UAAU,EAAE;YACVC,YAAY,eACVlJ,OAAA,CAACZ,cAAc;cAAC4H,QAAQ,EAAC,KAAK;cAAAzB,QAAA,gBAC5BvF,OAAA,CAACrB,UAAU;gBAACgJ,IAAI,EAAC,OAAO;gBAAApC,QAAA,eACtBvF,OAAA,CAACV,UAAU;kBAAA0G,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACbnG,OAAA,CAACrB,UAAU;gBAACgJ,IAAI,EAAC,OAAO;gBAAApC,QAAA,eACtBvF,OAAA,CAACT,aAAa;kBAAAyG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACP,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC;UAEpB;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACFnG,OAAA,CAACrB,UAAU;UACT0H,KAAK,EAAC,SAAS;UACfY,OAAO,EAAEvE,iBAAkB;UAC3ByG,QAAQ,EAAE,CAACvI,UAAU,CAAC+B,IAAI,CAAC,CAAC,IAAI3B,OAAQ;UAAAuE,QAAA,EAEvCvE,OAAO,gBAAGhB,OAAA,CAACb,gBAAgB;YAACwI,IAAI,EAAE;UAAG;YAAA3B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAAGnG,OAAA,CAACX,IAAI;YAAA2G,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1C,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eAGRnG,OAAA,CAAChB,IAAI;MACHoC,QAAQ,EAAEA,QAAS;MACnBgI,IAAI,EAAEC,OAAO,CAACjI,QAAQ,CAAE;MACxBd,OAAO,EAAEyD,eAAgB;MAAAwB,QAAA,gBAEzBvF,OAAA,CAACf,QAAQ;QAACgI,OAAO,EAAEjD,WAAY;QAAAuB,QAAA,gBAC7BvF,OAAA,CAACR,KAAK;UAAC2F,EAAE,EAAE;YAAEmE,EAAE,EAAE;UAAE;QAAE;UAAAtD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,SAE1B;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAU,CAAC,eACXnG,OAAA,CAACf,QAAQ;QAACgI,OAAO,EAAEA,CAAA,KAAMhD,iBAAiB,CAAC,IAAI,CAAE;QAAAsB,QAAA,EAAC;MAElD;QAAAS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAU,CAAC,eACXnG,OAAA,CAACf,QAAQ;QAACgI,OAAO,EAAEA,CAAA,KAAMhD,iBAAiB,CAAC,IAAI,CAAE;QAAAsB,QAAA,EAAC;MAElD;QAAAS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAU,CAAC,eACXnG,OAAA,CAACf,QAAQ;QAACgI,OAAO,EAAEA,CAAA,KAAMhD,iBAAiB,CAAC,IAAI,CAAE;QAAAsB,QAAA,EAAC;MAElD;QAAAS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAU,CAAC,EACV,CAAA7E,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEwD,QAAQ,OAAKrE,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE2B,GAAG,kBACtCpC,OAAA,CAAAE,SAAA;QAAAqF,QAAA,gBACEvF,OAAA,CAACd,OAAO;UAAA8G,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACXnG,OAAA,CAACf,QAAQ;UAACgI,OAAO,EAAEA,CAAA,KAAMtF,iBAAiB,CAACL,eAAe,CAAE;UAAAiE,QAAA,gBAC1DvF,OAAA,CAACN,IAAI;YAACyF,EAAE,EAAE;cAAEmE,EAAE,EAAE;YAAE;UAAE;YAAAtD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,QAEzB;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAU,CAAC,eACXnG,OAAA,CAACf,QAAQ;UAACkG,EAAE,EAAE;YAAEkB,KAAK,EAAE;UAAa,CAAE;UAAAd,QAAA,gBACpCvF,OAAA,CAACP,MAAM;YAAC0F,EAAE,EAAE;cAAEmE,EAAE,EAAE;YAAE;UAAE;YAAAtD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,UAE3B;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAU,CAAC;MAAA,eACX,CACH;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;AAAC5F,EAAA,CApYIJ,SAAmC;EAAA,QACtBL,OAAO;AAAA;AAAAyJ,EAAA,GADpBpJ,SAAmC;AAsYzC,eAAeA,SAAS;AAAC,IAAAoJ,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}