{"ast": null, "code": "import _objectSpread from\"C:/Users/<USER>/OneDrive/\\u30C9\\u30AD\\u30E5\\u30E1\\u30F3\\u30C8/Projects/StudyHub/studyhub-app/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import React,{useState,useEffect}from'react';import{Container,Grid,Paper,List,ListItem,ListItemAvatar,ListItemText,Avatar,Typography,Box,Badge,TextField,InputAdornment,Fab,Dialog,DialogTitle,DialogContent,DialogActions,Button,FormControl,InputLabel,Select,MenuItem,CircularProgress}from'@mui/material';import{Search,Add,Message,Group,Person}from'@mui/icons-material';import{formatDistanceToNow}from'date-fns';import{ChatService}from'../services/chatService';import{StudyGroupService}from'../services/studyGroupService';import{useAuth}from'../contexts/AuthContext';import GroupChat from'../components/Chat/GroupChat';import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const Chat=()=>{const{user}=useAuth();const[chats,setChats]=useState([]);const[selectedChat,setSelectedChat]=useState(null);const[loading,setLoading]=useState(true);const[searchTerm,setSearchTerm]=useState('');const[newChatOpen,setNewChatOpen]=useState(false);const[chatType,setChatType]=useState('private');const[studyGroups,setStudyGroups]=useState([]);const[selectedUsers,setSelectedUsers]=useState([]);const[newChatData,setNewChatData]=useState({name:'',description:'',participantEmail:''});useEffect(()=>{if(!user)return;// Subscribe to user's chats\nconst unsubscribe=ChatService.subscribeToUserChats(user.uid,userChats=>{setChats(userChats);setLoading(false);});// Load user's study groups for group chat creation\nloadStudyGroups();return unsubscribe;},[user]);const loadStudyGroups=async()=>{if(!user)return;try{const groups=await StudyGroupService.getUserStudyGroups(user.uid);setStudyGroups(groups);}catch(error){console.error('Error loading study groups:',error);}};const filteredChats=chats.filter(chat=>{if(!searchTerm)return true;const searchLower=searchTerm.toLowerCase();if(chat.type==='group'){var _chat$name;return(_chat$name=chat.name)===null||_chat$name===void 0?void 0:_chat$name.toLowerCase().includes(searchLower);}else{var _chat$participantDeta;// For private chats, search by participant names\nreturn(_chat$participantDeta=chat.participantDetails)===null||_chat$participantDeta===void 0?void 0:_chat$participantDeta.some(p=>p.displayName.toLowerCase().includes(searchLower)||p.email.toLowerCase().includes(searchLower));}});const handleCreatePrivateChat=async()=>{if(!user||!newChatData.participantEmail.trim())return;try{var _user$email;setLoading(true);// TODO: Find user by email and get their profile\n// For now, we'll create a mock profile\nconst otherUserProfile={displayName:newChatData.participantEmail.split('@')[0],email:newChatData.participantEmail,profilePicture:''};const currentUserProfile={displayName:user.displayName||((_user$email=user.email)===null||_user$email===void 0?void 0:_user$email.split('@')[0])||'User',email:user.email||'',profilePicture:user.photoURL||''};const chatData={participantId:'temp-user-id',// TODO: Get actual user ID\ninitialMessage:\"Hi! I'd like to connect with you on StudyHub.\"};const chatId=await ChatService.createPrivateChat(user.uid,chatData,currentUserProfile,otherUserProfile);setNewChatOpen(false);setNewChatData({name:'',description:'',participantEmail:''});}catch(error){console.error('Error creating private chat:',error);}finally{setLoading(false);}};const handleCreateGroupChat=async()=>{if(!user||!newChatData.name.trim())return;try{setLoading(true);// TODO: Implement group chat creation\nconsole.log('Creating group chat:',newChatData);setNewChatOpen(false);setNewChatData({name:'',description:'',participantEmail:''});}catch(error){console.error('Error creating group chat:',error);}finally{setLoading(false);}};const getChatDisplayName=chat=>{if(chat.type==='group'){return chat.name||'Group Chat';}else{var _chat$participantDeta2;// For private chats, show the other participant's name\nconst otherParticipant=(_chat$participantDeta2=chat.participantDetails)===null||_chat$participantDeta2===void 0?void 0:_chat$participantDeta2.find(p=>p.uid!==(user===null||user===void 0?void 0:user.uid));return(otherParticipant===null||otherParticipant===void 0?void 0:otherParticipant.displayName)||'Private Chat';}};const getChatAvatar=chat=>{if(chat.type==='group'){var _chat$name2;return((_chat$name2=chat.name)===null||_chat$name2===void 0?void 0:_chat$name2[0])||'G';}else{var _chat$participantDeta3,_otherParticipant$dis;const otherParticipant=(_chat$participantDeta3=chat.participantDetails)===null||_chat$participantDeta3===void 0?void 0:_chat$participantDeta3.find(p=>p.uid!==(user===null||user===void 0?void 0:user.uid));return(otherParticipant===null||otherParticipant===void 0?void 0:otherParticipant.profilePicture)||(otherParticipant===null||otherParticipant===void 0?void 0:(_otherParticipant$dis=otherParticipant.displayName)===null||_otherParticipant$dis===void 0?void 0:_otherParticipant$dis[0])||'U';}};const getLastMessagePreview=chat=>{if(!chat.lastMessage)return'No messages yet';const content=chat.lastMessage.content;const isOwnMessage=chat.lastMessage.senderId===(user===null||user===void 0?void 0:user.uid);const prefix=isOwnMessage?'You: ':'';return\"\".concat(prefix).concat(content.length>50?content.substring(0,50)+'...':content);};const formatLastMessageTime=chat=>{if(!chat.lastMessageAt)return'';const date=chat.lastMessageAt.toDate?chat.lastMessageAt.toDate():new Date(chat.lastMessageAt);return formatDistanceToNow(date,{addSuffix:true});};const getUnreadCount=chat=>{var _chat$unreadCount;return((_chat$unreadCount=chat.unreadCount)===null||_chat$unreadCount===void 0?void 0:_chat$unreadCount[(user===null||user===void 0?void 0:user.uid)||''])||0;};if(loading){return/*#__PURE__*/_jsx(Container,{maxWidth:\"lg\",children:/*#__PURE__*/_jsx(Box,{display:\"flex\",justifyContent:\"center\",alignItems:\"center\",height:\"400px\",children:/*#__PURE__*/_jsx(CircularProgress,{})})});}return/*#__PURE__*/_jsxs(Container,{maxWidth:\"lg\",sx:{py:3},children:[/*#__PURE__*/_jsx(Typography,{variant:\"h4\",component:\"h1\",gutterBottom:true,children:\"Messages\"}),/*#__PURE__*/_jsxs(Grid,{container:true,spacing:3,sx:{height:'calc(100vh - 200px)'},children:[/*#__PURE__*/_jsx(Grid,{size:{xs:12,md:4},children:/*#__PURE__*/_jsxs(Paper,{sx:{height:'100%',display:'flex',flexDirection:'column'},children:[/*#__PURE__*/_jsx(Box,{sx:{p:2,borderBottom:1,borderColor:'divider'},children:/*#__PURE__*/_jsx(TextField,{fullWidth:true,placeholder:\"Search conversations...\",value:searchTerm,onChange:e=>setSearchTerm(e.target.value),InputProps:{startAdornment:/*#__PURE__*/_jsx(InputAdornment,{position:\"start\",children:/*#__PURE__*/_jsx(Search,{})})}})}),/*#__PURE__*/_jsx(List,{sx:{flexGrow:1,overflow:'auto',py:0},children:filteredChats.length===0?/*#__PURE__*/_jsxs(Box,{sx:{p:3,textAlign:'center'},children:[/*#__PURE__*/_jsx(Typography,{variant:\"body2\",color:\"text.secondary\",children:\"No conversations yet\"}),/*#__PURE__*/_jsx(Typography,{variant:\"caption\",color:\"text.secondary\",children:\"Start a new conversation to get started\"})]}):filteredChats.map(chat=>/*#__PURE__*/_jsxs(ListItem,{onClick:()=>setSelectedChat(chat),sx:{cursor:'pointer',borderBottom:1,borderColor:'divider',bgcolor:(selectedChat===null||selectedChat===void 0?void 0:selectedChat.id)===chat.id?'action.selected':'transparent','&:hover':{bgcolor:'action.hover'}},children:[/*#__PURE__*/_jsx(ListItemAvatar,{children:/*#__PURE__*/_jsx(Badge,{badgeContent:getUnreadCount(chat),color:\"primary\",invisible:getUnreadCount(chat)===0,children:/*#__PURE__*/_jsx(Avatar,{src:typeof getChatAvatar(chat)==='string'&&getChatAvatar(chat).startsWith('http')?getChatAvatar(chat):undefined,children:typeof getChatAvatar(chat)==='string'&&!getChatAvatar(chat).startsWith('http')?getChatAvatar(chat):''})})}),/*#__PURE__*/_jsx(ListItemText,{primary:/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center',gap:1},children:[/*#__PURE__*/_jsx(Typography,{variant:\"subtitle2\",noWrap:true,children:getChatDisplayName(chat)}),chat.type==='group'&&/*#__PURE__*/_jsx(Group,{fontSize:\"small\",color:\"action\"})]}),secondary:/*#__PURE__*/_jsxs(Box,{children:[/*#__PURE__*/_jsx(Typography,{variant:\"body2\",color:\"text.secondary\",noWrap:true,children:getLastMessagePreview(chat)}),/*#__PURE__*/_jsx(Typography,{variant:\"caption\",color:\"text.secondary\",children:formatLastMessageTime(chat)})]})})]},chat.id))})]})}),/*#__PURE__*/_jsx(Grid,{size:{xs:12,md:8},children:/*#__PURE__*/_jsx(Paper,{sx:{height:'100%'},children:selectedChat?/*#__PURE__*/_jsx(GroupChat,{chatId:selectedChat.id,chat:selectedChat,onClose:()=>setSelectedChat(null)}):/*#__PURE__*/_jsxs(Box,{sx:{height:'100%',display:'flex',flexDirection:'column',alignItems:'center',justifyContent:'center',gap:2},children:[/*#__PURE__*/_jsx(Message,{sx:{fontSize:64,color:'text.secondary'}}),/*#__PURE__*/_jsx(Typography,{variant:\"h6\",color:\"text.secondary\",children:\"Select a conversation to start messaging\"}),/*#__PURE__*/_jsx(Typography,{variant:\"body2\",color:\"text.secondary\",children:\"Choose from your existing conversations or start a new one\"})]})})})]}),/*#__PURE__*/_jsx(Fab,{color:\"primary\",\"aria-label\":\"new chat\",sx:{position:'fixed',bottom:16,right:16},onClick:()=>setNewChatOpen(true),children:/*#__PURE__*/_jsx(Add,{})}),/*#__PURE__*/_jsxs(Dialog,{open:newChatOpen,onClose:()=>setNewChatOpen(false),maxWidth:\"sm\",fullWidth:true,children:[/*#__PURE__*/_jsx(DialogTitle,{children:\"Start New Conversation\"}),/*#__PURE__*/_jsx(DialogContent,{children:/*#__PURE__*/_jsxs(Box,{sx:{pt:1},children:[/*#__PURE__*/_jsxs(FormControl,{fullWidth:true,sx:{mb:2},children:[/*#__PURE__*/_jsx(InputLabel,{children:\"Chat Type\"}),/*#__PURE__*/_jsxs(Select,{value:chatType,label:\"Chat Type\",onChange:e=>setChatType(e.target.value),children:[/*#__PURE__*/_jsx(MenuItem,{value:\"private\",children:/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center',gap:1},children:[/*#__PURE__*/_jsx(Person,{}),\"Private Chat\"]})}),/*#__PURE__*/_jsx(MenuItem,{value:\"group\",children:/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center',gap:1},children:[/*#__PURE__*/_jsx(Group,{}),\"Group Chat\"]})})]})]}),chatType==='private'?/*#__PURE__*/_jsx(TextField,{fullWidth:true,label:\"User Email\",value:newChatData.participantEmail,onChange:e=>setNewChatData(_objectSpread(_objectSpread({},newChatData),{},{participantEmail:e.target.value})),placeholder:\"Enter email address of the person you want to chat with\"}):/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(TextField,{fullWidth:true,label:\"Group Name\",value:newChatData.name,onChange:e=>setNewChatData(_objectSpread(_objectSpread({},newChatData),{},{name:e.target.value})),sx:{mb:2}}),/*#__PURE__*/_jsx(TextField,{fullWidth:true,label:\"Description (optional)\",multiline:true,rows:2,value:newChatData.description,onChange:e=>setNewChatData(_objectSpread(_objectSpread({},newChatData),{},{description:e.target.value}))})]})]})}),/*#__PURE__*/_jsxs(DialogActions,{children:[/*#__PURE__*/_jsx(Button,{onClick:()=>setNewChatOpen(false),children:\"Cancel\"}),/*#__PURE__*/_jsx(Button,{onClick:chatType==='private'?handleCreatePrivateChat:handleCreateGroupChat,variant:\"contained\",disabled:loading||chatType==='private'&&!newChatData.participantEmail.trim()||chatType==='group'&&!newChatData.name.trim(),children:loading?/*#__PURE__*/_jsx(CircularProgress,{size:20}):'Start Chat'})]})]})]});};export default Chat;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Container", "Grid", "Paper", "List", "ListItem", "ListItemAvatar", "ListItemText", "Avatar", "Typography", "Box", "Badge", "TextField", "InputAdornment", "Fab", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "<PERSON><PERSON>", "FormControl", "InputLabel", "Select", "MenuItem", "CircularProgress", "Search", "Add", "Message", "Group", "Person", "formatDistanceToNow", "ChatService", "StudyGroupService", "useAuth", "GroupChat", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "Cha<PERSON>", "user", "chats", "setChats", "selectedC<PERSON>", "setSelectedChat", "loading", "setLoading", "searchTerm", "setSearchTerm", "newChatOpen", "setNewChatOpen", "chatType", "setChatType", "studyGroups", "setStudyGroups", "selectedUsers", "setSelectedUsers", "newChatData", "setNewChatData", "name", "description", "participantEmail", "unsubscribe", "subscribeToUserChats", "uid", "userChats", "loadStudyGroups", "groups", "getUserStudyGroups", "error", "console", "filteredChats", "filter", "chat", "searchLower", "toLowerCase", "type", "_chat$name", "includes", "_chat$participantDeta", "participantDetails", "some", "p", "displayName", "email", "handleCreatePrivateChat", "trim", "_user$email", "otherUserProfile", "split", "profilePicture", "currentUserProfile", "photoURL", "chatData", "participantId", "initialMessage", "chatId", "createPrivateChat", "handleCreateGroupChat", "log", "getChatDisplayName", "_chat$participantDeta2", "otherParticipant", "find", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_chat$name2", "_chat$participantDeta3", "_otherParticipant$dis", "getLastMessagePreview", "lastMessage", "content", "isOwnMessage", "senderId", "prefix", "concat", "length", "substring", "formatLastMessageTime", "lastMessageAt", "date", "toDate", "Date", "addSuffix", "getUnreadCount", "_chat$unreadCount", "unreadCount", "max<PERSON><PERSON><PERSON>", "children", "display", "justifyContent", "alignItems", "height", "sx", "py", "variant", "component", "gutterBottom", "container", "spacing", "size", "xs", "md", "flexDirection", "borderBottom", "borderColor", "fullWidth", "placeholder", "value", "onChange", "e", "target", "InputProps", "startAdornment", "position", "flexGrow", "overflow", "textAlign", "color", "map", "onClick", "cursor", "bgcolor", "id", "badgeContent", "invisible", "src", "startsWith", "undefined", "primary", "gap", "noWrap", "fontSize", "secondary", "onClose", "bottom", "right", "open", "pt", "mb", "label", "_objectSpread", "multiline", "rows", "disabled"], "sources": ["C:/Users/<USER>/OneDrive/ドキュメント/Projects/StudyHub/studyhub-app/src/pages/Chat.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Container,\n  Grid,\n  Paper,\n  List,\n  ListItem,\n  ListItemAvatar,\n  ListItemText,\n  Avatar,\n  Typography,\n  Box,\n  Badge,\n  IconButton,\n  TextField,\n  InputAdornment,\n  Fab,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  Button,\n  FormControl,\n  InputLabel,\n  Select,\n  MenuItem,\n  Chip,\n  CircularProgress,\n} from '@mui/material';\nimport {\n  Search,\n  Add,\n  Message,\n  Group,\n  Person,\n  Close,\n} from '@mui/icons-material';\nimport { formatDistanceToNow } from 'date-fns';\nimport { ChatService } from '../services/chatService';\nimport { StudyGroupService } from '../services/studyGroupService';\nimport { Chat as ChatType, CreatePrivateChatData } from '../types/chat';\nimport { StudyGroup } from '../types/studyGroup';\nimport { useAuth } from '../contexts/AuthContext';\nimport GroupChat from '../components/Chat/GroupChat';\n\nconst Chat: React.FC = () => {\n  const { user } = useAuth();\n  const [chats, setChats] = useState<ChatType[]>([]);\n  const [selectedChat, setSelectedChat] = useState<ChatType | null>(null);\n  const [loading, setLoading] = useState(true);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [newChatOpen, setNewChatOpen] = useState(false);\n  const [chatType, setChatType] = useState<'private' | 'group'>('private');\n  const [studyGroups, setStudyGroups] = useState<StudyGroup[]>([]);\n  const [selectedUsers, setSelectedUsers] = useState<string[]>([]);\n  const [newChatData, setNewChatData] = useState({\n    name: '',\n    description: '',\n    participantEmail: '',\n  });\n\n  useEffect(() => {\n    if (!user) return;\n\n    // Subscribe to user's chats\n    const unsubscribe = ChatService.subscribeToUserChats(user.uid, (userChats) => {\n      setChats(userChats);\n      setLoading(false);\n    });\n\n    // Load user's study groups for group chat creation\n    loadStudyGroups();\n\n    return unsubscribe;\n  }, [user]);\n\n  const loadStudyGroups = async () => {\n    if (!user) return;\n\n    try {\n      const groups = await StudyGroupService.getUserStudyGroups(user.uid);\n      setStudyGroups(groups);\n    } catch (error) {\n      console.error('Error loading study groups:', error);\n    }\n  };\n\n  const filteredChats = chats.filter(chat => {\n    if (!searchTerm) return true;\n    \n    const searchLower = searchTerm.toLowerCase();\n    \n    if (chat.type === 'group') {\n      return chat.name?.toLowerCase().includes(searchLower);\n    } else {\n      // For private chats, search by participant names\n      return chat.participantDetails?.some(p => \n        p.displayName.toLowerCase().includes(searchLower) ||\n        p.email.toLowerCase().includes(searchLower)\n      );\n    }\n  });\n\n  const handleCreatePrivateChat = async () => {\n    if (!user || !newChatData.participantEmail.trim()) return;\n\n    try {\n      setLoading(true);\n      \n      // TODO: Find user by email and get their profile\n      // For now, we'll create a mock profile\n      const otherUserProfile = {\n        displayName: newChatData.participantEmail.split('@')[0],\n        email: newChatData.participantEmail,\n        profilePicture: '',\n      };\n\n      const currentUserProfile = {\n        displayName: user.displayName || user.email?.split('@')[0] || 'User',\n        email: user.email || '',\n        profilePicture: user.photoURL || '',\n      };\n\n      const chatData: CreatePrivateChatData = {\n        participantId: 'temp-user-id', // TODO: Get actual user ID\n        initialMessage: `Hi! I'd like to connect with you on StudyHub.`,\n      };\n\n      const chatId = await ChatService.createPrivateChat(\n        user.uid,\n        chatData,\n        currentUserProfile,\n        otherUserProfile\n      );\n\n      setNewChatOpen(false);\n      setNewChatData({ name: '', description: '', participantEmail: '' });\n    } catch (error) {\n      console.error('Error creating private chat:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleCreateGroupChat = async () => {\n    if (!user || !newChatData.name.trim()) return;\n\n    try {\n      setLoading(true);\n      \n      // TODO: Implement group chat creation\n      console.log('Creating group chat:', newChatData);\n      \n      setNewChatOpen(false);\n      setNewChatData({ name: '', description: '', participantEmail: '' });\n    } catch (error) {\n      console.error('Error creating group chat:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const getChatDisplayName = (chat: ChatType) => {\n    if (chat.type === 'group') {\n      return chat.name || 'Group Chat';\n    } else {\n      // For private chats, show the other participant's name\n      const otherParticipant = chat.participantDetails?.find(p => p.uid !== user?.uid);\n      return otherParticipant?.displayName || 'Private Chat';\n    }\n  };\n\n  const getChatAvatar = (chat: ChatType) => {\n    if (chat.type === 'group') {\n      return chat.name?.[0] || 'G';\n    } else {\n      const otherParticipant = chat.participantDetails?.find(p => p.uid !== user?.uid);\n      return otherParticipant?.profilePicture || otherParticipant?.displayName?.[0] || 'U';\n    }\n  };\n\n  const getLastMessagePreview = (chat: ChatType) => {\n    if (!chat.lastMessage) return 'No messages yet';\n    \n    const content = chat.lastMessage.content;\n    const isOwnMessage = chat.lastMessage.senderId === user?.uid;\n    const prefix = isOwnMessage ? 'You: ' : '';\n    \n    return `${prefix}${content.length > 50 ? content.substring(0, 50) + '...' : content}`;\n  };\n\n  const formatLastMessageTime = (chat: ChatType) => {\n    if (!chat.lastMessageAt) return '';\n    \n    const date = chat.lastMessageAt.toDate ? chat.lastMessageAt.toDate() : new Date(chat.lastMessageAt);\n    return formatDistanceToNow(date, { addSuffix: true });\n  };\n\n  const getUnreadCount = (chat: ChatType) => {\n    return chat.unreadCount?.[user?.uid || ''] || 0;\n  };\n\n  if (loading) {\n    return (\n      <Container maxWidth=\"lg\">\n        <Box display=\"flex\" justifyContent=\"center\" alignItems=\"center\" height=\"400px\">\n          <CircularProgress />\n        </Box>\n      </Container>\n    );\n  }\n\n  return (\n    <Container maxWidth=\"lg\" sx={{ py: 3 }}>\n      <Typography variant=\"h4\" component=\"h1\" gutterBottom>\n        Messages\n      </Typography>\n\n      <Grid container spacing={3} sx={{ height: 'calc(100vh - 200px)' }}>\n        {/* Chat List */}\n        <Grid size={{ xs: 12, md: 4 }}>\n          <Paper sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>\n            {/* Search */}\n            <Box sx={{ p: 2, borderBottom: 1, borderColor: 'divider' }}>\n              <TextField\n                fullWidth\n                placeholder=\"Search conversations...\"\n                value={searchTerm}\n                onChange={(e) => setSearchTerm(e.target.value)}\n                InputProps={{\n                  startAdornment: (\n                    <InputAdornment position=\"start\">\n                      <Search />\n                    </InputAdornment>\n                  ),\n                }}\n              />\n            </Box>\n\n            {/* Chat List */}\n            <List sx={{ flexGrow: 1, overflow: 'auto', py: 0 }}>\n              {filteredChats.length === 0 ? (\n                <Box sx={{ p: 3, textAlign: 'center' }}>\n                  <Typography variant=\"body2\" color=\"text.secondary\">\n                    No conversations yet\n                  </Typography>\n                  <Typography variant=\"caption\" color=\"text.secondary\">\n                    Start a new conversation to get started\n                  </Typography>\n                </Box>\n              ) : (\n                filteredChats.map((chat) => (\n                  <ListItem\n                    key={chat.id}\n                    onClick={() => setSelectedChat(chat)}\n                    sx={{\n                      cursor: 'pointer',\n                      borderBottom: 1,\n                      borderColor: 'divider',\n                      bgcolor: selectedChat?.id === chat.id ? 'action.selected' : 'transparent',\n                      '&:hover': {\n                        bgcolor: 'action.hover',\n                      },\n                    }}\n                  >\n                    <ListItemAvatar>\n                      <Badge\n                        badgeContent={getUnreadCount(chat)}\n                        color=\"primary\"\n                        invisible={getUnreadCount(chat) === 0}\n                      >\n                        <Avatar src={typeof getChatAvatar(chat) === 'string' && getChatAvatar(chat).startsWith('http') ? getChatAvatar(chat) : undefined}>\n                          {typeof getChatAvatar(chat) === 'string' && !getChatAvatar(chat).startsWith('http') ? getChatAvatar(chat) : ''}\n                        </Avatar>\n                      </Badge>\n                    </ListItemAvatar>\n                    <ListItemText\n                      primary={\n                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\n                          <Typography variant=\"subtitle2\" noWrap>\n                            {getChatDisplayName(chat)}\n                          </Typography>\n                          {chat.type === 'group' && (\n                            <Group fontSize=\"small\" color=\"action\" />\n                          )}\n                        </Box>\n                      }\n                      secondary={\n                        <Box>\n                          <Typography variant=\"body2\" color=\"text.secondary\" noWrap>\n                            {getLastMessagePreview(chat)}\n                          </Typography>\n                          <Typography variant=\"caption\" color=\"text.secondary\">\n                            {formatLastMessageTime(chat)}\n                          </Typography>\n                        </Box>\n                      }\n                    />\n                  </ListItem>\n                ))\n              )}\n            </List>\n          </Paper>\n        </Grid>\n\n        {/* Chat Area */}\n        <Grid size={{ xs: 12, md: 8 }}>\n          <Paper sx={{ height: '100%' }}>\n            {selectedChat ? (\n              <GroupChat\n                chatId={selectedChat.id}\n                chat={selectedChat}\n                onClose={() => setSelectedChat(null)}\n              />\n            ) : (\n              <Box\n                sx={{\n                  height: '100%',\n                  display: 'flex',\n                  flexDirection: 'column',\n                  alignItems: 'center',\n                  justifyContent: 'center',\n                  gap: 2,\n                }}\n              >\n                <Message sx={{ fontSize: 64, color: 'text.secondary' }} />\n                <Typography variant=\"h6\" color=\"text.secondary\">\n                  Select a conversation to start messaging\n                </Typography>\n                <Typography variant=\"body2\" color=\"text.secondary\">\n                  Choose from your existing conversations or start a new one\n                </Typography>\n              </Box>\n            )}\n          </Paper>\n        </Grid>\n      </Grid>\n\n      {/* Floating Action Button */}\n      <Fab\n        color=\"primary\"\n        aria-label=\"new chat\"\n        sx={{ position: 'fixed', bottom: 16, right: 16 }}\n        onClick={() => setNewChatOpen(true)}\n      >\n        <Add />\n      </Fab>\n\n      {/* New Chat Dialog */}\n      <Dialog open={newChatOpen} onClose={() => setNewChatOpen(false)} maxWidth=\"sm\" fullWidth>\n        <DialogTitle>Start New Conversation</DialogTitle>\n        <DialogContent>\n          <Box sx={{ pt: 1 }}>\n            <FormControl fullWidth sx={{ mb: 2 }}>\n              <InputLabel>Chat Type</InputLabel>\n              <Select\n                value={chatType}\n                label=\"Chat Type\"\n                onChange={(e) => setChatType(e.target.value as 'private' | 'group')}\n              >\n                <MenuItem value=\"private\">\n                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\n                    <Person />\n                    Private Chat\n                  </Box>\n                </MenuItem>\n                <MenuItem value=\"group\">\n                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\n                    <Group />\n                    Group Chat\n                  </Box>\n                </MenuItem>\n              </Select>\n            </FormControl>\n\n            {chatType === 'private' ? (\n              <TextField\n                fullWidth\n                label=\"User Email\"\n                value={newChatData.participantEmail}\n                onChange={(e) => setNewChatData({ ...newChatData, participantEmail: e.target.value })}\n                placeholder=\"Enter email address of the person you want to chat with\"\n              />\n            ) : (\n              <>\n                <TextField\n                  fullWidth\n                  label=\"Group Name\"\n                  value={newChatData.name}\n                  onChange={(e) => setNewChatData({ ...newChatData, name: e.target.value })}\n                  sx={{ mb: 2 }}\n                />\n                <TextField\n                  fullWidth\n                  label=\"Description (optional)\"\n                  multiline\n                  rows={2}\n                  value={newChatData.description}\n                  onChange={(e) => setNewChatData({ ...newChatData, description: e.target.value })}\n                />\n              </>\n            )}\n          </Box>\n        </DialogContent>\n        <DialogActions>\n          <Button onClick={() => setNewChatOpen(false)}>Cancel</Button>\n          <Button\n            onClick={chatType === 'private' ? handleCreatePrivateChat : handleCreateGroupChat}\n            variant=\"contained\"\n            disabled={\n              loading ||\n              (chatType === 'private' && !newChatData.participantEmail.trim()) ||\n              (chatType === 'group' && !newChatData.name.trim())\n            }\n          >\n            {loading ? <CircularProgress size={20} /> : 'Start Chat'}\n          </Button>\n        </DialogActions>\n      </Dialog>\n    </Container>\n  );\n};\n\nexport default Chat;\n"], "mappings": "gLAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,OACEC,SAAS,CACTC,IAAI,CACJC,KAAK,CACLC,IAAI,CACJC,QAAQ,CACRC,cAAc,CACdC,YAAY,CACZC,MAAM,CACNC,UAAU,CACVC,GAAG,CACHC,KAAK,CAELC,SAAS,CACTC,cAAc,CACdC,GAAG,CACHC,MAAM,CACNC,WAAW,CACXC,aAAa,CACbC,aAAa,CACbC,MAAM,CACNC,WAAW,CACXC,UAAU,CACVC,MAAM,CACNC,QAAQ,CAERC,gBAAgB,KACX,eAAe,CACtB,OACEC,MAAM,CACNC,GAAG,CACHC,OAAO,CACPC,KAAK,CACLC,MAAM,KAED,qBAAqB,CAC5B,OAASC,mBAAmB,KAAQ,UAAU,CAC9C,OAASC,WAAW,KAAQ,yBAAyB,CACrD,OAASC,iBAAiB,KAAQ,+BAA+B,CAGjE,OAASC,OAAO,KAAQ,yBAAyB,CACjD,MAAO,CAAAC,SAAS,KAAM,8BAA8B,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBAErD,KAAM,CAAAC,IAAc,CAAGA,CAAA,GAAM,CAC3B,KAAM,CAAEC,IAAK,CAAC,CAAGT,OAAO,CAAC,CAAC,CAC1B,KAAM,CAACU,KAAK,CAAEC,QAAQ,CAAC,CAAG7C,QAAQ,CAAa,EAAE,CAAC,CAClD,KAAM,CAAC8C,YAAY,CAAEC,eAAe,CAAC,CAAG/C,QAAQ,CAAkB,IAAI,CAAC,CACvE,KAAM,CAACgD,OAAO,CAAEC,UAAU,CAAC,CAAGjD,QAAQ,CAAC,IAAI,CAAC,CAC5C,KAAM,CAACkD,UAAU,CAAEC,aAAa,CAAC,CAAGnD,QAAQ,CAAC,EAAE,CAAC,CAChD,KAAM,CAACoD,WAAW,CAAEC,cAAc,CAAC,CAAGrD,QAAQ,CAAC,KAAK,CAAC,CACrD,KAAM,CAACsD,QAAQ,CAAEC,WAAW,CAAC,CAAGvD,QAAQ,CAAsB,SAAS,CAAC,CACxE,KAAM,CAACwD,WAAW,CAAEC,cAAc,CAAC,CAAGzD,QAAQ,CAAe,EAAE,CAAC,CAChE,KAAM,CAAC0D,aAAa,CAAEC,gBAAgB,CAAC,CAAG3D,QAAQ,CAAW,EAAE,CAAC,CAChE,KAAM,CAAC4D,WAAW,CAAEC,cAAc,CAAC,CAAG7D,QAAQ,CAAC,CAC7C8D,IAAI,CAAE,EAAE,CACRC,WAAW,CAAE,EAAE,CACfC,gBAAgB,CAAE,EACpB,CAAC,CAAC,CAEF/D,SAAS,CAAC,IAAM,CACd,GAAI,CAAC0C,IAAI,CAAE,OAEX;AACA,KAAM,CAAAsB,WAAW,CAAGjC,WAAW,CAACkC,oBAAoB,CAACvB,IAAI,CAACwB,GAAG,CAAGC,SAAS,EAAK,CAC5EvB,QAAQ,CAACuB,SAAS,CAAC,CACnBnB,UAAU,CAAC,KAAK,CAAC,CACnB,CAAC,CAAC,CAEF;AACAoB,eAAe,CAAC,CAAC,CAEjB,MAAO,CAAAJ,WAAW,CACpB,CAAC,CAAE,CAACtB,IAAI,CAAC,CAAC,CAEV,KAAM,CAAA0B,eAAe,CAAG,KAAAA,CAAA,GAAY,CAClC,GAAI,CAAC1B,IAAI,CAAE,OAEX,GAAI,CACF,KAAM,CAAA2B,MAAM,CAAG,KAAM,CAAArC,iBAAiB,CAACsC,kBAAkB,CAAC5B,IAAI,CAACwB,GAAG,CAAC,CACnEV,cAAc,CAACa,MAAM,CAAC,CACxB,CAAE,MAAOE,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,6BAA6B,CAAEA,KAAK,CAAC,CACrD,CACF,CAAC,CAED,KAAM,CAAAE,aAAa,CAAG9B,KAAK,CAAC+B,MAAM,CAACC,IAAI,EAAI,CACzC,GAAI,CAAC1B,UAAU,CAAE,MAAO,KAAI,CAE5B,KAAM,CAAA2B,WAAW,CAAG3B,UAAU,CAAC4B,WAAW,CAAC,CAAC,CAE5C,GAAIF,IAAI,CAACG,IAAI,GAAK,OAAO,CAAE,KAAAC,UAAA,CACzB,OAAAA,UAAA,CAAOJ,IAAI,CAACd,IAAI,UAAAkB,UAAA,iBAATA,UAAA,CAAWF,WAAW,CAAC,CAAC,CAACG,QAAQ,CAACJ,WAAW,CAAC,CACvD,CAAC,IAAM,KAAAK,qBAAA,CACL;AACA,OAAAA,qBAAA,CAAON,IAAI,CAACO,kBAAkB,UAAAD,qBAAA,iBAAvBA,qBAAA,CAAyBE,IAAI,CAACC,CAAC,EACpCA,CAAC,CAACC,WAAW,CAACR,WAAW,CAAC,CAAC,CAACG,QAAQ,CAACJ,WAAW,CAAC,EACjDQ,CAAC,CAACE,KAAK,CAACT,WAAW,CAAC,CAAC,CAACG,QAAQ,CAACJ,WAAW,CAC5C,CAAC,CACH,CACF,CAAC,CAAC,CAEF,KAAM,CAAAW,uBAAuB,CAAG,KAAAA,CAAA,GAAY,CAC1C,GAAI,CAAC7C,IAAI,EAAI,CAACiB,WAAW,CAACI,gBAAgB,CAACyB,IAAI,CAAC,CAAC,CAAE,OAEnD,GAAI,KAAAC,WAAA,CACFzC,UAAU,CAAC,IAAI,CAAC,CAEhB;AACA;AACA,KAAM,CAAA0C,gBAAgB,CAAG,CACvBL,WAAW,CAAE1B,WAAW,CAACI,gBAAgB,CAAC4B,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CACvDL,KAAK,CAAE3B,WAAW,CAACI,gBAAgB,CACnC6B,cAAc,CAAE,EAClB,CAAC,CAED,KAAM,CAAAC,kBAAkB,CAAG,CACzBR,WAAW,CAAE3C,IAAI,CAAC2C,WAAW,IAAAI,WAAA,CAAI/C,IAAI,CAAC4C,KAAK,UAAAG,WAAA,iBAAVA,WAAA,CAAYE,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAI,MAAM,CACpEL,KAAK,CAAE5C,IAAI,CAAC4C,KAAK,EAAI,EAAE,CACvBM,cAAc,CAAElD,IAAI,CAACoD,QAAQ,EAAI,EACnC,CAAC,CAED,KAAM,CAAAC,QAA+B,CAAG,CACtCC,aAAa,CAAE,cAAc,CAAE;AAC/BC,cAAc,gDAChB,CAAC,CAED,KAAM,CAAAC,MAAM,CAAG,KAAM,CAAAnE,WAAW,CAACoE,iBAAiB,CAChDzD,IAAI,CAACwB,GAAG,CACR6B,QAAQ,CACRF,kBAAkB,CAClBH,gBACF,CAAC,CAEDtC,cAAc,CAAC,KAAK,CAAC,CACrBQ,cAAc,CAAC,CAAEC,IAAI,CAAE,EAAE,CAAEC,WAAW,CAAE,EAAE,CAAEC,gBAAgB,CAAE,EAAG,CAAC,CAAC,CACrE,CAAE,MAAOQ,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,8BAA8B,CAAEA,KAAK,CAAC,CACtD,CAAC,OAAS,CACRvB,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED,KAAM,CAAAoD,qBAAqB,CAAG,KAAAA,CAAA,GAAY,CACxC,GAAI,CAAC1D,IAAI,EAAI,CAACiB,WAAW,CAACE,IAAI,CAAC2B,IAAI,CAAC,CAAC,CAAE,OAEvC,GAAI,CACFxC,UAAU,CAAC,IAAI,CAAC,CAEhB;AACAwB,OAAO,CAAC6B,GAAG,CAAC,sBAAsB,CAAE1C,WAAW,CAAC,CAEhDP,cAAc,CAAC,KAAK,CAAC,CACrBQ,cAAc,CAAC,CAAEC,IAAI,CAAE,EAAE,CAAEC,WAAW,CAAE,EAAE,CAAEC,gBAAgB,CAAE,EAAG,CAAC,CAAC,CACrE,CAAE,MAAOQ,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,4BAA4B,CAAEA,KAAK,CAAC,CACpD,CAAC,OAAS,CACRvB,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED,KAAM,CAAAsD,kBAAkB,CAAI3B,IAAc,EAAK,CAC7C,GAAIA,IAAI,CAACG,IAAI,GAAK,OAAO,CAAE,CACzB,MAAO,CAAAH,IAAI,CAACd,IAAI,EAAI,YAAY,CAClC,CAAC,IAAM,KAAA0C,sBAAA,CACL;AACA,KAAM,CAAAC,gBAAgB,EAAAD,sBAAA,CAAG5B,IAAI,CAACO,kBAAkB,UAAAqB,sBAAA,iBAAvBA,sBAAA,CAAyBE,IAAI,CAACrB,CAAC,EAAIA,CAAC,CAAClB,GAAG,IAAKxB,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAEwB,GAAG,EAAC,CAChF,MAAO,CAAAsC,gBAAgB,SAAhBA,gBAAgB,iBAAhBA,gBAAgB,CAAEnB,WAAW,GAAI,cAAc,CACxD,CACF,CAAC,CAED,KAAM,CAAAqB,aAAa,CAAI/B,IAAc,EAAK,CACxC,GAAIA,IAAI,CAACG,IAAI,GAAK,OAAO,CAAE,KAAA6B,WAAA,CACzB,MAAO,EAAAA,WAAA,CAAAhC,IAAI,CAACd,IAAI,UAAA8C,WAAA,iBAATA,WAAA,CAAY,CAAC,CAAC,GAAI,GAAG,CAC9B,CAAC,IAAM,KAAAC,sBAAA,CAAAC,qBAAA,CACL,KAAM,CAAAL,gBAAgB,EAAAI,sBAAA,CAAGjC,IAAI,CAACO,kBAAkB,UAAA0B,sBAAA,iBAAvBA,sBAAA,CAAyBH,IAAI,CAACrB,CAAC,EAAIA,CAAC,CAAClB,GAAG,IAAKxB,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAEwB,GAAG,EAAC,CAChF,MAAO,CAAAsC,gBAAgB,SAAhBA,gBAAgB,iBAAhBA,gBAAgB,CAAEZ,cAAc,IAAIY,gBAAgB,SAAhBA,gBAAgB,kBAAAK,qBAAA,CAAhBL,gBAAgB,CAAEnB,WAAW,UAAAwB,qBAAA,iBAA7BA,qBAAA,CAAgC,CAAC,CAAC,GAAI,GAAG,CACtF,CACF,CAAC,CAED,KAAM,CAAAC,qBAAqB,CAAInC,IAAc,EAAK,CAChD,GAAI,CAACA,IAAI,CAACoC,WAAW,CAAE,MAAO,iBAAiB,CAE/C,KAAM,CAAAC,OAAO,CAAGrC,IAAI,CAACoC,WAAW,CAACC,OAAO,CACxC,KAAM,CAAAC,YAAY,CAAGtC,IAAI,CAACoC,WAAW,CAACG,QAAQ,IAAKxE,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAEwB,GAAG,EAC5D,KAAM,CAAAiD,MAAM,CAAGF,YAAY,CAAG,OAAO,CAAG,EAAE,CAE1C,SAAAG,MAAA,CAAUD,MAAM,EAAAC,MAAA,CAAGJ,OAAO,CAACK,MAAM,CAAG,EAAE,CAAGL,OAAO,CAACM,SAAS,CAAC,CAAC,CAAE,EAAE,CAAC,CAAG,KAAK,CAAGN,OAAO,EACrF,CAAC,CAED,KAAM,CAAAO,qBAAqB,CAAI5C,IAAc,EAAK,CAChD,GAAI,CAACA,IAAI,CAAC6C,aAAa,CAAE,MAAO,EAAE,CAElC,KAAM,CAAAC,IAAI,CAAG9C,IAAI,CAAC6C,aAAa,CAACE,MAAM,CAAG/C,IAAI,CAAC6C,aAAa,CAACE,MAAM,CAAC,CAAC,CAAG,GAAI,CAAAC,IAAI,CAAChD,IAAI,CAAC6C,aAAa,CAAC,CACnG,MAAO,CAAA1F,mBAAmB,CAAC2F,IAAI,CAAE,CAAEG,SAAS,CAAE,IAAK,CAAC,CAAC,CACvD,CAAC,CAED,KAAM,CAAAC,cAAc,CAAIlD,IAAc,EAAK,KAAAmD,iBAAA,CACzC,MAAO,EAAAA,iBAAA,CAAAnD,IAAI,CAACoD,WAAW,UAAAD,iBAAA,iBAAhBA,iBAAA,CAAmB,CAAApF,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAEwB,GAAG,GAAI,EAAE,CAAC,GAAI,CAAC,CACjD,CAAC,CAED,GAAInB,OAAO,CAAE,CACX,mBACEX,IAAA,CAACnC,SAAS,EAAC+H,QAAQ,CAAC,IAAI,CAAAC,QAAA,cACtB7F,IAAA,CAAC1B,GAAG,EAACwH,OAAO,CAAC,MAAM,CAACC,cAAc,CAAC,QAAQ,CAACC,UAAU,CAAC,QAAQ,CAACC,MAAM,CAAC,OAAO,CAAAJ,QAAA,cAC5E7F,IAAA,CAACZ,gBAAgB,GAAE,CAAC,CACjB,CAAC,CACG,CAAC,CAEhB,CAEA,mBACEc,KAAA,CAACrC,SAAS,EAAC+H,QAAQ,CAAC,IAAI,CAACM,EAAE,CAAE,CAAEC,EAAE,CAAE,CAAE,CAAE,CAAAN,QAAA,eACrC7F,IAAA,CAAC3B,UAAU,EAAC+H,OAAO,CAAC,IAAI,CAACC,SAAS,CAAC,IAAI,CAACC,YAAY,MAAAT,QAAA,CAAC,UAErD,CAAY,CAAC,cAEb3F,KAAA,CAACpC,IAAI,EAACyI,SAAS,MAACC,OAAO,CAAE,CAAE,CAACN,EAAE,CAAE,CAAED,MAAM,CAAE,qBAAsB,CAAE,CAAAJ,QAAA,eAEhE7F,IAAA,CAAClC,IAAI,EAAC2I,IAAI,CAAE,CAAEC,EAAE,CAAE,EAAE,CAAEC,EAAE,CAAE,CAAE,CAAE,CAAAd,QAAA,cAC5B3F,KAAA,CAACnC,KAAK,EAACmI,EAAE,CAAE,CAAED,MAAM,CAAE,MAAM,CAAEH,OAAO,CAAE,MAAM,CAAEc,aAAa,CAAE,QAAS,CAAE,CAAAf,QAAA,eAEtE7F,IAAA,CAAC1B,GAAG,EAAC4H,EAAE,CAAE,CAAElD,CAAC,CAAE,CAAC,CAAE6D,YAAY,CAAE,CAAC,CAAEC,WAAW,CAAE,SAAU,CAAE,CAAAjB,QAAA,cACzD7F,IAAA,CAACxB,SAAS,EACRuI,SAAS,MACTC,WAAW,CAAC,yBAAyB,CACrCC,KAAK,CAAEpG,UAAW,CAClBqG,QAAQ,CAAGC,CAAC,EAAKrG,aAAa,CAACqG,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE,CAC/CI,UAAU,CAAE,CACVC,cAAc,cACZtH,IAAA,CAACvB,cAAc,EAAC8I,QAAQ,CAAC,OAAO,CAAA1B,QAAA,cAC9B7F,IAAA,CAACX,MAAM,GAAE,CAAC,CACI,CAEpB,CAAE,CACH,CAAC,CACC,CAAC,cAGNW,IAAA,CAAChC,IAAI,EAACkI,EAAE,CAAE,CAAEsB,QAAQ,CAAE,CAAC,CAAEC,QAAQ,CAAE,MAAM,CAAEtB,EAAE,CAAE,CAAE,CAAE,CAAAN,QAAA,CAChDxD,aAAa,CAAC4C,MAAM,GAAK,CAAC,cACzB/E,KAAA,CAAC5B,GAAG,EAAC4H,EAAE,CAAE,CAAElD,CAAC,CAAE,CAAC,CAAE0E,SAAS,CAAE,QAAS,CAAE,CAAA7B,QAAA,eACrC7F,IAAA,CAAC3B,UAAU,EAAC+H,OAAO,CAAC,OAAO,CAACuB,KAAK,CAAC,gBAAgB,CAAA9B,QAAA,CAAC,sBAEnD,CAAY,CAAC,cACb7F,IAAA,CAAC3B,UAAU,EAAC+H,OAAO,CAAC,SAAS,CAACuB,KAAK,CAAC,gBAAgB,CAAA9B,QAAA,CAAC,yCAErD,CAAY,CAAC,EACV,CAAC,CAENxD,aAAa,CAACuF,GAAG,CAAErF,IAAI,eACrBrC,KAAA,CAACjC,QAAQ,EAEP4J,OAAO,CAAEA,CAAA,GAAMnH,eAAe,CAAC6B,IAAI,CAAE,CACrC2D,EAAE,CAAE,CACF4B,MAAM,CAAE,SAAS,CACjBjB,YAAY,CAAE,CAAC,CACfC,WAAW,CAAE,SAAS,CACtBiB,OAAO,CAAE,CAAAtH,YAAY,SAAZA,YAAY,iBAAZA,YAAY,CAAEuH,EAAE,IAAKzF,IAAI,CAACyF,EAAE,CAAG,iBAAiB,CAAG,aAAa,CACzE,SAAS,CAAE,CACTD,OAAO,CAAE,cACX,CACF,CAAE,CAAAlC,QAAA,eAEF7F,IAAA,CAAC9B,cAAc,EAAA2H,QAAA,cACb7F,IAAA,CAACzB,KAAK,EACJ0J,YAAY,CAAExC,cAAc,CAAClD,IAAI,CAAE,CACnCoF,KAAK,CAAC,SAAS,CACfO,SAAS,CAAEzC,cAAc,CAAClD,IAAI,CAAC,GAAK,CAAE,CAAAsD,QAAA,cAEtC7F,IAAA,CAAC5B,MAAM,EAAC+J,GAAG,CAAE,MAAO,CAAA7D,aAAa,CAAC/B,IAAI,CAAC,GAAK,QAAQ,EAAI+B,aAAa,CAAC/B,IAAI,CAAC,CAAC6F,UAAU,CAAC,MAAM,CAAC,CAAG9D,aAAa,CAAC/B,IAAI,CAAC,CAAG8F,SAAU,CAAAxC,QAAA,CAC9H,MAAO,CAAAvB,aAAa,CAAC/B,IAAI,CAAC,GAAK,QAAQ,EAAI,CAAC+B,aAAa,CAAC/B,IAAI,CAAC,CAAC6F,UAAU,CAAC,MAAM,CAAC,CAAG9D,aAAa,CAAC/B,IAAI,CAAC,CAAG,EAAE,CACxG,CAAC,CACJ,CAAC,CACM,CAAC,cACjBvC,IAAA,CAAC7B,YAAY,EACXmK,OAAO,cACLpI,KAAA,CAAC5B,GAAG,EAAC4H,EAAE,CAAE,CAAEJ,OAAO,CAAE,MAAM,CAAEE,UAAU,CAAE,QAAQ,CAAEuC,GAAG,CAAE,CAAE,CAAE,CAAA1C,QAAA,eACzD7F,IAAA,CAAC3B,UAAU,EAAC+H,OAAO,CAAC,WAAW,CAACoC,MAAM,MAAA3C,QAAA,CACnC3B,kBAAkB,CAAC3B,IAAI,CAAC,CACf,CAAC,CACZA,IAAI,CAACG,IAAI,GAAK,OAAO,eACpB1C,IAAA,CAACR,KAAK,EAACiJ,QAAQ,CAAC,OAAO,CAACd,KAAK,CAAC,QAAQ,CAAE,CACzC,EACE,CACN,CACDe,SAAS,cACPxI,KAAA,CAAC5B,GAAG,EAAAuH,QAAA,eACF7F,IAAA,CAAC3B,UAAU,EAAC+H,OAAO,CAAC,OAAO,CAACuB,KAAK,CAAC,gBAAgB,CAACa,MAAM,MAAA3C,QAAA,CACtDnB,qBAAqB,CAACnC,IAAI,CAAC,CAClB,CAAC,cACbvC,IAAA,CAAC3B,UAAU,EAAC+H,OAAO,CAAC,SAAS,CAACuB,KAAK,CAAC,gBAAgB,CAAA9B,QAAA,CACjDV,qBAAqB,CAAC5C,IAAI,CAAC,CAClB,CAAC,EACV,CACN,CACF,CAAC,GA5CGA,IAAI,CAACyF,EA6CF,CACX,CACF,CACG,CAAC,EACF,CAAC,CACJ,CAAC,cAGPhI,IAAA,CAAClC,IAAI,EAAC2I,IAAI,CAAE,CAAEC,EAAE,CAAE,EAAE,CAAEC,EAAE,CAAE,CAAE,CAAE,CAAAd,QAAA,cAC5B7F,IAAA,CAACjC,KAAK,EAACmI,EAAE,CAAE,CAAED,MAAM,CAAE,MAAO,CAAE,CAAAJ,QAAA,CAC3BpF,YAAY,cACXT,IAAA,CAACF,SAAS,EACRgE,MAAM,CAAErD,YAAY,CAACuH,EAAG,CACxBzF,IAAI,CAAE9B,YAAa,CACnBkI,OAAO,CAAEA,CAAA,GAAMjI,eAAe,CAAC,IAAI,CAAE,CACtC,CAAC,cAEFR,KAAA,CAAC5B,GAAG,EACF4H,EAAE,CAAE,CACFD,MAAM,CAAE,MAAM,CACdH,OAAO,CAAE,MAAM,CACfc,aAAa,CAAE,QAAQ,CACvBZ,UAAU,CAAE,QAAQ,CACpBD,cAAc,CAAE,QAAQ,CACxBwC,GAAG,CAAE,CACP,CAAE,CAAA1C,QAAA,eAEF7F,IAAA,CAACT,OAAO,EAAC2G,EAAE,CAAE,CAAEuC,QAAQ,CAAE,EAAE,CAAEd,KAAK,CAAE,gBAAiB,CAAE,CAAE,CAAC,cAC1D3H,IAAA,CAAC3B,UAAU,EAAC+H,OAAO,CAAC,IAAI,CAACuB,KAAK,CAAC,gBAAgB,CAAA9B,QAAA,CAAC,0CAEhD,CAAY,CAAC,cACb7F,IAAA,CAAC3B,UAAU,EAAC+H,OAAO,CAAC,OAAO,CAACuB,KAAK,CAAC,gBAAgB,CAAA9B,QAAA,CAAC,4DAEnD,CAAY,CAAC,EACV,CACN,CACI,CAAC,CACJ,CAAC,EACH,CAAC,cAGP7F,IAAA,CAACtB,GAAG,EACFiJ,KAAK,CAAC,SAAS,CACf,aAAW,UAAU,CACrBzB,EAAE,CAAE,CAAEqB,QAAQ,CAAE,OAAO,CAAEqB,MAAM,CAAE,EAAE,CAAEC,KAAK,CAAE,EAAG,CAAE,CACjDhB,OAAO,CAAEA,CAAA,GAAM7G,cAAc,CAAC,IAAI,CAAE,CAAA6E,QAAA,cAEpC7F,IAAA,CAACV,GAAG,GAAE,CAAC,CACJ,CAAC,cAGNY,KAAA,CAACvB,MAAM,EAACmK,IAAI,CAAE/H,WAAY,CAAC4H,OAAO,CAAEA,CAAA,GAAM3H,cAAc,CAAC,KAAK,CAAE,CAAC4E,QAAQ,CAAC,IAAI,CAACmB,SAAS,MAAAlB,QAAA,eACtF7F,IAAA,CAACpB,WAAW,EAAAiH,QAAA,CAAC,wBAAsB,CAAa,CAAC,cACjD7F,IAAA,CAACnB,aAAa,EAAAgH,QAAA,cACZ3F,KAAA,CAAC5B,GAAG,EAAC4H,EAAE,CAAE,CAAE6C,EAAE,CAAE,CAAE,CAAE,CAAAlD,QAAA,eACjB3F,KAAA,CAAClB,WAAW,EAAC+H,SAAS,MAACb,EAAE,CAAE,CAAE8C,EAAE,CAAE,CAAE,CAAE,CAAAnD,QAAA,eACnC7F,IAAA,CAACf,UAAU,EAAA4G,QAAA,CAAC,WAAS,CAAY,CAAC,cAClC3F,KAAA,CAAChB,MAAM,EACL+H,KAAK,CAAEhG,QAAS,CAChBgI,KAAK,CAAC,WAAW,CACjB/B,QAAQ,CAAGC,CAAC,EAAKjG,WAAW,CAACiG,CAAC,CAACC,MAAM,CAACH,KAA4B,CAAE,CAAApB,QAAA,eAEpE7F,IAAA,CAACb,QAAQ,EAAC8H,KAAK,CAAC,SAAS,CAAApB,QAAA,cACvB3F,KAAA,CAAC5B,GAAG,EAAC4H,EAAE,CAAE,CAAEJ,OAAO,CAAE,MAAM,CAAEE,UAAU,CAAE,QAAQ,CAAEuC,GAAG,CAAE,CAAE,CAAE,CAAA1C,QAAA,eACzD7F,IAAA,CAACP,MAAM,GAAE,CAAC,eAEZ,EAAK,CAAC,CACE,CAAC,cACXO,IAAA,CAACb,QAAQ,EAAC8H,KAAK,CAAC,OAAO,CAAApB,QAAA,cACrB3F,KAAA,CAAC5B,GAAG,EAAC4H,EAAE,CAAE,CAAEJ,OAAO,CAAE,MAAM,CAAEE,UAAU,CAAE,QAAQ,CAAEuC,GAAG,CAAE,CAAE,CAAE,CAAA1C,QAAA,eACzD7F,IAAA,CAACR,KAAK,GAAE,CAAC,aAEX,EAAK,CAAC,CACE,CAAC,EACL,CAAC,EACE,CAAC,CAEbyB,QAAQ,GAAK,SAAS,cACrBjB,IAAA,CAACxB,SAAS,EACRuI,SAAS,MACTkC,KAAK,CAAC,YAAY,CAClBhC,KAAK,CAAE1F,WAAW,CAACI,gBAAiB,CACpCuF,QAAQ,CAAGC,CAAC,EAAK3F,cAAc,CAAA0H,aAAA,CAAAA,aAAA,IAAM3H,WAAW,MAAEI,gBAAgB,CAAEwF,CAAC,CAACC,MAAM,CAACH,KAAK,EAAE,CAAE,CACtFD,WAAW,CAAC,yDAAyD,CACtE,CAAC,cAEF9G,KAAA,CAAAE,SAAA,EAAAyF,QAAA,eACE7F,IAAA,CAACxB,SAAS,EACRuI,SAAS,MACTkC,KAAK,CAAC,YAAY,CAClBhC,KAAK,CAAE1F,WAAW,CAACE,IAAK,CACxByF,QAAQ,CAAGC,CAAC,EAAK3F,cAAc,CAAA0H,aAAA,CAAAA,aAAA,IAAM3H,WAAW,MAAEE,IAAI,CAAE0F,CAAC,CAACC,MAAM,CAACH,KAAK,EAAE,CAAE,CAC1Ef,EAAE,CAAE,CAAE8C,EAAE,CAAE,CAAE,CAAE,CACf,CAAC,cACFhJ,IAAA,CAACxB,SAAS,EACRuI,SAAS,MACTkC,KAAK,CAAC,wBAAwB,CAC9BE,SAAS,MACTC,IAAI,CAAE,CAAE,CACRnC,KAAK,CAAE1F,WAAW,CAACG,WAAY,CAC/BwF,QAAQ,CAAGC,CAAC,EAAK3F,cAAc,CAAA0H,aAAA,CAAAA,aAAA,IAAM3H,WAAW,MAAEG,WAAW,CAAEyF,CAAC,CAACC,MAAM,CAACH,KAAK,EAAE,CAAE,CAClF,CAAC,EACF,CACH,EACE,CAAC,CACO,CAAC,cAChB/G,KAAA,CAACpB,aAAa,EAAA+G,QAAA,eACZ7F,IAAA,CAACjB,MAAM,EAAC8I,OAAO,CAAEA,CAAA,GAAM7G,cAAc,CAAC,KAAK,CAAE,CAAA6E,QAAA,CAAC,QAAM,CAAQ,CAAC,cAC7D7F,IAAA,CAACjB,MAAM,EACL8I,OAAO,CAAE5G,QAAQ,GAAK,SAAS,CAAGkC,uBAAuB,CAAGa,qBAAsB,CAClFoC,OAAO,CAAC,WAAW,CACnBiD,QAAQ,CACN1I,OAAO,EACNM,QAAQ,GAAK,SAAS,EAAI,CAACM,WAAW,CAACI,gBAAgB,CAACyB,IAAI,CAAC,CAAE,EAC/DnC,QAAQ,GAAK,OAAO,EAAI,CAACM,WAAW,CAACE,IAAI,CAAC2B,IAAI,CAAC,CACjD,CAAAyC,QAAA,CAEAlF,OAAO,cAAGX,IAAA,CAACZ,gBAAgB,EAACqH,IAAI,CAAE,EAAG,CAAE,CAAC,CAAG,YAAY,CAClD,CAAC,EACI,CAAC,EACV,CAAC,EACA,CAAC,CAEhB,CAAC,CAED,cAAe,CAAApG,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}