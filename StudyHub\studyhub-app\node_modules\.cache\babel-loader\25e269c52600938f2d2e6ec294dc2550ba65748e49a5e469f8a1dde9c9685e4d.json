{"ast": null, "code": "// Source from https://github.com/alitaheri/normalize-scroll-left\nlet cachedType;\n\n/**\n * Based on the jquery plugin https://github.com/othree/jquery.rtl-scroll-type\n *\n * Types of scrollLeft, assuming scrollWidth=100 and direction is rtl.\n *\n * Type             | <- Most Left | Most Right -> | Initial\n * ---------------- | ------------ | ------------- | -------\n * default          | 0            | 100           | 100\n * negative (spec*) | -100         | 0             | 0\n * reverse          | 100          | 0             | 0\n *\n * Edge 85: default\n * Safari 14: negative\n * Chrome 85: negative\n * Firefox 81: negative\n * IE11: reverse\n *\n * spec* https://drafts.csswg.org/cssom-view/#dom-window-scroll\n */\nexport function detectScrollType() {\n  if (cachedType) {\n    return cachedType;\n  }\n  const dummy = document.createElement('div');\n  const container = document.createElement('div');\n  container.style.width = '10px';\n  container.style.height = '1px';\n  dummy.appendChild(container);\n  dummy.dir = 'rtl';\n  dummy.style.fontSize = '14px';\n  dummy.style.width = '4px';\n  dummy.style.height = '1px';\n  dummy.style.position = 'absolute';\n  dummy.style.top = '-1000px';\n  dummy.style.overflow = 'scroll';\n  document.body.appendChild(dummy);\n  cachedType = 'reverse';\n  if (dummy.scrollLeft > 0) {\n    cachedType = 'default';\n  } else {\n    dummy.scrollLeft = 1;\n    if (dummy.scrollLeft === 0) {\n      cachedType = 'negative';\n    }\n  }\n  document.body.removeChild(dummy);\n  return cachedType;\n}\n\n// Based on https://stackoverflow.com/a/24394376\nexport function getNormalizedScrollLeft(element, direction) {\n  const scrollLeft = element.scrollLeft;\n\n  // Perform the calculations only when direction is rtl to avoid messing up the ltr behavior\n  if (direction !== 'rtl') {\n    return scrollLeft;\n  }\n  const type = detectScrollType();\n  switch (type) {\n    case 'negative':\n      return element.scrollWidth - element.clientWidth + scrollLeft;\n    case 'reverse':\n      return element.scrollWidth - element.clientWidth - scrollLeft;\n    default:\n      return scrollLeft;\n  }\n}", "map": {"version": 3, "names": ["cachedType", "detectScrollType", "dummy", "document", "createElement", "container", "style", "width", "height", "append<PERSON><PERSON><PERSON>", "dir", "fontSize", "position", "top", "overflow", "body", "scrollLeft", "<PERSON><PERSON><PERSON><PERSON>", "getNormalizedScrollLeft", "element", "direction", "type", "scrollWidth", "clientWidth"], "sources": ["C:/Users/<USER>/OneDrive/ドキュメント/Projects/StudyHub/studyhub-app/node_modules/@mui/utils/esm/scrollLeft/scrollLeft.js"], "sourcesContent": ["// Source from https://github.com/alitaheri/normalize-scroll-left\nlet cachedType;\n\n/**\n * Based on the jquery plugin https://github.com/othree/jquery.rtl-scroll-type\n *\n * Types of scrollLeft, assuming scrollWidth=100 and direction is rtl.\n *\n * Type             | <- Most Left | Most Right -> | Initial\n * ---------------- | ------------ | ------------- | -------\n * default          | 0            | 100           | 100\n * negative (spec*) | -100         | 0             | 0\n * reverse          | 100          | 0             | 0\n *\n * Edge 85: default\n * Safari 14: negative\n * Chrome 85: negative\n * Firefox 81: negative\n * IE11: reverse\n *\n * spec* https://drafts.csswg.org/cssom-view/#dom-window-scroll\n */\nexport function detectScrollType() {\n  if (cachedType) {\n    return cachedType;\n  }\n  const dummy = document.createElement('div');\n  const container = document.createElement('div');\n  container.style.width = '10px';\n  container.style.height = '1px';\n  dummy.appendChild(container);\n  dummy.dir = 'rtl';\n  dummy.style.fontSize = '14px';\n  dummy.style.width = '4px';\n  dummy.style.height = '1px';\n  dummy.style.position = 'absolute';\n  dummy.style.top = '-1000px';\n  dummy.style.overflow = 'scroll';\n  document.body.appendChild(dummy);\n  cachedType = 'reverse';\n  if (dummy.scrollLeft > 0) {\n    cachedType = 'default';\n  } else {\n    dummy.scrollLeft = 1;\n    if (dummy.scrollLeft === 0) {\n      cachedType = 'negative';\n    }\n  }\n  document.body.removeChild(dummy);\n  return cachedType;\n}\n\n// Based on https://stackoverflow.com/a/24394376\nexport function getNormalizedScrollLeft(element, direction) {\n  const scrollLeft = element.scrollLeft;\n\n  // Perform the calculations only when direction is rtl to avoid messing up the ltr behavior\n  if (direction !== 'rtl') {\n    return scrollLeft;\n  }\n  const type = detectScrollType();\n  switch (type) {\n    case 'negative':\n      return element.scrollWidth - element.clientWidth + scrollLeft;\n    case 'reverse':\n      return element.scrollWidth - element.clientWidth - scrollLeft;\n    default:\n      return scrollLeft;\n  }\n}"], "mappings": "AAAA;AACA,IAAIA,UAAU;;AAEd;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,gBAAgBA,CAAA,EAAG;EACjC,IAAID,UAAU,EAAE;IACd,OAAOA,UAAU;EACnB;EACA,MAAME,KAAK,GAAGC,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;EAC3C,MAAMC,SAAS,GAAGF,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;EAC/CC,SAAS,CAACC,KAAK,CAACC,KAAK,GAAG,MAAM;EAC9BF,SAAS,CAACC,KAAK,CAACE,MAAM,GAAG,KAAK;EAC9BN,KAAK,CAACO,WAAW,CAACJ,SAAS,CAAC;EAC5BH,KAAK,CAACQ,GAAG,GAAG,KAAK;EACjBR,KAAK,CAACI,KAAK,CAACK,QAAQ,GAAG,MAAM;EAC7BT,KAAK,CAACI,KAAK,CAACC,KAAK,GAAG,KAAK;EACzBL,KAAK,CAACI,KAAK,CAACE,MAAM,GAAG,KAAK;EAC1BN,KAAK,CAACI,KAAK,CAACM,QAAQ,GAAG,UAAU;EACjCV,KAAK,CAACI,KAAK,CAACO,GAAG,GAAG,SAAS;EAC3BX,KAAK,CAACI,KAAK,CAACQ,QAAQ,GAAG,QAAQ;EAC/BX,QAAQ,CAACY,IAAI,CAACN,WAAW,CAACP,KAAK,CAAC;EAChCF,UAAU,GAAG,SAAS;EACtB,IAAIE,KAAK,CAACc,UAAU,GAAG,CAAC,EAAE;IACxBhB,UAAU,GAAG,SAAS;EACxB,CAAC,MAAM;IACLE,KAAK,CAACc,UAAU,GAAG,CAAC;IACpB,IAAId,KAAK,CAACc,UAAU,KAAK,CAAC,EAAE;MAC1BhB,UAAU,GAAG,UAAU;IACzB;EACF;EACAG,QAAQ,CAACY,IAAI,CAACE,WAAW,CAACf,KAAK,CAAC;EAChC,OAAOF,UAAU;AACnB;;AAEA;AACA,OAAO,SAASkB,uBAAuBA,CAACC,OAAO,EAAEC,SAAS,EAAE;EAC1D,MAAMJ,UAAU,GAAGG,OAAO,CAACH,UAAU;;EAErC;EACA,IAAII,SAAS,KAAK,KAAK,EAAE;IACvB,OAAOJ,UAAU;EACnB;EACA,MAAMK,IAAI,GAAGpB,gBAAgB,CAAC,CAAC;EAC/B,QAAQoB,IAAI;IACV,KAAK,UAAU;MACb,OAAOF,OAAO,CAACG,WAAW,GAAGH,OAAO,CAACI,WAAW,GAAGP,UAAU;IAC/D,KAAK,SAAS;MACZ,OAAOG,OAAO,CAACG,WAAW,GAAGH,OAAO,CAACI,WAAW,GAAGP,UAAU;IAC/D;MACE,OAAOA,UAAU;EACrB;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}