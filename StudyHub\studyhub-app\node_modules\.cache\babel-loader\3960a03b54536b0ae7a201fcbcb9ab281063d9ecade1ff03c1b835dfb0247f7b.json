{"ast": null, "code": "import getFreshSideObject from \"./getFreshSideObject.js\";\nexport default function mergePaddingObject(paddingObject) {\n  return Object.assign({}, getFreshSideObject(), paddingObject);\n}", "map": {"version": 3, "names": ["getFreshSideObject", "mergePaddingObject", "paddingObject", "Object", "assign"], "sources": ["C:/Users/<USER>/OneDrive/ドキュメント/Projects/StudyHub/studyhub-app/node_modules/@popperjs/core/lib/utils/mergePaddingObject.js"], "sourcesContent": ["import getFreshSideObject from \"./getFreshSideObject.js\";\nexport default function mergePaddingObject(paddingObject) {\n  return Object.assign({}, getFreshSideObject(), paddingObject);\n}"], "mappings": "AAAA,OAAOA,kBAAkB,MAAM,yBAAyB;AACxD,eAAe,SAASC,kBAAkBA,CAACC,aAAa,EAAE;EACxD,OAAOC,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEJ,kBAAkB,CAAC,CAAC,EAAEE,aAAa,CAAC;AAC/D", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}