{"ast": null, "code": "'use client';\n\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport { CacheProvider } from '@emotion/react';\nimport createCache from '@emotion/cache';\nimport { StyleSheet } from '@emotion/sheet';\n\n// To fix [Jest performance](https://github.com/mui/material-ui/issues/45638).\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst cacheMap = new Map();\n\n// Need to add a private variable to test the generated CSS from Emotion, this is the simplest way to do it.\n// We can't test the CSS from `style` tag easily because the `speedy: true` (produce empty text content) is enabled by Emotion.\n// Even if we disable it, JSDOM needs extra configuration to be able to parse `@layer` CSS.\nexport const TEST_INTERNALS_DO_NOT_USE = {\n  /**\n   * to intercept the generated CSS before inserting to the style tag, so that we can check the generated CSS.\n   *\n   * let rule;\n   * TEST_INTERNALS_DO_NOT_USE.insert = (...args) => {\n   *    rule = args[0];\n   * };\n   *\n   * expect(rule).to.equal(...);\n   */\n  insert: undefined\n};\n\n// We might be able to remove this when this issue is fixed:\n// https://github.com/emotion-js/emotion/issues/2790\nconst createEmotionCache = (options, CustomSheet) => {\n  const cache = createCache(options);\n\n  // Do the same as https://github.com/emotion-js/emotion/blob/main/packages/cache/src/index.js#L238-L245\n  cache.sheet = new CustomSheet({\n    key: cache.key,\n    nonce: cache.sheet.nonce,\n    container: cache.sheet.container,\n    speedy: cache.sheet.isSpeedy,\n    prepend: cache.sheet.prepend,\n    insertionPoint: cache.sheet.insertionPoint\n  });\n  return cache;\n};\nlet insertionPoint;\nif (typeof document === 'object') {\n  // Use `insertionPoint` over `prepend`(deprecated) because it can be controlled for GlobalStyles injection order\n  // For more information, see https://github.com/mui/material-ui/issues/44597\n  insertionPoint = document.querySelector('[name=\"emotion-insertion-point\"]');\n  if (!insertionPoint) {\n    insertionPoint = document.createElement('meta');\n    insertionPoint.setAttribute('name', 'emotion-insertion-point');\n    insertionPoint.setAttribute('content', '');\n    const head = document.querySelector('head');\n    if (head) {\n      head.prepend(insertionPoint);\n    }\n  }\n}\nfunction getCache(injectFirst, enableCssLayer) {\n  if (injectFirst || enableCssLayer) {\n    /**\n     * This is for client-side apps only.\n     * A custom sheet is required to make the GlobalStyles API injected above the insertion point.\n     * This is because the [sheet](https://github.com/emotion-js/emotion/blob/main/packages/react/src/global.js#L94-L99) does not consume the options.\n     */\n    class MyStyleSheet extends StyleSheet {\n      insert(rule, options) {\n        if (TEST_INTERNALS_DO_NOT_USE.insert) {\n          return TEST_INTERNALS_DO_NOT_USE.insert(rule, options);\n        }\n        if (this.key && this.key.endsWith('global')) {\n          this.before = insertionPoint;\n        }\n        return super.insert(rule, options);\n      }\n    }\n    const emotionCache = createEmotionCache({\n      key: 'css',\n      insertionPoint: injectFirst ? insertionPoint : undefined\n    }, MyStyleSheet);\n    if (enableCssLayer) {\n      const prevInsert = emotionCache.insert;\n      emotionCache.insert = (...args) => {\n        if (!args[1].styles.match(/^@layer\\s+[^{]*$/)) {\n          // avoid nested @layer\n          args[1].styles = `@layer mui {${args[1].styles}}`;\n        }\n        return prevInsert(...args);\n      };\n    }\n    return emotionCache;\n  }\n  return undefined;\n}\nexport default function StyledEngineProvider(props) {\n  const {\n    injectFirst,\n    enableCssLayer,\n    children\n  } = props;\n  const cache = React.useMemo(() => {\n    const cacheKey = `${injectFirst}-${enableCssLayer}`;\n    if (typeof document === 'object' && cacheMap.has(cacheKey)) {\n      return cacheMap.get(cacheKey);\n    }\n    const fresh = getCache(injectFirst, enableCssLayer);\n    cacheMap.set(cacheKey, fresh);\n    return fresh;\n  }, [injectFirst, enableCssLayer]);\n  return cache ? /*#__PURE__*/_jsx(CacheProvider, {\n    value: cache,\n    children: children\n  }) : children;\n}\nprocess.env.NODE_ENV !== \"production\" ? StyledEngineProvider.propTypes = {\n  /**\n   * Your component tree.\n   */\n  children: PropTypes.node,\n  /**\n   * If `true`, the styles are wrapped in `@layer mui`.\n   * Learn more about [Cascade layers](https://developer.mozilla.org/en-US/docs/Learn_web_development/Core/Styling_basics/Cascade_layers).\n   */\n  enableCssLayer: PropTypes.bool,\n  /**\n   * By default, the styles are injected last in the <head> element of the page.\n   * As a result, they gain more specificity than any other style sheet.\n   * If you want to override MUI's styles, set this prop.\n   */\n  injectFirst: PropTypes.bool\n} : void 0;", "map": {"version": 3, "names": ["React", "PropTypes", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "createCache", "StyleSheet", "jsx", "_jsx", "cacheMap", "Map", "TEST_INTERNALS_DO_NOT_USE", "insert", "undefined", "createEmotionCache", "options", "CustomSheet", "cache", "sheet", "key", "nonce", "container", "speedy", "isSpeedy", "prepend", "insertionPoint", "document", "querySelector", "createElement", "setAttribute", "head", "getCache", "injectFirst", "enableCssLayer", "MyStyleSheet", "rule", "endsWith", "before", "emotionCache", "prevInsert", "args", "styles", "match", "StyledEngineProvider", "props", "children", "useMemo", "cache<PERSON>ey", "has", "get", "fresh", "set", "value", "process", "env", "NODE_ENV", "propTypes", "node", "bool"], "sources": ["C:/Users/<USER>/OneDrive/ドキュメント/Projects/StudyHub/studyhub-app/node_modules/@mui/styled-engine/esm/StyledEngineProvider/StyledEngineProvider.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport { CacheProvider } from '@emotion/react';\nimport createCache from '@emotion/cache';\nimport { StyleSheet } from '@emotion/sheet';\n\n// To fix [Jest performance](https://github.com/mui/material-ui/issues/45638).\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst cacheMap = new Map();\n\n// Need to add a private variable to test the generated CSS from Emotion, this is the simplest way to do it.\n// We can't test the CSS from `style` tag easily because the `speedy: true` (produce empty text content) is enabled by Emotion.\n// Even if we disable it, JSDOM needs extra configuration to be able to parse `@layer` CSS.\nexport const TEST_INTERNALS_DO_NOT_USE = {\n  /**\n   * to intercept the generated CSS before inserting to the style tag, so that we can check the generated CSS.\n   *\n   * let rule;\n   * TEST_INTERNALS_DO_NOT_USE.insert = (...args) => {\n   *    rule = args[0];\n   * };\n   *\n   * expect(rule).to.equal(...);\n   */\n  insert: undefined\n};\n\n// We might be able to remove this when this issue is fixed:\n// https://github.com/emotion-js/emotion/issues/2790\nconst createEmotionCache = (options, CustomSheet) => {\n  const cache = createCache(options);\n\n  // Do the same as https://github.com/emotion-js/emotion/blob/main/packages/cache/src/index.js#L238-L245\n  cache.sheet = new CustomSheet({\n    key: cache.key,\n    nonce: cache.sheet.nonce,\n    container: cache.sheet.container,\n    speedy: cache.sheet.isSpeedy,\n    prepend: cache.sheet.prepend,\n    insertionPoint: cache.sheet.insertionPoint\n  });\n  return cache;\n};\nlet insertionPoint;\nif (typeof document === 'object') {\n  // Use `insertionPoint` over `prepend`(deprecated) because it can be controlled for GlobalStyles injection order\n  // For more information, see https://github.com/mui/material-ui/issues/44597\n  insertionPoint = document.querySelector('[name=\"emotion-insertion-point\"]');\n  if (!insertionPoint) {\n    insertionPoint = document.createElement('meta');\n    insertionPoint.setAttribute('name', 'emotion-insertion-point');\n    insertionPoint.setAttribute('content', '');\n    const head = document.querySelector('head');\n    if (head) {\n      head.prepend(insertionPoint);\n    }\n  }\n}\nfunction getCache(injectFirst, enableCssLayer) {\n  if (injectFirst || enableCssLayer) {\n    /**\n     * This is for client-side apps only.\n     * A custom sheet is required to make the GlobalStyles API injected above the insertion point.\n     * This is because the [sheet](https://github.com/emotion-js/emotion/blob/main/packages/react/src/global.js#L94-L99) does not consume the options.\n     */\n    class MyStyleSheet extends StyleSheet {\n      insert(rule, options) {\n        if (TEST_INTERNALS_DO_NOT_USE.insert) {\n          return TEST_INTERNALS_DO_NOT_USE.insert(rule, options);\n        }\n        if (this.key && this.key.endsWith('global')) {\n          this.before = insertionPoint;\n        }\n        return super.insert(rule, options);\n      }\n    }\n    const emotionCache = createEmotionCache({\n      key: 'css',\n      insertionPoint: injectFirst ? insertionPoint : undefined\n    }, MyStyleSheet);\n    if (enableCssLayer) {\n      const prevInsert = emotionCache.insert;\n      emotionCache.insert = (...args) => {\n        if (!args[1].styles.match(/^@layer\\s+[^{]*$/)) {\n          // avoid nested @layer\n          args[1].styles = `@layer mui {${args[1].styles}}`;\n        }\n        return prevInsert(...args);\n      };\n    }\n    return emotionCache;\n  }\n  return undefined;\n}\nexport default function StyledEngineProvider(props) {\n  const {\n    injectFirst,\n    enableCssLayer,\n    children\n  } = props;\n  const cache = React.useMemo(() => {\n    const cacheKey = `${injectFirst}-${enableCssLayer}`;\n    if (typeof document === 'object' && cacheMap.has(cacheKey)) {\n      return cacheMap.get(cacheKey);\n    }\n    const fresh = getCache(injectFirst, enableCssLayer);\n    cacheMap.set(cacheKey, fresh);\n    return fresh;\n  }, [injectFirst, enableCssLayer]);\n  return cache ? /*#__PURE__*/_jsx(CacheProvider, {\n    value: cache,\n    children: children\n  }) : children;\n}\nprocess.env.NODE_ENV !== \"production\" ? StyledEngineProvider.propTypes = {\n  /**\n   * Your component tree.\n   */\n  children: PropTypes.node,\n  /**\n   * If `true`, the styles are wrapped in `@layer mui`.\n   * Learn more about [Cascade layers](https://developer.mozilla.org/en-US/docs/Learn_web_development/Core/Styling_basics/Cascade_layers).\n   */\n  enableCssLayer: PropTypes.bool,\n  /**\n   * By default, the styles are injected last in the <head> element of the page.\n   * As a result, they gain more specificity than any other style sheet.\n   * If you want to override MUI's styles, set this prop.\n   */\n  injectFirst: PropTypes.bool\n} : void 0;"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,SAASC,aAAa,QAAQ,gBAAgB;AAC9C,OAAOC,WAAW,MAAM,gBAAgB;AACxC,SAASC,UAAU,QAAQ,gBAAgB;;AAE3C;AACA,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,QAAQ,GAAG,IAAIC,GAAG,CAAC,CAAC;;AAE1B;AACA;AACA;AACA,OAAO,MAAMC,yBAAyB,GAAG;EACvC;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEC,MAAM,EAAEC;AACV,CAAC;;AAED;AACA;AACA,MAAMC,kBAAkB,GAAGA,CAACC,OAAO,EAAEC,WAAW,KAAK;EACnD,MAAMC,KAAK,GAAGZ,WAAW,CAACU,OAAO,CAAC;;EAElC;EACAE,KAAK,CAACC,KAAK,GAAG,IAAIF,WAAW,CAAC;IAC5BG,GAAG,EAAEF,KAAK,CAACE,GAAG;IACdC,KAAK,EAAEH,KAAK,CAACC,KAAK,CAACE,KAAK;IACxBC,SAAS,EAAEJ,KAAK,CAACC,KAAK,CAACG,SAAS;IAChCC,MAAM,EAAEL,KAAK,CAACC,KAAK,CAACK,QAAQ;IAC5BC,OAAO,EAAEP,KAAK,CAACC,KAAK,CAACM,OAAO;IAC5BC,cAAc,EAAER,KAAK,CAACC,KAAK,CAACO;EAC9B,CAAC,CAAC;EACF,OAAOR,KAAK;AACd,CAAC;AACD,IAAIQ,cAAc;AAClB,IAAI,OAAOC,QAAQ,KAAK,QAAQ,EAAE;EAChC;EACA;EACAD,cAAc,GAAGC,QAAQ,CAACC,aAAa,CAAC,kCAAkC,CAAC;EAC3E,IAAI,CAACF,cAAc,EAAE;IACnBA,cAAc,GAAGC,QAAQ,CAACE,aAAa,CAAC,MAAM,CAAC;IAC/CH,cAAc,CAACI,YAAY,CAAC,MAAM,EAAE,yBAAyB,CAAC;IAC9DJ,cAAc,CAACI,YAAY,CAAC,SAAS,EAAE,EAAE,CAAC;IAC1C,MAAMC,IAAI,GAAGJ,QAAQ,CAACC,aAAa,CAAC,MAAM,CAAC;IAC3C,IAAIG,IAAI,EAAE;MACRA,IAAI,CAACN,OAAO,CAACC,cAAc,CAAC;IAC9B;EACF;AACF;AACA,SAASM,QAAQA,CAACC,WAAW,EAAEC,cAAc,EAAE;EAC7C,IAAID,WAAW,IAAIC,cAAc,EAAE;IACjC;AACJ;AACA;AACA;AACA;IACI,MAAMC,YAAY,SAAS5B,UAAU,CAAC;MACpCM,MAAMA,CAACuB,IAAI,EAAEpB,OAAO,EAAE;QACpB,IAAIJ,yBAAyB,CAACC,MAAM,EAAE;UACpC,OAAOD,yBAAyB,CAACC,MAAM,CAACuB,IAAI,EAAEpB,OAAO,CAAC;QACxD;QACA,IAAI,IAAI,CAACI,GAAG,IAAI,IAAI,CAACA,GAAG,CAACiB,QAAQ,CAAC,QAAQ,CAAC,EAAE;UAC3C,IAAI,CAACC,MAAM,GAAGZ,cAAc;QAC9B;QACA,OAAO,KAAK,CAACb,MAAM,CAACuB,IAAI,EAAEpB,OAAO,CAAC;MACpC;IACF;IACA,MAAMuB,YAAY,GAAGxB,kBAAkB,CAAC;MACtCK,GAAG,EAAE,KAAK;MACVM,cAAc,EAAEO,WAAW,GAAGP,cAAc,GAAGZ;IACjD,CAAC,EAAEqB,YAAY,CAAC;IAChB,IAAID,cAAc,EAAE;MAClB,MAAMM,UAAU,GAAGD,YAAY,CAAC1B,MAAM;MACtC0B,YAAY,CAAC1B,MAAM,GAAG,CAAC,GAAG4B,IAAI,KAAK;QACjC,IAAI,CAACA,IAAI,CAAC,CAAC,CAAC,CAACC,MAAM,CAACC,KAAK,CAAC,kBAAkB,CAAC,EAAE;UAC7C;UACAF,IAAI,CAAC,CAAC,CAAC,CAACC,MAAM,GAAG,eAAeD,IAAI,CAAC,CAAC,CAAC,CAACC,MAAM,GAAG;QACnD;QACA,OAAOF,UAAU,CAAC,GAAGC,IAAI,CAAC;MAC5B,CAAC;IACH;IACA,OAAOF,YAAY;EACrB;EACA,OAAOzB,SAAS;AAClB;AACA,eAAe,SAAS8B,oBAAoBA,CAACC,KAAK,EAAE;EAClD,MAAM;IACJZ,WAAW;IACXC,cAAc;IACdY;EACF,CAAC,GAAGD,KAAK;EACT,MAAM3B,KAAK,GAAGf,KAAK,CAAC4C,OAAO,CAAC,MAAM;IAChC,MAAMC,QAAQ,GAAG,GAAGf,WAAW,IAAIC,cAAc,EAAE;IACnD,IAAI,OAAOP,QAAQ,KAAK,QAAQ,IAAIjB,QAAQ,CAACuC,GAAG,CAACD,QAAQ,CAAC,EAAE;MAC1D,OAAOtC,QAAQ,CAACwC,GAAG,CAACF,QAAQ,CAAC;IAC/B;IACA,MAAMG,KAAK,GAAGnB,QAAQ,CAACC,WAAW,EAAEC,cAAc,CAAC;IACnDxB,QAAQ,CAAC0C,GAAG,CAACJ,QAAQ,EAAEG,KAAK,CAAC;IAC7B,OAAOA,KAAK;EACd,CAAC,EAAE,CAAClB,WAAW,EAAEC,cAAc,CAAC,CAAC;EACjC,OAAOhB,KAAK,GAAG,aAAaT,IAAI,CAACJ,aAAa,EAAE;IAC9CgD,KAAK,EAAEnC,KAAK;IACZ4B,QAAQ,EAAEA;EACZ,CAAC,CAAC,GAAGA,QAAQ;AACf;AACAQ,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGZ,oBAAoB,CAACa,SAAS,GAAG;EACvE;AACF;AACA;EACEX,QAAQ,EAAE1C,SAAS,CAACsD,IAAI;EACxB;AACF;AACA;AACA;EACExB,cAAc,EAAE9B,SAAS,CAACuD,IAAI;EAC9B;AACF;AACA;AACA;AACA;EACE1B,WAAW,EAAE7B,SAAS,CAACuD;AACzB,CAAC,GAAG,KAAK,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}