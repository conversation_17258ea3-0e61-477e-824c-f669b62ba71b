{"ast": null, "code": "import _objectSpread from \"C:/Users/<USER>/OneDrive/\\u30C9\\u30AD\\u30E5\\u30E1\\u30F3\\u30C8/Projects/StudyHub/studyhub-app/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nimport clsx from 'clsx';\n\n/**\n * Add keys, values of `defaultProps` that does not exist in `props`\n * @param defaultProps\n * @param props\n * @param mergeClassNameAndStyle If `true`, merges `className` and `style` props instead of overriding them.\n *   When `false` (default), props override defaultProps. When `true`, `className` values are concatenated\n *   and `style` objects are merged with props taking precedence.\n * @returns resolved props\n */\nexport default function resolveProps(defaultProps, props) {\n  let mergeClassNameAndStyle = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : false;\n  const output = _objectSpread({}, props);\n  for (const key in defaultProps) {\n    if (Object.prototype.hasOwnProperty.call(defaultProps, key)) {\n      const propName = key;\n      if (propName === 'components' || propName === 'slots') {\n        output[propName] = _objectSpread(_objectSpread({}, defaultProps[propName]), output[propName]);\n      } else if (propName === 'componentsProps' || propName === 'slotProps') {\n        const defaultSlotProps = defaultProps[propName];\n        const slotProps = props[propName];\n        if (!slotProps) {\n          output[propName] = defaultSlotProps || {};\n        } else if (!defaultSlotProps) {\n          output[propName] = slotProps;\n        } else {\n          output[propName] = _objectSpread({}, slotProps);\n          for (const slotKey in defaultSlotProps) {\n            if (Object.prototype.hasOwnProperty.call(defaultSlotProps, slotKey)) {\n              const slotPropName = slotKey;\n              output[propName][slotPropName] = resolveProps(defaultSlotProps[slotPropName], slotProps[slotPropName], mergeClassNameAndStyle);\n            }\n          }\n        }\n      } else if (propName === 'className' && mergeClassNameAndStyle && props.className) {\n        output.className = clsx(defaultProps === null || defaultProps === void 0 ? void 0 : defaultProps.className, props === null || props === void 0 ? void 0 : props.className);\n      } else if (propName === 'style' && mergeClassNameAndStyle && props.style) {\n        output.style = _objectSpread(_objectSpread({}, defaultProps === null || defaultProps === void 0 ? void 0 : defaultProps.style), props === null || props === void 0 ? void 0 : props.style);\n      } else if (output[propName] === undefined) {\n        output[propName] = defaultProps[propName];\n      }\n    }\n  }\n  return output;\n}", "map": {"version": 3, "names": ["clsx", "resolveProps", "defaultProps", "props", "mergeClassNameAndStyle", "arguments", "length", "undefined", "output", "_objectSpread", "key", "Object", "prototype", "hasOwnProperty", "call", "propName", "defaultSlotProps", "slotProps", "<PERSON><PERSON><PERSON>", "slotPropName", "className", "style"], "sources": ["C:/Users/<USER>/OneDrive/ドキュメント/Projects/StudyHub/studyhub-app/node_modules/@mui/utils/esm/resolveProps/resolveProps.js"], "sourcesContent": ["import clsx from 'clsx';\n\n/**\n * Add keys, values of `defaultProps` that does not exist in `props`\n * @param defaultProps\n * @param props\n * @param mergeClassNameAndStyle If `true`, merges `className` and `style` props instead of overriding them.\n *   When `false` (default), props override defaultProps. When `true`, `className` values are concatenated\n *   and `style` objects are merged with props taking precedence.\n * @returns resolved props\n */\nexport default function resolveProps(defaultProps, props, mergeClassNameAndStyle = false) {\n  const output = {\n    ...props\n  };\n  for (const key in defaultProps) {\n    if (Object.prototype.hasOwnProperty.call(defaultProps, key)) {\n      const propName = key;\n      if (propName === 'components' || propName === 'slots') {\n        output[propName] = {\n          ...defaultProps[propName],\n          ...output[propName]\n        };\n      } else if (propName === 'componentsProps' || propName === 'slotProps') {\n        const defaultSlotProps = defaultProps[propName];\n        const slotProps = props[propName];\n        if (!slotProps) {\n          output[propName] = defaultSlotProps || {};\n        } else if (!defaultSlotProps) {\n          output[propName] = slotProps;\n        } else {\n          output[propName] = {\n            ...slotProps\n          };\n          for (const slotKey in defaultSlotProps) {\n            if (Object.prototype.hasOwnProperty.call(defaultSlotProps, slotKey)) {\n              const slotPropName = slotKey;\n              output[propName][slotPropName] = resolveProps(defaultSlotProps[slotPropName], slotProps[slotPropName], mergeClassNameAndStyle);\n            }\n          }\n        }\n      } else if (propName === 'className' && mergeClassNameAndStyle && props.className) {\n        output.className = clsx(defaultProps?.className, props?.className);\n      } else if (propName === 'style' && mergeClassNameAndStyle && props.style) {\n        output.style = {\n          ...defaultProps?.style,\n          ...props?.style\n        };\n      } else if (output[propName] === undefined) {\n        output[propName] = defaultProps[propName];\n      }\n    }\n  }\n  return output;\n}"], "mappings": ";AAAA,OAAOA,IAAI,MAAM,MAAM;;AAEvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASC,YAAYA,CAACC,YAAY,EAAEC,KAAK,EAAkC;EAAA,IAAhCC,sBAAsB,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,KAAK;EACtF,MAAMG,MAAM,GAAAC,aAAA,KACPN,KAAK,CACT;EACD,KAAK,MAAMO,GAAG,IAAIR,YAAY,EAAE;IAC9B,IAAIS,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACZ,YAAY,EAAEQ,GAAG,CAAC,EAAE;MAC3D,MAAMK,QAAQ,GAAGL,GAAG;MACpB,IAAIK,QAAQ,KAAK,YAAY,IAAIA,QAAQ,KAAK,OAAO,EAAE;QACrDP,MAAM,CAACO,QAAQ,CAAC,GAAAN,aAAA,CAAAA,aAAA,KACXP,YAAY,CAACa,QAAQ,CAAC,GACtBP,MAAM,CAACO,QAAQ,CAAC,CACpB;MACH,CAAC,MAAM,IAAIA,QAAQ,KAAK,iBAAiB,IAAIA,QAAQ,KAAK,WAAW,EAAE;QACrE,MAAMC,gBAAgB,GAAGd,YAAY,CAACa,QAAQ,CAAC;QAC/C,MAAME,SAAS,GAAGd,KAAK,CAACY,QAAQ,CAAC;QACjC,IAAI,CAACE,SAAS,EAAE;UACdT,MAAM,CAACO,QAAQ,CAAC,GAAGC,gBAAgB,IAAI,CAAC,CAAC;QAC3C,CAAC,MAAM,IAAI,CAACA,gBAAgB,EAAE;UAC5BR,MAAM,CAACO,QAAQ,CAAC,GAAGE,SAAS;QAC9B,CAAC,MAAM;UACLT,MAAM,CAACO,QAAQ,CAAC,GAAAN,aAAA,KACXQ,SAAS,CACb;UACD,KAAK,MAAMC,OAAO,IAAIF,gBAAgB,EAAE;YACtC,IAAIL,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACE,gBAAgB,EAAEE,OAAO,CAAC,EAAE;cACnE,MAAMC,YAAY,GAAGD,OAAO;cAC5BV,MAAM,CAACO,QAAQ,CAAC,CAACI,YAAY,CAAC,GAAGlB,YAAY,CAACe,gBAAgB,CAACG,YAAY,CAAC,EAAEF,SAAS,CAACE,YAAY,CAAC,EAAEf,sBAAsB,CAAC;YAChI;UACF;QACF;MACF,CAAC,MAAM,IAAIW,QAAQ,KAAK,WAAW,IAAIX,sBAAsB,IAAID,KAAK,CAACiB,SAAS,EAAE;QAChFZ,MAAM,CAACY,SAAS,GAAGpB,IAAI,CAACE,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEkB,SAAS,EAAEjB,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEiB,SAAS,CAAC;MACpE,CAAC,MAAM,IAAIL,QAAQ,KAAK,OAAO,IAAIX,sBAAsB,IAAID,KAAK,CAACkB,KAAK,EAAE;QACxEb,MAAM,CAACa,KAAK,GAAAZ,aAAA,CAAAA,aAAA,KACPP,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEmB,KAAK,GACnBlB,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEkB,KAAK,CAChB;MACH,CAAC,MAAM,IAAIb,MAAM,CAACO,QAAQ,CAAC,KAAKR,SAAS,EAAE;QACzCC,MAAM,CAACO,QAAQ,CAAC,GAAGb,YAAY,CAACa,QAAQ,CAAC;MAC3C;IACF;EACF;EACA,OAAOP,MAAM;AACf", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}