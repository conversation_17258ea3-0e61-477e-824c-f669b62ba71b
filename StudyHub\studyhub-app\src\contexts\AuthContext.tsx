import React, { createContext, useContext, useEffect, useState } from 'react';
import { 
  User,
  onAuthStateChanged,
  signInWithEmailAndPassword,
  createUserWithEmailAndPassword,
  signOut,
  updateProfile
} from 'firebase/auth';
import { 
  doc, 
  getDoc, 
  setDoc, 
  updateDoc,
  serverTimestamp 
} from 'firebase/firestore';
import { auth, db } from '../firebase/config';

export interface UserProfile {
  uid: string;
  email: string;
  firstName: string;
  lastName: string;
  displayName: string;
  bio?: string;
  university?: string;
  major?: string;
  year?: string;
  profilePicture?: string;
  createdAt: any;
  updatedAt: any;
  isOnline: boolean;
  lastSeen: any;
}

interface AuthContextType {
  currentUser: User | null;
  userProfile: UserProfile | null;
  loading: boolean;
  login: (email: string, password: string) => Promise<void>;
  register: (email: string, password: string, firstName: string, lastName: string) => Promise<void>;
  logout: () => Promise<void>;
  updateUserProfile: (updates: Partial<UserProfile>) => Promise<void>;
  refreshUserProfile: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [currentUser, setCurrentUser] = useState<User | null>(null);
  const [userProfile, setUserProfile] = useState<UserProfile | null>(null);
  const [loading, setLoading] = useState(true);

  const createUserProfile = async (user: User, additionalData: any = {}) => {
    const userRef = doc(db, 'users', user.uid);
    const userSnap = await getDoc(userRef);

    if (!userSnap.exists()) {
      const { firstName, lastName, ...otherData } = additionalData;
      const displayName = `${firstName} ${lastName}`;
      
      try {
        await setDoc(userRef, {
          uid: user.uid,
          email: user.email,
          firstName,
          lastName,
          displayName,
          bio: '',
          university: '',
          major: '',
          year: '',
          profilePicture: '',
          createdAt: serverTimestamp(),
          updatedAt: serverTimestamp(),
          isOnline: true,
          lastSeen: serverTimestamp(),
          ...otherData
        });

        // Update Firebase Auth profile
        await updateProfile(user, {
          displayName: displayName
        });
      } catch (error) {
        console.error('Error creating user profile:', error);
        throw error;
      }
    }
    
    return userRef;
  };

  const fetchUserProfile = async (uid: string): Promise<UserProfile | null> => {
    try {
      const userRef = doc(db, 'users', uid);
      const userSnap = await getDoc(userRef);
      
      if (userSnap.exists()) {
        return userSnap.data() as UserProfile;
      }
      return null;
    } catch (error) {
      console.error('Error fetching user profile:', error);
      return null;
    }
  };

  const updateUserOnlineStatus = async (uid: string, isOnline: boolean) => {
    try {
      const userRef = doc(db, 'users', uid);
      await updateDoc(userRef, {
        isOnline,
        lastSeen: serverTimestamp()
      });
    } catch (error) {
      console.error('Error updating online status:', error);
    }
  };

  const login = async (email: string, password: string) => {
    try {
      const result = await signInWithEmailAndPassword(auth, email, password);
      await updateUserOnlineStatus(result.user.uid, true);
    } catch (error) {
      console.error('Login error:', error);
      throw error;
    }
  };

  const register = async (email: string, password: string, firstName: string, lastName: string) => {
    try {
      const result = await createUserWithEmailAndPassword(auth, email, password);
      await createUserProfile(result.user, { firstName, lastName });
      await updateUserOnlineStatus(result.user.uid, true);
    } catch (error) {
      console.error('Registration error:', error);
      throw error;
    }
  };

  const logout = async () => {
    try {
      if (currentUser) {
        await updateUserOnlineStatus(currentUser.uid, false);
      }
      await signOut(auth);
    } catch (error) {
      console.error('Logout error:', error);
      throw error;
    }
  };

  const updateUserProfile = async (updates: Partial<UserProfile>) => {
    if (!currentUser) throw new Error('No user logged in');
    
    try {
      const userRef = doc(db, 'users', currentUser.uid);
      const updateData = {
        ...updates,
        updatedAt: serverTimestamp()
      };
      
      await updateDoc(userRef, updateData);
      
      // Update displayName in Firebase Auth if firstName or lastName changed
      if (updates.firstName || updates.lastName) {
        const currentProfile = userProfile;
        const firstName = updates.firstName || currentProfile?.firstName || '';
        const lastName = updates.lastName || currentProfile?.lastName || '';
        const displayName = `${firstName} ${lastName}`;
        
        await updateProfile(currentUser, { displayName });
        updateData.displayName = displayName;
      }
      
      // Update local state
      setUserProfile(prev => prev ? { ...prev, ...updateData } : null);
    } catch (error) {
      console.error('Error updating user profile:', error);
      throw error;
    }
  };

  const refreshUserProfile = async () => {
    if (!currentUser) return;
    
    const profile = await fetchUserProfile(currentUser.uid);
    setUserProfile(profile);
  };

  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, async (user) => {
      setCurrentUser(user);
      
      if (user) {
        // Fetch user profile
        const profile = await fetchUserProfile(user.uid);
        setUserProfile(profile);
        
        // Update online status
        await updateUserOnlineStatus(user.uid, true);
        
        // Set up beforeunload listener to update offline status
        const handleBeforeUnload = () => {
          updateUserOnlineStatus(user.uid, false);
        };
        
        window.addEventListener('beforeunload', handleBeforeUnload);
        
        return () => {
          window.removeEventListener('beforeunload', handleBeforeUnload);
        };
      } else {
        setUserProfile(null);
      }
      
      setLoading(false);
    });

    return unsubscribe;
  }, []);

  const value: AuthContextType = {
    currentUser,
    userProfile,
    loading,
    login,
    register,
    logout,
    updateUserProfile,
    refreshUserProfile
  };

  return (
    <AuthContext.Provider value={value}>
      {!loading && children}
    </AuthContext.Provider>
  );
};
