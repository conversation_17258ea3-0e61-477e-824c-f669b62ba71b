{"ast": null, "code": "'use client';\n\nimport _objectSpread from \"C:/Users/<USER>/OneDrive/\\u30C9\\u30AD\\u30E5\\u30E1\\u30F3\\u30C8/Projects/StudyHub/studyhub-app/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nimport _objectWithoutProperties from \"C:/Users/<USER>/OneDrive/\\u30C9\\u30AD\\u30E5\\u30E1\\u30F3\\u30C8/Projects/StudyHub/studyhub-app/node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js\";\nconst _excluded = [\"anchor\", \"classes\", \"className\", \"width\", \"style\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport { styled } from \"../zero-styled/index.js\";\nimport memoTheme from \"../utils/memoTheme.js\";\nimport rootShouldForwardProp from \"../styles/rootShouldForwardProp.js\";\nimport capitalize from \"../utils/capitalize.js\";\nimport { isHorizontal } from \"../Drawer/Drawer.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst SwipeAreaRoot = styled('div', {\n  name: 'MuiSwipeArea',\n  shouldForwardProp: rootShouldForwardProp\n})(memoTheme(_ref => {\n  let {\n    theme\n  } = _ref;\n  return {\n    position: 'fixed',\n    top: 0,\n    left: 0,\n    bottom: 0,\n    zIndex: theme.zIndex.drawer - 1,\n    variants: [{\n      props: {\n        anchor: 'left'\n      },\n      style: {\n        right: 'auto'\n      }\n    }, {\n      props: {\n        anchor: 'right'\n      },\n      style: {\n        left: 'auto',\n        right: 0\n      }\n    }, {\n      props: {\n        anchor: 'top'\n      },\n      style: {\n        bottom: 'auto',\n        right: 0\n      }\n    }, {\n      props: {\n        anchor: 'bottom'\n      },\n      style: {\n        top: 'auto',\n        bottom: 0,\n        right: 0\n      }\n    }]\n  };\n}));\n\n/**\n * @ignore - internal component.\n */\nconst SwipeArea = /*#__PURE__*/React.forwardRef(function SwipeArea(props, ref) {\n  const {\n      anchor,\n      classes = {},\n      className,\n      width,\n      style\n    } = props,\n    other = _objectWithoutProperties(props, _excluded);\n  const ownerState = props;\n  return /*#__PURE__*/_jsx(SwipeAreaRoot, _objectSpread({\n    className: clsx('PrivateSwipeArea-root', classes.root, classes[\"anchor\".concat(capitalize(anchor))], className),\n    ref: ref,\n    style: _objectSpread({\n      [isHorizontal(anchor) ? 'width' : 'height']: width\n    }, style),\n    ownerState: ownerState\n  }, other));\n});\nprocess.env.NODE_ENV !== \"production\" ? SwipeArea.propTypes = {\n  /**\n   * Side on which to attach the discovery area.\n   */\n  anchor: PropTypes.oneOf(['left', 'top', 'right', 'bottom']).isRequired,\n  /**\n   * @ignore\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * @ignore\n   */\n  style: PropTypes.object,\n  /**\n   * The width of the left most (or right most) area in `px` where the\n   * drawer can be swiped open from.\n   */\n  width: PropTypes.number.isRequired\n} : void 0;\nexport default SwipeArea;", "map": {"version": 3, "names": ["_objectSpread", "_objectWithoutProperties", "_excluded", "React", "PropTypes", "clsx", "styled", "memoTheme", "rootShouldForwardProp", "capitalize", "isHorizontal", "jsx", "_jsx", "SwipeAreaRoot", "name", "shouldForwardProp", "_ref", "theme", "position", "top", "left", "bottom", "zIndex", "drawer", "variants", "props", "anchor", "style", "right", "SwipeArea", "forwardRef", "ref", "classes", "className", "width", "other", "ownerState", "root", "concat", "process", "env", "NODE_ENV", "propTypes", "oneOf", "isRequired", "object", "string", "number"], "sources": ["C:/Users/<USER>/OneDrive/ドキュメント/Projects/StudyHub/studyhub-app/node_modules/@mui/material/esm/SwipeableDrawer/SwipeArea.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport { styled } from \"../zero-styled/index.js\";\nimport memoTheme from \"../utils/memoTheme.js\";\nimport rootShouldForwardProp from \"../styles/rootShouldForwardProp.js\";\nimport capitalize from \"../utils/capitalize.js\";\nimport { isHorizontal } from \"../Drawer/Drawer.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst SwipeAreaRoot = styled('div', {\n  name: 'MuiSwipeArea',\n  shouldForwardProp: rootShouldForwardProp\n})(memoTheme(({\n  theme\n}) => ({\n  position: 'fixed',\n  top: 0,\n  left: 0,\n  bottom: 0,\n  zIndex: theme.zIndex.drawer - 1,\n  variants: [{\n    props: {\n      anchor: 'left'\n    },\n    style: {\n      right: 'auto'\n    }\n  }, {\n    props: {\n      anchor: 'right'\n    },\n    style: {\n      left: 'auto',\n      right: 0\n    }\n  }, {\n    props: {\n      anchor: 'top'\n    },\n    style: {\n      bottom: 'auto',\n      right: 0\n    }\n  }, {\n    props: {\n      anchor: 'bottom'\n    },\n    style: {\n      top: 'auto',\n      bottom: 0,\n      right: 0\n    }\n  }]\n})));\n\n/**\n * @ignore - internal component.\n */\nconst SwipeArea = /*#__PURE__*/React.forwardRef(function SwipeArea(props, ref) {\n  const {\n    anchor,\n    classes = {},\n    className,\n    width,\n    style,\n    ...other\n  } = props;\n  const ownerState = props;\n  return /*#__PURE__*/_jsx(SwipeAreaRoot, {\n    className: clsx('PrivateSwipeArea-root', classes.root, classes[`anchor${capitalize(anchor)}`], className),\n    ref: ref,\n    style: {\n      [isHorizontal(anchor) ? 'width' : 'height']: width,\n      ...style\n    },\n    ownerState: ownerState,\n    ...other\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? SwipeArea.propTypes = {\n  /**\n   * Side on which to attach the discovery area.\n   */\n  anchor: PropTypes.oneOf(['left', 'top', 'right', 'bottom']).isRequired,\n  /**\n   * @ignore\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * @ignore\n   */\n  style: PropTypes.object,\n  /**\n   * The width of the left most (or right most) area in `px` where the\n   * drawer can be swiped open from.\n   */\n  width: PropTypes.number.isRequired\n} : void 0;\nexport default SwipeArea;"], "mappings": "AAAA,YAAY;;AAAC,OAAAA,aAAA;AAAA,OAAAC,wBAAA;AAAA,MAAAC,SAAA;AAEb,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,IAAI,MAAM,MAAM;AACvB,SAASC,MAAM,QAAQ,yBAAyB;AAChD,OAAOC,SAAS,MAAM,uBAAuB;AAC7C,OAAOC,qBAAqB,MAAM,oCAAoC;AACtE,OAAOC,UAAU,MAAM,wBAAwB;AAC/C,SAASC,YAAY,QAAQ,qBAAqB;AAClD,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,aAAa,GAAGP,MAAM,CAAC,KAAK,EAAE;EAClCQ,IAAI,EAAE,cAAc;EACpBC,iBAAiB,EAAEP;AACrB,CAAC,CAAC,CAACD,SAAS,CAACS,IAAA;EAAA,IAAC;IACZC;EACF,CAAC,GAAAD,IAAA;EAAA,OAAM;IACLE,QAAQ,EAAE,OAAO;IACjBC,GAAG,EAAE,CAAC;IACNC,IAAI,EAAE,CAAC;IACPC,MAAM,EAAE,CAAC;IACTC,MAAM,EAAEL,KAAK,CAACK,MAAM,CAACC,MAAM,GAAG,CAAC;IAC/BC,QAAQ,EAAE,CAAC;MACTC,KAAK,EAAE;QACLC,MAAM,EAAE;MACV,CAAC;MACDC,KAAK,EAAE;QACLC,KAAK,EAAE;MACT;IACF,CAAC,EAAE;MACDH,KAAK,EAAE;QACLC,MAAM,EAAE;MACV,CAAC;MACDC,KAAK,EAAE;QACLP,IAAI,EAAE,MAAM;QACZQ,KAAK,EAAE;MACT;IACF,CAAC,EAAE;MACDH,KAAK,EAAE;QACLC,MAAM,EAAE;MACV,CAAC;MACDC,KAAK,EAAE;QACLN,MAAM,EAAE,MAAM;QACdO,KAAK,EAAE;MACT;IACF,CAAC,EAAE;MACDH,KAAK,EAAE;QACLC,MAAM,EAAE;MACV,CAAC;MACDC,KAAK,EAAE;QACLR,GAAG,EAAE,MAAM;QACXE,MAAM,EAAE,CAAC;QACTO,KAAK,EAAE;MACT;IACF,CAAC;EACH,CAAC;AAAA,CAAC,CAAC,CAAC;;AAEJ;AACA;AACA;AACA,MAAMC,SAAS,GAAG,aAAa1B,KAAK,CAAC2B,UAAU,CAAC,SAASD,SAASA,CAACJ,KAAK,EAAEM,GAAG,EAAE;EAC7E,MAAM;MACJL,MAAM;MACNM,OAAO,GAAG,CAAC,CAAC;MACZC,SAAS;MACTC,KAAK;MACLP;IAEF,CAAC,GAAGF,KAAK;IADJU,KAAK,GAAAlC,wBAAA,CACNwB,KAAK,EAAAvB,SAAA;EACT,MAAMkC,UAAU,GAAGX,KAAK;EACxB,OAAO,aAAab,IAAI,CAACC,aAAa,EAAAb,aAAA;IACpCiC,SAAS,EAAE5B,IAAI,CAAC,uBAAuB,EAAE2B,OAAO,CAACK,IAAI,EAAEL,OAAO,UAAAM,MAAA,CAAU7B,UAAU,CAACiB,MAAM,CAAC,EAAG,EAAEO,SAAS,CAAC;IACzGF,GAAG,EAAEA,GAAG;IACRJ,KAAK,EAAA3B,aAAA;MACH,CAACU,YAAY,CAACgB,MAAM,CAAC,GAAG,OAAO,GAAG,QAAQ,GAAGQ;IAAK,GAC/CP,KAAK,CACT;IACDS,UAAU,EAAEA;EAAU,GACnBD,KAAK,CACT,CAAC;AACJ,CAAC,CAAC;AACFI,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGZ,SAAS,CAACa,SAAS,GAAG;EAC5D;AACF;AACA;EACEhB,MAAM,EAAEtB,SAAS,CAACuC,KAAK,CAAC,CAAC,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAC,CAACC,UAAU;EACtE;AACF;AACA;EACEZ,OAAO,EAAE5B,SAAS,CAACyC,MAAM;EACzB;AACF;AACA;EACEZ,SAAS,EAAE7B,SAAS,CAAC0C,MAAM;EAC3B;AACF;AACA;EACEnB,KAAK,EAAEvB,SAAS,CAACyC,MAAM;EACvB;AACF;AACA;AACA;EACEX,KAAK,EAAE9B,SAAS,CAAC2C,MAAM,CAACH;AAC1B,CAAC,GAAG,KAAK,CAAC;AACV,eAAef,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}