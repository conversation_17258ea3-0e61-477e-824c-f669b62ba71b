import { 
  collection, 
  addDoc, 
  getDocs, 
  doc, 
  updateDoc, 
  query, 
  orderBy, 
  limit,
  Timestamp,
  onSnapshot,
  arrayUnion,
  arrayRemove 
} from 'firebase/firestore';
import { db } from '../firebase/config';

export interface Post {
  id: string;
  authorId: string;
  authorName: string;
  authorAvatar?: string;
  content: string;
  imageUrl?: string;
  timestamp: Date;
  likes: string[]; // Array of user IDs who liked the post
  comments: number;
  tags: string[];
}

export interface PostComment {
  id: string;
  postId: string;
  authorId: string;
  authorName: string;
  authorAvatar?: string;
  content: string;
  timestamp: Date;
}

export class PostService {
  // Create a new post
  static async createPost(postData: Omit<Post, 'id' | 'timestamp' | 'likes' | 'comments'>): Promise<string> {
    try {
      const docRef = await addDoc(collection(db, 'posts'), {
        ...postData,
        timestamp: Timestamp.now(),
        likes: [],
        comments: 0,
      });
      return docRef.id;
    } catch (error) {
      console.error('Error creating post:', error);
      throw error;
    }
  }

  // Get all posts
  static async getPosts(): Promise<Post[]> {
    try {
      const q = query(
        collection(db, 'posts'),
        orderBy('timestamp', 'desc'),
        limit(50)
      );
      
      const querySnapshot = await getDocs(q);
      return querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        timestamp: doc.data().timestamp?.toDate() || new Date(),
      })) as Post[];
    } catch (error) {
      console.error('Error fetching posts:', error);
      throw error;
    }
  }

  // Subscribe to real-time posts updates
  static subscribeToPosts(callback: (posts: Post[]) => void): () => void {
    const q = query(
      collection(db, 'posts'),
      orderBy('timestamp', 'desc'),
      limit(50)
    );
    
    return onSnapshot(q, (querySnapshot) => {
      const posts = querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        timestamp: doc.data().timestamp?.toDate() || new Date(),
      })) as Post[];
      callback(posts);
    }, (error) => {
      console.error('Error in posts subscription:', error);
    });
  }

  // Like a post
  static async likePost(postId: string, userId: string): Promise<void> {
    try {
      const postRef = doc(db, 'posts', postId);
      await updateDoc(postRef, {
        likes: arrayUnion(userId)
      });
    } catch (error) {
      console.error('Error liking post:', error);
      throw error;
    }
  }

  // Unlike a post
  static async unlikePost(postId: string, userId: string): Promise<void> {
    try {
      const postRef = doc(db, 'posts', postId);
      await updateDoc(postRef, {
        likes: arrayRemove(userId)
      });
    } catch (error) {
      console.error('Error unliking post:', error);
      throw error;
    }
  }

  // Add a comment to a post
  static async addComment(commentData: Omit<PostComment, 'id' | 'timestamp'>): Promise<string> {
    try {
      // Add comment to comments collection
      const docRef = await addDoc(collection(db, 'comments'), {
        ...commentData,
        timestamp: Timestamp.now(),
      });

      // Increment comment count on the post
      const postRef = doc(db, 'posts', commentData.postId);
      await updateDoc(postRef, {
        comments: arrayUnion(docRef.id)
      });

      return docRef.id;
    } catch (error) {
      console.error('Error adding comment:', error);
      throw error;
    }
  }

  // Get comments for a post
  static async getPostComments(postId: string): Promise<PostComment[]> {
    try {
      const q = query(
        collection(db, 'comments'),
        orderBy('timestamp', 'asc')
      );
      
      const querySnapshot = await getDocs(q);
      const allComments = querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        timestamp: doc.data().timestamp?.toDate() || new Date(),
      })) as PostComment[];

      // Filter comments for this specific post
      return allComments.filter(comment => comment.postId === postId);
    } catch (error) {
      console.error('Error fetching post comments:', error);
      throw error;
    }
  }
}
