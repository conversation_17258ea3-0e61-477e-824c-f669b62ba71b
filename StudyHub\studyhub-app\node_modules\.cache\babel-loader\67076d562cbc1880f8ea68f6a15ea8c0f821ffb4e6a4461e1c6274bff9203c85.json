{"ast": null, "code": "import { getDayOfYear } from \"../../getDayOfYear.js\";\nimport { getISOWeek } from \"../../getISOWeek.js\";\nimport { getISOWeekYear } from \"../../getISOWeekYear.js\";\nimport { getWeek } from \"../../getWeek.js\";\nimport { getWeekYear } from \"../../getWeekYear.js\";\nimport { addLeadingZeros } from \"../addLeadingZeros.js\";\nimport { lightFormatters } from \"./lightFormatters.js\";\nconst dayPeriodEnum = {\n  am: \"am\",\n  pm: \"pm\",\n  midnight: \"midnight\",\n  noon: \"noon\",\n  morning: \"morning\",\n  afternoon: \"afternoon\",\n  evening: \"evening\",\n  night: \"night\"\n};\n\n/*\n * |     | Unit                           |     | Unit                           |\n * |-----|--------------------------------|-----|--------------------------------|\n * |  a  | AM, PM                         |  A* | Milliseconds in day            |\n * |  b  | AM, PM, noon, midnight         |  B  | Flexible day period            |\n * |  c  | Stand-alone local day of week  |  C* | Localized hour w/ day period   |\n * |  d  | Day of month                   |  D  | Day of year                    |\n * |  e  | Local day of week              |  E  | Day of week                    |\n * |  f  |                                |  F* | Day of week in month           |\n * |  g* | Modified Julian day            |  G  | Era                            |\n * |  h  | Hour [1-12]                    |  H  | Hour [0-23]                    |\n * |  i! | ISO day of week                |  I! | ISO week of year               |\n * |  j* | Localized hour w/ day period   |  J* | Localized hour w/o day period  |\n * |  k  | Hour [1-24]                    |  K  | Hour [0-11]                    |\n * |  l* | (deprecated)                   |  L  | Stand-alone month              |\n * |  m  | Minute                         |  M  | Month                          |\n * |  n  |                                |  N  |                                |\n * |  o! | Ordinal number modifier        |  O  | Timezone (GMT)                 |\n * |  p! | Long localized time            |  P! | Long localized date            |\n * |  q  | Stand-alone quarter            |  Q  | Quarter                        |\n * |  r* | Related Gregorian year         |  R! | ISO week-numbering year        |\n * |  s  | Second                         |  S  | Fraction of second             |\n * |  t! | Seconds timestamp              |  T! | Milliseconds timestamp         |\n * |  u  | Extended year                  |  U* | Cyclic year                    |\n * |  v* | Timezone (generic non-locat.)  |  V* | Timezone (location)            |\n * |  w  | Local week of year             |  W* | Week of month                  |\n * |  x  | Timezone (ISO-8601 w/o Z)      |  X  | Timezone (ISO-8601)            |\n * |  y  | Year (abs)                     |  Y  | Local week-numbering year      |\n * |  z  | Timezone (specific non-locat.) |  Z* | Timezone (aliases)             |\n *\n * Letters marked by * are not implemented but reserved by Unicode standard.\n *\n * Letters marked by ! are non-standard, but implemented by date-fns:\n * - `o` modifies the previous token to turn it into an ordinal (see `format` docs)\n * - `i` is ISO day of week. For `i` and `ii` is returns numeric ISO week days,\n *   i.e. 7 for Sunday, 1 for Monday, etc.\n * - `I` is ISO week of year, as opposed to `w` which is local week of year.\n * - `R` is ISO week-numbering year, as opposed to `Y` which is local week-numbering year.\n *   `R` is supposed to be used in conjunction with `I` and `i`\n *   for universal ISO week-numbering date, whereas\n *   `Y` is supposed to be used in conjunction with `w` and `e`\n *   for week-numbering date specific to the locale.\n * - `P` is long localized date format\n * - `p` is long localized time format\n */\n\nexport const formatters = {\n  // Era\n  G: function (date, token, localize) {\n    const era = date.getFullYear() > 0 ? 1 : 0;\n    switch (token) {\n      // AD, BC\n      case \"G\":\n      case \"GG\":\n      case \"GGG\":\n        return localize.era(era, {\n          width: \"abbreviated\"\n        });\n      // A, B\n      case \"GGGGG\":\n        return localize.era(era, {\n          width: \"narrow\"\n        });\n      // Anno Domini, Before Christ\n      case \"GGGG\":\n      default:\n        return localize.era(era, {\n          width: \"wide\"\n        });\n    }\n  },\n  // Year\n  y: function (date, token, localize) {\n    // Ordinal number\n    if (token === \"yo\") {\n      const signedYear = date.getFullYear();\n      // Returns 1 for 1 BC (which is year 0 in JavaScript)\n      const year = signedYear > 0 ? signedYear : 1 - signedYear;\n      return localize.ordinalNumber(year, {\n        unit: \"year\"\n      });\n    }\n    return lightFormatters.y(date, token);\n  },\n  // Local week-numbering year\n  Y: function (date, token, localize, options) {\n    const signedWeekYear = getWeekYear(date, options);\n    // Returns 1 for 1 BC (which is year 0 in JavaScript)\n    const weekYear = signedWeekYear > 0 ? signedWeekYear : 1 - signedWeekYear;\n\n    // Two digit year\n    if (token === \"YY\") {\n      const twoDigitYear = weekYear % 100;\n      return addLeadingZeros(twoDigitYear, 2);\n    }\n\n    // Ordinal number\n    if (token === \"Yo\") {\n      return localize.ordinalNumber(weekYear, {\n        unit: \"year\"\n      });\n    }\n\n    // Padding\n    return addLeadingZeros(weekYear, token.length);\n  },\n  // ISO week-numbering year\n  R: function (date, token) {\n    const isoWeekYear = getISOWeekYear(date);\n\n    // Padding\n    return addLeadingZeros(isoWeekYear, token.length);\n  },\n  // Extended year. This is a single number designating the year of this calendar system.\n  // The main difference between `y` and `u` localizers are B.C. years:\n  // | Year | `y` | `u` |\n  // |------|-----|-----|\n  // | AC 1 |   1 |   1 |\n  // | BC 1 |   1 |   0 |\n  // | BC 2 |   2 |  -1 |\n  // Also `yy` always returns the last two digits of a year,\n  // while `uu` pads single digit years to 2 characters and returns other years unchanged.\n  u: function (date, token) {\n    const year = date.getFullYear();\n    return addLeadingZeros(year, token.length);\n  },\n  // Quarter\n  Q: function (date, token, localize) {\n    const quarter = Math.ceil((date.getMonth() + 1) / 3);\n    switch (token) {\n      // 1, 2, 3, 4\n      case \"Q\":\n        return String(quarter);\n      // 01, 02, 03, 04\n      case \"QQ\":\n        return addLeadingZeros(quarter, 2);\n      // 1st, 2nd, 3rd, 4th\n      case \"Qo\":\n        return localize.ordinalNumber(quarter, {\n          unit: \"quarter\"\n        });\n      // Q1, Q2, Q3, Q4\n      case \"QQQ\":\n        return localize.quarter(quarter, {\n          width: \"abbreviated\",\n          context: \"formatting\"\n        });\n      // 1, 2, 3, 4 (narrow quarter; could be not numerical)\n      case \"QQQQQ\":\n        return localize.quarter(quarter, {\n          width: \"narrow\",\n          context: \"formatting\"\n        });\n      // 1st quarter, 2nd quarter, ...\n      case \"QQQQ\":\n      default:\n        return localize.quarter(quarter, {\n          width: \"wide\",\n          context: \"formatting\"\n        });\n    }\n  },\n  // Stand-alone quarter\n  q: function (date, token, localize) {\n    const quarter = Math.ceil((date.getMonth() + 1) / 3);\n    switch (token) {\n      // 1, 2, 3, 4\n      case \"q\":\n        return String(quarter);\n      // 01, 02, 03, 04\n      case \"qq\":\n        return addLeadingZeros(quarter, 2);\n      // 1st, 2nd, 3rd, 4th\n      case \"qo\":\n        return localize.ordinalNumber(quarter, {\n          unit: \"quarter\"\n        });\n      // Q1, Q2, Q3, Q4\n      case \"qqq\":\n        return localize.quarter(quarter, {\n          width: \"abbreviated\",\n          context: \"standalone\"\n        });\n      // 1, 2, 3, 4 (narrow quarter; could be not numerical)\n      case \"qqqqq\":\n        return localize.quarter(quarter, {\n          width: \"narrow\",\n          context: \"standalone\"\n        });\n      // 1st quarter, 2nd quarter, ...\n      case \"qqqq\":\n      default:\n        return localize.quarter(quarter, {\n          width: \"wide\",\n          context: \"standalone\"\n        });\n    }\n  },\n  // Month\n  M: function (date, token, localize) {\n    const month = date.getMonth();\n    switch (token) {\n      case \"M\":\n      case \"MM\":\n        return lightFormatters.M(date, token);\n      // 1st, 2nd, ..., 12th\n      case \"Mo\":\n        return localize.ordinalNumber(month + 1, {\n          unit: \"month\"\n        });\n      // Jan, Feb, ..., Dec\n      case \"MMM\":\n        return localize.month(month, {\n          width: \"abbreviated\",\n          context: \"formatting\"\n        });\n      // J, F, ..., D\n      case \"MMMMM\":\n        return localize.month(month, {\n          width: \"narrow\",\n          context: \"formatting\"\n        });\n      // January, February, ..., December\n      case \"MMMM\":\n      default:\n        return localize.month(month, {\n          width: \"wide\",\n          context: \"formatting\"\n        });\n    }\n  },\n  // Stand-alone month\n  L: function (date, token, localize) {\n    const month = date.getMonth();\n    switch (token) {\n      // 1, 2, ..., 12\n      case \"L\":\n        return String(month + 1);\n      // 01, 02, ..., 12\n      case \"LL\":\n        return addLeadingZeros(month + 1, 2);\n      // 1st, 2nd, ..., 12th\n      case \"Lo\":\n        return localize.ordinalNumber(month + 1, {\n          unit: \"month\"\n        });\n      // Jan, Feb, ..., Dec\n      case \"LLL\":\n        return localize.month(month, {\n          width: \"abbreviated\",\n          context: \"standalone\"\n        });\n      // J, F, ..., D\n      case \"LLLLL\":\n        return localize.month(month, {\n          width: \"narrow\",\n          context: \"standalone\"\n        });\n      // January, February, ..., December\n      case \"LLLL\":\n      default:\n        return localize.month(month, {\n          width: \"wide\",\n          context: \"standalone\"\n        });\n    }\n  },\n  // Local week of year\n  w: function (date, token, localize, options) {\n    const week = getWeek(date, options);\n    if (token === \"wo\") {\n      return localize.ordinalNumber(week, {\n        unit: \"week\"\n      });\n    }\n    return addLeadingZeros(week, token.length);\n  },\n  // ISO week of year\n  I: function (date, token, localize) {\n    const isoWeek = getISOWeek(date);\n    if (token === \"Io\") {\n      return localize.ordinalNumber(isoWeek, {\n        unit: \"week\"\n      });\n    }\n    return addLeadingZeros(isoWeek, token.length);\n  },\n  // Day of the month\n  d: function (date, token, localize) {\n    if (token === \"do\") {\n      return localize.ordinalNumber(date.getDate(), {\n        unit: \"date\"\n      });\n    }\n    return lightFormatters.d(date, token);\n  },\n  // Day of year\n  D: function (date, token, localize) {\n    const dayOfYear = getDayOfYear(date);\n    if (token === \"Do\") {\n      return localize.ordinalNumber(dayOfYear, {\n        unit: \"dayOfYear\"\n      });\n    }\n    return addLeadingZeros(dayOfYear, token.length);\n  },\n  // Day of week\n  E: function (date, token, localize) {\n    const dayOfWeek = date.getDay();\n    switch (token) {\n      // Tue\n      case \"E\":\n      case \"EE\":\n      case \"EEE\":\n        return localize.day(dayOfWeek, {\n          width: \"abbreviated\",\n          context: \"formatting\"\n        });\n      // T\n      case \"EEEEE\":\n        return localize.day(dayOfWeek, {\n          width: \"narrow\",\n          context: \"formatting\"\n        });\n      // Tu\n      case \"EEEEEE\":\n        return localize.day(dayOfWeek, {\n          width: \"short\",\n          context: \"formatting\"\n        });\n      // Tuesday\n      case \"EEEE\":\n      default:\n        return localize.day(dayOfWeek, {\n          width: \"wide\",\n          context: \"formatting\"\n        });\n    }\n  },\n  // Local day of week\n  e: function (date, token, localize, options) {\n    const dayOfWeek = date.getDay();\n    const localDayOfWeek = (dayOfWeek - options.weekStartsOn + 8) % 7 || 7;\n    switch (token) {\n      // Numerical value (Nth day of week with current locale or weekStartsOn)\n      case \"e\":\n        return String(localDayOfWeek);\n      // Padded numerical value\n      case \"ee\":\n        return addLeadingZeros(localDayOfWeek, 2);\n      // 1st, 2nd, ..., 7th\n      case \"eo\":\n        return localize.ordinalNumber(localDayOfWeek, {\n          unit: \"day\"\n        });\n      case \"eee\":\n        return localize.day(dayOfWeek, {\n          width: \"abbreviated\",\n          context: \"formatting\"\n        });\n      // T\n      case \"eeeee\":\n        return localize.day(dayOfWeek, {\n          width: \"narrow\",\n          context: \"formatting\"\n        });\n      // Tu\n      case \"eeeeee\":\n        return localize.day(dayOfWeek, {\n          width: \"short\",\n          context: \"formatting\"\n        });\n      // Tuesday\n      case \"eeee\":\n      default:\n        return localize.day(dayOfWeek, {\n          width: \"wide\",\n          context: \"formatting\"\n        });\n    }\n  },\n  // Stand-alone local day of week\n  c: function (date, token, localize, options) {\n    const dayOfWeek = date.getDay();\n    const localDayOfWeek = (dayOfWeek - options.weekStartsOn + 8) % 7 || 7;\n    switch (token) {\n      // Numerical value (same as in `e`)\n      case \"c\":\n        return String(localDayOfWeek);\n      // Padded numerical value\n      case \"cc\":\n        return addLeadingZeros(localDayOfWeek, token.length);\n      // 1st, 2nd, ..., 7th\n      case \"co\":\n        return localize.ordinalNumber(localDayOfWeek, {\n          unit: \"day\"\n        });\n      case \"ccc\":\n        return localize.day(dayOfWeek, {\n          width: \"abbreviated\",\n          context: \"standalone\"\n        });\n      // T\n      case \"ccccc\":\n        return localize.day(dayOfWeek, {\n          width: \"narrow\",\n          context: \"standalone\"\n        });\n      // Tu\n      case \"cccccc\":\n        return localize.day(dayOfWeek, {\n          width: \"short\",\n          context: \"standalone\"\n        });\n      // Tuesday\n      case \"cccc\":\n      default:\n        return localize.day(dayOfWeek, {\n          width: \"wide\",\n          context: \"standalone\"\n        });\n    }\n  },\n  // ISO day of week\n  i: function (date, token, localize) {\n    const dayOfWeek = date.getDay();\n    const isoDayOfWeek = dayOfWeek === 0 ? 7 : dayOfWeek;\n    switch (token) {\n      // 2\n      case \"i\":\n        return String(isoDayOfWeek);\n      // 02\n      case \"ii\":\n        return addLeadingZeros(isoDayOfWeek, token.length);\n      // 2nd\n      case \"io\":\n        return localize.ordinalNumber(isoDayOfWeek, {\n          unit: \"day\"\n        });\n      // Tue\n      case \"iii\":\n        return localize.day(dayOfWeek, {\n          width: \"abbreviated\",\n          context: \"formatting\"\n        });\n      // T\n      case \"iiiii\":\n        return localize.day(dayOfWeek, {\n          width: \"narrow\",\n          context: \"formatting\"\n        });\n      // Tu\n      case \"iiiiii\":\n        return localize.day(dayOfWeek, {\n          width: \"short\",\n          context: \"formatting\"\n        });\n      // Tuesday\n      case \"iiii\":\n      default:\n        return localize.day(dayOfWeek, {\n          width: \"wide\",\n          context: \"formatting\"\n        });\n    }\n  },\n  // AM or PM\n  a: function (date, token, localize) {\n    const hours = date.getHours();\n    const dayPeriodEnumValue = hours / 12 >= 1 ? \"pm\" : \"am\";\n    switch (token) {\n      case \"a\":\n      case \"aa\":\n        return localize.dayPeriod(dayPeriodEnumValue, {\n          width: \"abbreviated\",\n          context: \"formatting\"\n        });\n      case \"aaa\":\n        return localize.dayPeriod(dayPeriodEnumValue, {\n          width: \"abbreviated\",\n          context: \"formatting\"\n        }).toLowerCase();\n      case \"aaaaa\":\n        return localize.dayPeriod(dayPeriodEnumValue, {\n          width: \"narrow\",\n          context: \"formatting\"\n        });\n      case \"aaaa\":\n      default:\n        return localize.dayPeriod(dayPeriodEnumValue, {\n          width: \"wide\",\n          context: \"formatting\"\n        });\n    }\n  },\n  // AM, PM, midnight, noon\n  b: function (date, token, localize) {\n    const hours = date.getHours();\n    let dayPeriodEnumValue;\n    if (hours === 12) {\n      dayPeriodEnumValue = dayPeriodEnum.noon;\n    } else if (hours === 0) {\n      dayPeriodEnumValue = dayPeriodEnum.midnight;\n    } else {\n      dayPeriodEnumValue = hours / 12 >= 1 ? \"pm\" : \"am\";\n    }\n    switch (token) {\n      case \"b\":\n      case \"bb\":\n        return localize.dayPeriod(dayPeriodEnumValue, {\n          width: \"abbreviated\",\n          context: \"formatting\"\n        });\n      case \"bbb\":\n        return localize.dayPeriod(dayPeriodEnumValue, {\n          width: \"abbreviated\",\n          context: \"formatting\"\n        }).toLowerCase();\n      case \"bbbbb\":\n        return localize.dayPeriod(dayPeriodEnumValue, {\n          width: \"narrow\",\n          context: \"formatting\"\n        });\n      case \"bbbb\":\n      default:\n        return localize.dayPeriod(dayPeriodEnumValue, {\n          width: \"wide\",\n          context: \"formatting\"\n        });\n    }\n  },\n  // in the morning, in the afternoon, in the evening, at night\n  B: function (date, token, localize) {\n    const hours = date.getHours();\n    let dayPeriodEnumValue;\n    if (hours >= 17) {\n      dayPeriodEnumValue = dayPeriodEnum.evening;\n    } else if (hours >= 12) {\n      dayPeriodEnumValue = dayPeriodEnum.afternoon;\n    } else if (hours >= 4) {\n      dayPeriodEnumValue = dayPeriodEnum.morning;\n    } else {\n      dayPeriodEnumValue = dayPeriodEnum.night;\n    }\n    switch (token) {\n      case \"B\":\n      case \"BB\":\n      case \"BBB\":\n        return localize.dayPeriod(dayPeriodEnumValue, {\n          width: \"abbreviated\",\n          context: \"formatting\"\n        });\n      case \"BBBBB\":\n        return localize.dayPeriod(dayPeriodEnumValue, {\n          width: \"narrow\",\n          context: \"formatting\"\n        });\n      case \"BBBB\":\n      default:\n        return localize.dayPeriod(dayPeriodEnumValue, {\n          width: \"wide\",\n          context: \"formatting\"\n        });\n    }\n  },\n  // Hour [1-12]\n  h: function (date, token, localize) {\n    if (token === \"ho\") {\n      let hours = date.getHours() % 12;\n      if (hours === 0) hours = 12;\n      return localize.ordinalNumber(hours, {\n        unit: \"hour\"\n      });\n    }\n    return lightFormatters.h(date, token);\n  },\n  // Hour [0-23]\n  H: function (date, token, localize) {\n    if (token === \"Ho\") {\n      return localize.ordinalNumber(date.getHours(), {\n        unit: \"hour\"\n      });\n    }\n    return lightFormatters.H(date, token);\n  },\n  // Hour [0-11]\n  K: function (date, token, localize) {\n    const hours = date.getHours() % 12;\n    if (token === \"Ko\") {\n      return localize.ordinalNumber(hours, {\n        unit: \"hour\"\n      });\n    }\n    return addLeadingZeros(hours, token.length);\n  },\n  // Hour [1-24]\n  k: function (date, token, localize) {\n    let hours = date.getHours();\n    if (hours === 0) hours = 24;\n    if (token === \"ko\") {\n      return localize.ordinalNumber(hours, {\n        unit: \"hour\"\n      });\n    }\n    return addLeadingZeros(hours, token.length);\n  },\n  // Minute\n  m: function (date, token, localize) {\n    if (token === \"mo\") {\n      return localize.ordinalNumber(date.getMinutes(), {\n        unit: \"minute\"\n      });\n    }\n    return lightFormatters.m(date, token);\n  },\n  // Second\n  s: function (date, token, localize) {\n    if (token === \"so\") {\n      return localize.ordinalNumber(date.getSeconds(), {\n        unit: \"second\"\n      });\n    }\n    return lightFormatters.s(date, token);\n  },\n  // Fraction of second\n  S: function (date, token) {\n    return lightFormatters.S(date, token);\n  },\n  // Timezone (ISO-8601. If offset is 0, output is always `'Z'`)\n  X: function (date, token, _localize) {\n    const timezoneOffset = date.getTimezoneOffset();\n    if (timezoneOffset === 0) {\n      return \"Z\";\n    }\n    switch (token) {\n      // Hours and optional minutes\n      case \"X\":\n        return formatTimezoneWithOptionalMinutes(timezoneOffset);\n\n      // Hours, minutes and optional seconds without `:` delimiter\n      // Note: neither ISO-8601 nor JavaScript supports seconds in timezone offsets\n      // so this token always has the same output as `XX`\n      case \"XXXX\":\n      case \"XX\":\n        // Hours and minutes without `:` delimiter\n        return formatTimezone(timezoneOffset);\n\n      // Hours, minutes and optional seconds with `:` delimiter\n      // Note: neither ISO-8601 nor JavaScript supports seconds in timezone offsets\n      // so this token always has the same output as `XXX`\n      case \"XXXXX\":\n      case \"XXX\": // Hours and minutes with `:` delimiter\n      default:\n        return formatTimezone(timezoneOffset, \":\");\n    }\n  },\n  // Timezone (ISO-8601. If offset is 0, output is `'+00:00'` or equivalent)\n  x: function (date, token, _localize) {\n    const timezoneOffset = date.getTimezoneOffset();\n    switch (token) {\n      // Hours and optional minutes\n      case \"x\":\n        return formatTimezoneWithOptionalMinutes(timezoneOffset);\n\n      // Hours, minutes and optional seconds without `:` delimiter\n      // Note: neither ISO-8601 nor JavaScript supports seconds in timezone offsets\n      // so this token always has the same output as `xx`\n      case \"xxxx\":\n      case \"xx\":\n        // Hours and minutes without `:` delimiter\n        return formatTimezone(timezoneOffset);\n\n      // Hours, minutes and optional seconds with `:` delimiter\n      // Note: neither ISO-8601 nor JavaScript supports seconds in timezone offsets\n      // so this token always has the same output as `xxx`\n      case \"xxxxx\":\n      case \"xxx\": // Hours and minutes with `:` delimiter\n      default:\n        return formatTimezone(timezoneOffset, \":\");\n    }\n  },\n  // Timezone (GMT)\n  O: function (date, token, _localize) {\n    const timezoneOffset = date.getTimezoneOffset();\n    switch (token) {\n      // Short\n      case \"O\":\n      case \"OO\":\n      case \"OOO\":\n        return \"GMT\" + formatTimezoneShort(timezoneOffset, \":\");\n      // Long\n      case \"OOOO\":\n      default:\n        return \"GMT\" + formatTimezone(timezoneOffset, \":\");\n    }\n  },\n  // Timezone (specific non-location)\n  z: function (date, token, _localize) {\n    const timezoneOffset = date.getTimezoneOffset();\n    switch (token) {\n      // Short\n      case \"z\":\n      case \"zz\":\n      case \"zzz\":\n        return \"GMT\" + formatTimezoneShort(timezoneOffset, \":\");\n      // Long\n      case \"zzzz\":\n      default:\n        return \"GMT\" + formatTimezone(timezoneOffset, \":\");\n    }\n  },\n  // Seconds timestamp\n  t: function (date, token, _localize) {\n    const timestamp = Math.trunc(+date / 1000);\n    return addLeadingZeros(timestamp, token.length);\n  },\n  // Milliseconds timestamp\n  T: function (date, token, _localize) {\n    return addLeadingZeros(+date, token.length);\n  }\n};\nfunction formatTimezoneShort(offset) {\n  let delimiter = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : \"\";\n  const sign = offset > 0 ? \"-\" : \"+\";\n  const absOffset = Math.abs(offset);\n  const hours = Math.trunc(absOffset / 60);\n  const minutes = absOffset % 60;\n  if (minutes === 0) {\n    return sign + String(hours);\n  }\n  return sign + String(hours) + delimiter + addLeadingZeros(minutes, 2);\n}\nfunction formatTimezoneWithOptionalMinutes(offset, delimiter) {\n  if (offset % 60 === 0) {\n    const sign = offset > 0 ? \"-\" : \"+\";\n    return sign + addLeadingZeros(Math.abs(offset) / 60, 2);\n  }\n  return formatTimezone(offset, delimiter);\n}\nfunction formatTimezone(offset) {\n  let delimiter = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : \"\";\n  const sign = offset > 0 ? \"-\" : \"+\";\n  const absOffset = Math.abs(offset);\n  const hours = addLeadingZeros(Math.trunc(absOffset / 60), 2);\n  const minutes = addLeadingZeros(absOffset % 60, 2);\n  return sign + hours + delimiter + minutes;\n}", "map": {"version": 3, "names": ["getDayOfYear", "getISOWeek", "getISOWeekYear", "getWeek", "getWeekYear", "addLeadingZeros", "lightFormatters", "dayPeriodEnum", "am", "pm", "midnight", "noon", "morning", "afternoon", "evening", "night", "formatters", "G", "date", "token", "localize", "era", "getFullYear", "width", "y", "signedYear", "year", "ordinalNumber", "unit", "Y", "options", "signedWeekYear", "weekYear", "twoDigitYear", "length", "R", "isoWeekYear", "u", "Q", "quarter", "Math", "ceil", "getMonth", "String", "context", "q", "M", "month", "L", "w", "week", "I", "isoWeek", "d", "getDate", "D", "dayOfYear", "E", "dayOfWeek", "getDay", "day", "e", "localDayOfWeek", "weekStartsOn", "c", "i", "isoDayOfWeek", "a", "hours", "getHours", "dayPeriodEnumValue", "<PERSON><PERSON><PERSON><PERSON>", "toLowerCase", "b", "B", "h", "H", "K", "k", "m", "getMinutes", "s", "getSeconds", "S", "X", "_localize", "timezoneOffset", "getTimezoneOffset", "formatTimezoneWithOptionalMinutes", "formatTimezone", "x", "O", "formatTimezoneShort", "z", "t", "timestamp", "trunc", "T", "offset", "delimiter", "arguments", "undefined", "sign", "absOffset", "abs", "minutes"], "sources": ["C:/Users/<USER>/OneDrive/ドキュメント/Projects/StudyHub/studyhub-app/node_modules/date-fns/_lib/format/formatters.js"], "sourcesContent": ["import { getDayOfYear } from \"../../getDayOfYear.js\";\nimport { getISOWeek } from \"../../getISOWeek.js\";\nimport { getISOWeekYear } from \"../../getISOWeekYear.js\";\nimport { getWeek } from \"../../getWeek.js\";\nimport { getWeekYear } from \"../../getWeekYear.js\";\n\nimport { addLeadingZeros } from \"../addLeadingZeros.js\";\nimport { lightFormatters } from \"./lightFormatters.js\";\n\nconst dayPeriodEnum = {\n  am: \"am\",\n  pm: \"pm\",\n  midnight: \"midnight\",\n  noon: \"noon\",\n  morning: \"morning\",\n  afternoon: \"afternoon\",\n  evening: \"evening\",\n  night: \"night\",\n};\n\n/*\n * |     | Unit                           |     | Unit                           |\n * |-----|--------------------------------|-----|--------------------------------|\n * |  a  | AM, PM                         |  A* | Milliseconds in day            |\n * |  b  | AM, PM, noon, midnight         |  B  | Flexible day period            |\n * |  c  | Stand-alone local day of week  |  C* | Localized hour w/ day period   |\n * |  d  | Day of month                   |  D  | Day of year                    |\n * |  e  | Local day of week              |  E  | Day of week                    |\n * |  f  |                                |  F* | Day of week in month           |\n * |  g* | Modified Julian day            |  G  | Era                            |\n * |  h  | Hour [1-12]                    |  H  | Hour [0-23]                    |\n * |  i! | ISO day of week                |  I! | ISO week of year               |\n * |  j* | Localized hour w/ day period   |  J* | Localized hour w/o day period  |\n * |  k  | Hour [1-24]                    |  K  | Hour [0-11]                    |\n * |  l* | (deprecated)                   |  L  | Stand-alone month              |\n * |  m  | Minute                         |  M  | Month                          |\n * |  n  |                                |  N  |                                |\n * |  o! | Ordinal number modifier        |  O  | Timezone (GMT)                 |\n * |  p! | Long localized time            |  P! | Long localized date            |\n * |  q  | Stand-alone quarter            |  Q  | Quarter                        |\n * |  r* | Related Gregorian year         |  R! | ISO week-numbering year        |\n * |  s  | Second                         |  S  | Fraction of second             |\n * |  t! | Seconds timestamp              |  T! | Milliseconds timestamp         |\n * |  u  | Extended year                  |  U* | Cyclic year                    |\n * |  v* | Timezone (generic non-locat.)  |  V* | Timezone (location)            |\n * |  w  | Local week of year             |  W* | Week of month                  |\n * |  x  | Timezone (ISO-8601 w/o Z)      |  X  | Timezone (ISO-8601)            |\n * |  y  | Year (abs)                     |  Y  | Local week-numbering year      |\n * |  z  | Timezone (specific non-locat.) |  Z* | Timezone (aliases)             |\n *\n * Letters marked by * are not implemented but reserved by Unicode standard.\n *\n * Letters marked by ! are non-standard, but implemented by date-fns:\n * - `o` modifies the previous token to turn it into an ordinal (see `format` docs)\n * - `i` is ISO day of week. For `i` and `ii` is returns numeric ISO week days,\n *   i.e. 7 for Sunday, 1 for Monday, etc.\n * - `I` is ISO week of year, as opposed to `w` which is local week of year.\n * - `R` is ISO week-numbering year, as opposed to `Y` which is local week-numbering year.\n *   `R` is supposed to be used in conjunction with `I` and `i`\n *   for universal ISO week-numbering date, whereas\n *   `Y` is supposed to be used in conjunction with `w` and `e`\n *   for week-numbering date specific to the locale.\n * - `P` is long localized date format\n * - `p` is long localized time format\n */\n\nexport const formatters = {\n  // Era\n  G: function (date, token, localize) {\n    const era = date.getFullYear() > 0 ? 1 : 0;\n    switch (token) {\n      // AD, BC\n      case \"G\":\n      case \"GG\":\n      case \"GGG\":\n        return localize.era(era, { width: \"abbreviated\" });\n      // A, B\n      case \"GGGGG\":\n        return localize.era(era, { width: \"narrow\" });\n      // Anno Domini, Before Christ\n      case \"GGGG\":\n      default:\n        return localize.era(era, { width: \"wide\" });\n    }\n  },\n\n  // Year\n  y: function (date, token, localize) {\n    // Ordinal number\n    if (token === \"yo\") {\n      const signedYear = date.getFullYear();\n      // Returns 1 for 1 BC (which is year 0 in JavaScript)\n      const year = signedYear > 0 ? signedYear : 1 - signedYear;\n      return localize.ordinalNumber(year, { unit: \"year\" });\n    }\n\n    return lightFormatters.y(date, token);\n  },\n\n  // Local week-numbering year\n  Y: function (date, token, localize, options) {\n    const signedWeekYear = getWeekYear(date, options);\n    // Returns 1 for 1 BC (which is year 0 in JavaScript)\n    const weekYear = signedWeekYear > 0 ? signedWeekYear : 1 - signedWeekYear;\n\n    // Two digit year\n    if (token === \"YY\") {\n      const twoDigitYear = weekYear % 100;\n      return addLeadingZeros(twoDigitYear, 2);\n    }\n\n    // Ordinal number\n    if (token === \"Yo\") {\n      return localize.ordinalNumber(weekYear, { unit: \"year\" });\n    }\n\n    // Padding\n    return addLeadingZeros(weekYear, token.length);\n  },\n\n  // ISO week-numbering year\n  R: function (date, token) {\n    const isoWeekYear = getISOWeekYear(date);\n\n    // Padding\n    return addLeadingZeros(isoWeekYear, token.length);\n  },\n\n  // Extended year. This is a single number designating the year of this calendar system.\n  // The main difference between `y` and `u` localizers are B.C. years:\n  // | Year | `y` | `u` |\n  // |------|-----|-----|\n  // | AC 1 |   1 |   1 |\n  // | BC 1 |   1 |   0 |\n  // | BC 2 |   2 |  -1 |\n  // Also `yy` always returns the last two digits of a year,\n  // while `uu` pads single digit years to 2 characters and returns other years unchanged.\n  u: function (date, token) {\n    const year = date.getFullYear();\n    return addLeadingZeros(year, token.length);\n  },\n\n  // Quarter\n  Q: function (date, token, localize) {\n    const quarter = Math.ceil((date.getMonth() + 1) / 3);\n    switch (token) {\n      // 1, 2, 3, 4\n      case \"Q\":\n        return String(quarter);\n      // 01, 02, 03, 04\n      case \"QQ\":\n        return addLeadingZeros(quarter, 2);\n      // 1st, 2nd, 3rd, 4th\n      case \"Qo\":\n        return localize.ordinalNumber(quarter, { unit: \"quarter\" });\n      // Q1, Q2, Q3, Q4\n      case \"QQQ\":\n        return localize.quarter(quarter, {\n          width: \"abbreviated\",\n          context: \"formatting\",\n        });\n      // 1, 2, 3, 4 (narrow quarter; could be not numerical)\n      case \"QQQQQ\":\n        return localize.quarter(quarter, {\n          width: \"narrow\",\n          context: \"formatting\",\n        });\n      // 1st quarter, 2nd quarter, ...\n      case \"QQQQ\":\n      default:\n        return localize.quarter(quarter, {\n          width: \"wide\",\n          context: \"formatting\",\n        });\n    }\n  },\n\n  // Stand-alone quarter\n  q: function (date, token, localize) {\n    const quarter = Math.ceil((date.getMonth() + 1) / 3);\n    switch (token) {\n      // 1, 2, 3, 4\n      case \"q\":\n        return String(quarter);\n      // 01, 02, 03, 04\n      case \"qq\":\n        return addLeadingZeros(quarter, 2);\n      // 1st, 2nd, 3rd, 4th\n      case \"qo\":\n        return localize.ordinalNumber(quarter, { unit: \"quarter\" });\n      // Q1, Q2, Q3, Q4\n      case \"qqq\":\n        return localize.quarter(quarter, {\n          width: \"abbreviated\",\n          context: \"standalone\",\n        });\n      // 1, 2, 3, 4 (narrow quarter; could be not numerical)\n      case \"qqqqq\":\n        return localize.quarter(quarter, {\n          width: \"narrow\",\n          context: \"standalone\",\n        });\n      // 1st quarter, 2nd quarter, ...\n      case \"qqqq\":\n      default:\n        return localize.quarter(quarter, {\n          width: \"wide\",\n          context: \"standalone\",\n        });\n    }\n  },\n\n  // Month\n  M: function (date, token, localize) {\n    const month = date.getMonth();\n    switch (token) {\n      case \"M\":\n      case \"MM\":\n        return lightFormatters.M(date, token);\n      // 1st, 2nd, ..., 12th\n      case \"Mo\":\n        return localize.ordinalNumber(month + 1, { unit: \"month\" });\n      // Jan, Feb, ..., Dec\n      case \"MMM\":\n        return localize.month(month, {\n          width: \"abbreviated\",\n          context: \"formatting\",\n        });\n      // J, F, ..., D\n      case \"MMMMM\":\n        return localize.month(month, {\n          width: \"narrow\",\n          context: \"formatting\",\n        });\n      // January, February, ..., December\n      case \"MMMM\":\n      default:\n        return localize.month(month, { width: \"wide\", context: \"formatting\" });\n    }\n  },\n\n  // Stand-alone month\n  L: function (date, token, localize) {\n    const month = date.getMonth();\n    switch (token) {\n      // 1, 2, ..., 12\n      case \"L\":\n        return String(month + 1);\n      // 01, 02, ..., 12\n      case \"LL\":\n        return addLeadingZeros(month + 1, 2);\n      // 1st, 2nd, ..., 12th\n      case \"Lo\":\n        return localize.ordinalNumber(month + 1, { unit: \"month\" });\n      // Jan, Feb, ..., Dec\n      case \"LLL\":\n        return localize.month(month, {\n          width: \"abbreviated\",\n          context: \"standalone\",\n        });\n      // J, F, ..., D\n      case \"LLLLL\":\n        return localize.month(month, {\n          width: \"narrow\",\n          context: \"standalone\",\n        });\n      // January, February, ..., December\n      case \"LLLL\":\n      default:\n        return localize.month(month, { width: \"wide\", context: \"standalone\" });\n    }\n  },\n\n  // Local week of year\n  w: function (date, token, localize, options) {\n    const week = getWeek(date, options);\n\n    if (token === \"wo\") {\n      return localize.ordinalNumber(week, { unit: \"week\" });\n    }\n\n    return addLeadingZeros(week, token.length);\n  },\n\n  // ISO week of year\n  I: function (date, token, localize) {\n    const isoWeek = getISOWeek(date);\n\n    if (token === \"Io\") {\n      return localize.ordinalNumber(isoWeek, { unit: \"week\" });\n    }\n\n    return addLeadingZeros(isoWeek, token.length);\n  },\n\n  // Day of the month\n  d: function (date, token, localize) {\n    if (token === \"do\") {\n      return localize.ordinalNumber(date.getDate(), { unit: \"date\" });\n    }\n\n    return lightFormatters.d(date, token);\n  },\n\n  // Day of year\n  D: function (date, token, localize) {\n    const dayOfYear = getDayOfYear(date);\n\n    if (token === \"Do\") {\n      return localize.ordinalNumber(dayOfYear, { unit: \"dayOfYear\" });\n    }\n\n    return addLeadingZeros(dayOfYear, token.length);\n  },\n\n  // Day of week\n  E: function (date, token, localize) {\n    const dayOfWeek = date.getDay();\n    switch (token) {\n      // Tue\n      case \"E\":\n      case \"EE\":\n      case \"EEE\":\n        return localize.day(dayOfWeek, {\n          width: \"abbreviated\",\n          context: \"formatting\",\n        });\n      // T\n      case \"EEEEE\":\n        return localize.day(dayOfWeek, {\n          width: \"narrow\",\n          context: \"formatting\",\n        });\n      // Tu\n      case \"EEEEEE\":\n        return localize.day(dayOfWeek, {\n          width: \"short\",\n          context: \"formatting\",\n        });\n      // Tuesday\n      case \"EEEE\":\n      default:\n        return localize.day(dayOfWeek, {\n          width: \"wide\",\n          context: \"formatting\",\n        });\n    }\n  },\n\n  // Local day of week\n  e: function (date, token, localize, options) {\n    const dayOfWeek = date.getDay();\n    const localDayOfWeek = (dayOfWeek - options.weekStartsOn + 8) % 7 || 7;\n    switch (token) {\n      // Numerical value (Nth day of week with current locale or weekStartsOn)\n      case \"e\":\n        return String(localDayOfWeek);\n      // Padded numerical value\n      case \"ee\":\n        return addLeadingZeros(localDayOfWeek, 2);\n      // 1st, 2nd, ..., 7th\n      case \"eo\":\n        return localize.ordinalNumber(localDayOfWeek, { unit: \"day\" });\n      case \"eee\":\n        return localize.day(dayOfWeek, {\n          width: \"abbreviated\",\n          context: \"formatting\",\n        });\n      // T\n      case \"eeeee\":\n        return localize.day(dayOfWeek, {\n          width: \"narrow\",\n          context: \"formatting\",\n        });\n      // Tu\n      case \"eeeeee\":\n        return localize.day(dayOfWeek, {\n          width: \"short\",\n          context: \"formatting\",\n        });\n      // Tuesday\n      case \"eeee\":\n      default:\n        return localize.day(dayOfWeek, {\n          width: \"wide\",\n          context: \"formatting\",\n        });\n    }\n  },\n\n  // Stand-alone local day of week\n  c: function (date, token, localize, options) {\n    const dayOfWeek = date.getDay();\n    const localDayOfWeek = (dayOfWeek - options.weekStartsOn + 8) % 7 || 7;\n    switch (token) {\n      // Numerical value (same as in `e`)\n      case \"c\":\n        return String(localDayOfWeek);\n      // Padded numerical value\n      case \"cc\":\n        return addLeadingZeros(localDayOfWeek, token.length);\n      // 1st, 2nd, ..., 7th\n      case \"co\":\n        return localize.ordinalNumber(localDayOfWeek, { unit: \"day\" });\n      case \"ccc\":\n        return localize.day(dayOfWeek, {\n          width: \"abbreviated\",\n          context: \"standalone\",\n        });\n      // T\n      case \"ccccc\":\n        return localize.day(dayOfWeek, {\n          width: \"narrow\",\n          context: \"standalone\",\n        });\n      // Tu\n      case \"cccccc\":\n        return localize.day(dayOfWeek, {\n          width: \"short\",\n          context: \"standalone\",\n        });\n      // Tuesday\n      case \"cccc\":\n      default:\n        return localize.day(dayOfWeek, {\n          width: \"wide\",\n          context: \"standalone\",\n        });\n    }\n  },\n\n  // ISO day of week\n  i: function (date, token, localize) {\n    const dayOfWeek = date.getDay();\n    const isoDayOfWeek = dayOfWeek === 0 ? 7 : dayOfWeek;\n    switch (token) {\n      // 2\n      case \"i\":\n        return String(isoDayOfWeek);\n      // 02\n      case \"ii\":\n        return addLeadingZeros(isoDayOfWeek, token.length);\n      // 2nd\n      case \"io\":\n        return localize.ordinalNumber(isoDayOfWeek, { unit: \"day\" });\n      // Tue\n      case \"iii\":\n        return localize.day(dayOfWeek, {\n          width: \"abbreviated\",\n          context: \"formatting\",\n        });\n      // T\n      case \"iiiii\":\n        return localize.day(dayOfWeek, {\n          width: \"narrow\",\n          context: \"formatting\",\n        });\n      // Tu\n      case \"iiiiii\":\n        return localize.day(dayOfWeek, {\n          width: \"short\",\n          context: \"formatting\",\n        });\n      // Tuesday\n      case \"iiii\":\n      default:\n        return localize.day(dayOfWeek, {\n          width: \"wide\",\n          context: \"formatting\",\n        });\n    }\n  },\n\n  // AM or PM\n  a: function (date, token, localize) {\n    const hours = date.getHours();\n    const dayPeriodEnumValue = hours / 12 >= 1 ? \"pm\" : \"am\";\n\n    switch (token) {\n      case \"a\":\n      case \"aa\":\n        return localize.dayPeriod(dayPeriodEnumValue, {\n          width: \"abbreviated\",\n          context: \"formatting\",\n        });\n      case \"aaa\":\n        return localize\n          .dayPeriod(dayPeriodEnumValue, {\n            width: \"abbreviated\",\n            context: \"formatting\",\n          })\n          .toLowerCase();\n      case \"aaaaa\":\n        return localize.dayPeriod(dayPeriodEnumValue, {\n          width: \"narrow\",\n          context: \"formatting\",\n        });\n      case \"aaaa\":\n      default:\n        return localize.dayPeriod(dayPeriodEnumValue, {\n          width: \"wide\",\n          context: \"formatting\",\n        });\n    }\n  },\n\n  // AM, PM, midnight, noon\n  b: function (date, token, localize) {\n    const hours = date.getHours();\n    let dayPeriodEnumValue;\n    if (hours === 12) {\n      dayPeriodEnumValue = dayPeriodEnum.noon;\n    } else if (hours === 0) {\n      dayPeriodEnumValue = dayPeriodEnum.midnight;\n    } else {\n      dayPeriodEnumValue = hours / 12 >= 1 ? \"pm\" : \"am\";\n    }\n\n    switch (token) {\n      case \"b\":\n      case \"bb\":\n        return localize.dayPeriod(dayPeriodEnumValue, {\n          width: \"abbreviated\",\n          context: \"formatting\",\n        });\n      case \"bbb\":\n        return localize\n          .dayPeriod(dayPeriodEnumValue, {\n            width: \"abbreviated\",\n            context: \"formatting\",\n          })\n          .toLowerCase();\n      case \"bbbbb\":\n        return localize.dayPeriod(dayPeriodEnumValue, {\n          width: \"narrow\",\n          context: \"formatting\",\n        });\n      case \"bbbb\":\n      default:\n        return localize.dayPeriod(dayPeriodEnumValue, {\n          width: \"wide\",\n          context: \"formatting\",\n        });\n    }\n  },\n\n  // in the morning, in the afternoon, in the evening, at night\n  B: function (date, token, localize) {\n    const hours = date.getHours();\n    let dayPeriodEnumValue;\n    if (hours >= 17) {\n      dayPeriodEnumValue = dayPeriodEnum.evening;\n    } else if (hours >= 12) {\n      dayPeriodEnumValue = dayPeriodEnum.afternoon;\n    } else if (hours >= 4) {\n      dayPeriodEnumValue = dayPeriodEnum.morning;\n    } else {\n      dayPeriodEnumValue = dayPeriodEnum.night;\n    }\n\n    switch (token) {\n      case \"B\":\n      case \"BB\":\n      case \"BBB\":\n        return localize.dayPeriod(dayPeriodEnumValue, {\n          width: \"abbreviated\",\n          context: \"formatting\",\n        });\n      case \"BBBBB\":\n        return localize.dayPeriod(dayPeriodEnumValue, {\n          width: \"narrow\",\n          context: \"formatting\",\n        });\n      case \"BBBB\":\n      default:\n        return localize.dayPeriod(dayPeriodEnumValue, {\n          width: \"wide\",\n          context: \"formatting\",\n        });\n    }\n  },\n\n  // Hour [1-12]\n  h: function (date, token, localize) {\n    if (token === \"ho\") {\n      let hours = date.getHours() % 12;\n      if (hours === 0) hours = 12;\n      return localize.ordinalNumber(hours, { unit: \"hour\" });\n    }\n\n    return lightFormatters.h(date, token);\n  },\n\n  // Hour [0-23]\n  H: function (date, token, localize) {\n    if (token === \"Ho\") {\n      return localize.ordinalNumber(date.getHours(), { unit: \"hour\" });\n    }\n\n    return lightFormatters.H(date, token);\n  },\n\n  // Hour [0-11]\n  K: function (date, token, localize) {\n    const hours = date.getHours() % 12;\n\n    if (token === \"Ko\") {\n      return localize.ordinalNumber(hours, { unit: \"hour\" });\n    }\n\n    return addLeadingZeros(hours, token.length);\n  },\n\n  // Hour [1-24]\n  k: function (date, token, localize) {\n    let hours = date.getHours();\n    if (hours === 0) hours = 24;\n\n    if (token === \"ko\") {\n      return localize.ordinalNumber(hours, { unit: \"hour\" });\n    }\n\n    return addLeadingZeros(hours, token.length);\n  },\n\n  // Minute\n  m: function (date, token, localize) {\n    if (token === \"mo\") {\n      return localize.ordinalNumber(date.getMinutes(), { unit: \"minute\" });\n    }\n\n    return lightFormatters.m(date, token);\n  },\n\n  // Second\n  s: function (date, token, localize) {\n    if (token === \"so\") {\n      return localize.ordinalNumber(date.getSeconds(), { unit: \"second\" });\n    }\n\n    return lightFormatters.s(date, token);\n  },\n\n  // Fraction of second\n  S: function (date, token) {\n    return lightFormatters.S(date, token);\n  },\n\n  // Timezone (ISO-8601. If offset is 0, output is always `'Z'`)\n  X: function (date, token, _localize) {\n    const timezoneOffset = date.getTimezoneOffset();\n\n    if (timezoneOffset === 0) {\n      return \"Z\";\n    }\n\n    switch (token) {\n      // Hours and optional minutes\n      case \"X\":\n        return formatTimezoneWithOptionalMinutes(timezoneOffset);\n\n      // Hours, minutes and optional seconds without `:` delimiter\n      // Note: neither ISO-8601 nor JavaScript supports seconds in timezone offsets\n      // so this token always has the same output as `XX`\n      case \"XXXX\":\n      case \"XX\": // Hours and minutes without `:` delimiter\n        return formatTimezone(timezoneOffset);\n\n      // Hours, minutes and optional seconds with `:` delimiter\n      // Note: neither ISO-8601 nor JavaScript supports seconds in timezone offsets\n      // so this token always has the same output as `XXX`\n      case \"XXXXX\":\n      case \"XXX\": // Hours and minutes with `:` delimiter\n      default:\n        return formatTimezone(timezoneOffset, \":\");\n    }\n  },\n\n  // Timezone (ISO-8601. If offset is 0, output is `'+00:00'` or equivalent)\n  x: function (date, token, _localize) {\n    const timezoneOffset = date.getTimezoneOffset();\n\n    switch (token) {\n      // Hours and optional minutes\n      case \"x\":\n        return formatTimezoneWithOptionalMinutes(timezoneOffset);\n\n      // Hours, minutes and optional seconds without `:` delimiter\n      // Note: neither ISO-8601 nor JavaScript supports seconds in timezone offsets\n      // so this token always has the same output as `xx`\n      case \"xxxx\":\n      case \"xx\": // Hours and minutes without `:` delimiter\n        return formatTimezone(timezoneOffset);\n\n      // Hours, minutes and optional seconds with `:` delimiter\n      // Note: neither ISO-8601 nor JavaScript supports seconds in timezone offsets\n      // so this token always has the same output as `xxx`\n      case \"xxxxx\":\n      case \"xxx\": // Hours and minutes with `:` delimiter\n      default:\n        return formatTimezone(timezoneOffset, \":\");\n    }\n  },\n\n  // Timezone (GMT)\n  O: function (date, token, _localize) {\n    const timezoneOffset = date.getTimezoneOffset();\n\n    switch (token) {\n      // Short\n      case \"O\":\n      case \"OO\":\n      case \"OOO\":\n        return \"GMT\" + formatTimezoneShort(timezoneOffset, \":\");\n      // Long\n      case \"OOOO\":\n      default:\n        return \"GMT\" + formatTimezone(timezoneOffset, \":\");\n    }\n  },\n\n  // Timezone (specific non-location)\n  z: function (date, token, _localize) {\n    const timezoneOffset = date.getTimezoneOffset();\n\n    switch (token) {\n      // Short\n      case \"z\":\n      case \"zz\":\n      case \"zzz\":\n        return \"GMT\" + formatTimezoneShort(timezoneOffset, \":\");\n      // Long\n      case \"zzzz\":\n      default:\n        return \"GMT\" + formatTimezone(timezoneOffset, \":\");\n    }\n  },\n\n  // Seconds timestamp\n  t: function (date, token, _localize) {\n    const timestamp = Math.trunc(+date / 1000);\n    return addLeadingZeros(timestamp, token.length);\n  },\n\n  // Milliseconds timestamp\n  T: function (date, token, _localize) {\n    return addLeadingZeros(+date, token.length);\n  },\n};\n\nfunction formatTimezoneShort(offset, delimiter = \"\") {\n  const sign = offset > 0 ? \"-\" : \"+\";\n  const absOffset = Math.abs(offset);\n  const hours = Math.trunc(absOffset / 60);\n  const minutes = absOffset % 60;\n  if (minutes === 0) {\n    return sign + String(hours);\n  }\n  return sign + String(hours) + delimiter + addLeadingZeros(minutes, 2);\n}\n\nfunction formatTimezoneWithOptionalMinutes(offset, delimiter) {\n  if (offset % 60 === 0) {\n    const sign = offset > 0 ? \"-\" : \"+\";\n    return sign + addLeadingZeros(Math.abs(offset) / 60, 2);\n  }\n  return formatTimezone(offset, delimiter);\n}\n\nfunction formatTimezone(offset, delimiter = \"\") {\n  const sign = offset > 0 ? \"-\" : \"+\";\n  const absOffset = Math.abs(offset);\n  const hours = addLeadingZeros(Math.trunc(absOffset / 60), 2);\n  const minutes = addLeadingZeros(absOffset % 60, 2);\n  return sign + hours + delimiter + minutes;\n}\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,uBAAuB;AACpD,SAASC,UAAU,QAAQ,qBAAqB;AAChD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,OAAO,QAAQ,kBAAkB;AAC1C,SAASC,WAAW,QAAQ,sBAAsB;AAElD,SAASC,eAAe,QAAQ,uBAAuB;AACvD,SAASC,eAAe,QAAQ,sBAAsB;AAEtD,MAAMC,aAAa,GAAG;EACpBC,EAAE,EAAE,IAAI;EACRC,EAAE,EAAE,IAAI;EACRC,QAAQ,EAAE,UAAU;EACpBC,IAAI,EAAE,MAAM;EACZC,OAAO,EAAE,SAAS;EAClBC,SAAS,EAAE,WAAW;EACtBC,OAAO,EAAE,SAAS;EAClBC,KAAK,EAAE;AACT,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,OAAO,MAAMC,UAAU,GAAG;EACxB;EACAC,CAAC,EAAE,SAAAA,CAAUC,IAAI,EAAEC,KAAK,EAAEC,QAAQ,EAAE;IAClC,MAAMC,GAAG,GAAGH,IAAI,CAACI,WAAW,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC;IAC1C,QAAQH,KAAK;MACX;MACA,KAAK,GAAG;MACR,KAAK,IAAI;MACT,KAAK,KAAK;QACR,OAAOC,QAAQ,CAACC,GAAG,CAACA,GAAG,EAAE;UAAEE,KAAK,EAAE;QAAc,CAAC,CAAC;MACpD;MACA,KAAK,OAAO;QACV,OAAOH,QAAQ,CAACC,GAAG,CAACA,GAAG,EAAE;UAAEE,KAAK,EAAE;QAAS,CAAC,CAAC;MAC/C;MACA,KAAK,MAAM;MACX;QACE,OAAOH,QAAQ,CAACC,GAAG,CAACA,GAAG,EAAE;UAAEE,KAAK,EAAE;QAAO,CAAC,CAAC;IAC/C;EACF,CAAC;EAED;EACAC,CAAC,EAAE,SAAAA,CAAUN,IAAI,EAAEC,KAAK,EAAEC,QAAQ,EAAE;IAClC;IACA,IAAID,KAAK,KAAK,IAAI,EAAE;MAClB,MAAMM,UAAU,GAAGP,IAAI,CAACI,WAAW,CAAC,CAAC;MACrC;MACA,MAAMI,IAAI,GAAGD,UAAU,GAAG,CAAC,GAAGA,UAAU,GAAG,CAAC,GAAGA,UAAU;MACzD,OAAOL,QAAQ,CAACO,aAAa,CAACD,IAAI,EAAE;QAAEE,IAAI,EAAE;MAAO,CAAC,CAAC;IACvD;IAEA,OAAOtB,eAAe,CAACkB,CAAC,CAACN,IAAI,EAAEC,KAAK,CAAC;EACvC,CAAC;EAED;EACAU,CAAC,EAAE,SAAAA,CAAUX,IAAI,EAAEC,KAAK,EAAEC,QAAQ,EAAEU,OAAO,EAAE;IAC3C,MAAMC,cAAc,GAAG3B,WAAW,CAACc,IAAI,EAAEY,OAAO,CAAC;IACjD;IACA,MAAME,QAAQ,GAAGD,cAAc,GAAG,CAAC,GAAGA,cAAc,GAAG,CAAC,GAAGA,cAAc;;IAEzE;IACA,IAAIZ,KAAK,KAAK,IAAI,EAAE;MAClB,MAAMc,YAAY,GAAGD,QAAQ,GAAG,GAAG;MACnC,OAAO3B,eAAe,CAAC4B,YAAY,EAAE,CAAC,CAAC;IACzC;;IAEA;IACA,IAAId,KAAK,KAAK,IAAI,EAAE;MAClB,OAAOC,QAAQ,CAACO,aAAa,CAACK,QAAQ,EAAE;QAAEJ,IAAI,EAAE;MAAO,CAAC,CAAC;IAC3D;;IAEA;IACA,OAAOvB,eAAe,CAAC2B,QAAQ,EAAEb,KAAK,CAACe,MAAM,CAAC;EAChD,CAAC;EAED;EACAC,CAAC,EAAE,SAAAA,CAAUjB,IAAI,EAAEC,KAAK,EAAE;IACxB,MAAMiB,WAAW,GAAGlC,cAAc,CAACgB,IAAI,CAAC;;IAExC;IACA,OAAOb,eAAe,CAAC+B,WAAW,EAAEjB,KAAK,CAACe,MAAM,CAAC;EACnD,CAAC;EAED;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACAG,CAAC,EAAE,SAAAA,CAAUnB,IAAI,EAAEC,KAAK,EAAE;IACxB,MAAMO,IAAI,GAAGR,IAAI,CAACI,WAAW,CAAC,CAAC;IAC/B,OAAOjB,eAAe,CAACqB,IAAI,EAAEP,KAAK,CAACe,MAAM,CAAC;EAC5C,CAAC;EAED;EACAI,CAAC,EAAE,SAAAA,CAAUpB,IAAI,EAAEC,KAAK,EAAEC,QAAQ,EAAE;IAClC,MAAMmB,OAAO,GAAGC,IAAI,CAACC,IAAI,CAAC,CAACvB,IAAI,CAACwB,QAAQ,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;IACpD,QAAQvB,KAAK;MACX;MACA,KAAK,GAAG;QACN,OAAOwB,MAAM,CAACJ,OAAO,CAAC;MACxB;MACA,KAAK,IAAI;QACP,OAAOlC,eAAe,CAACkC,OAAO,EAAE,CAAC,CAAC;MACpC;MACA,KAAK,IAAI;QACP,OAAOnB,QAAQ,CAACO,aAAa,CAACY,OAAO,EAAE;UAAEX,IAAI,EAAE;QAAU,CAAC,CAAC;MAC7D;MACA,KAAK,KAAK;QACR,OAAOR,QAAQ,CAACmB,OAAO,CAACA,OAAO,EAAE;UAC/BhB,KAAK,EAAE,aAAa;UACpBqB,OAAO,EAAE;QACX,CAAC,CAAC;MACJ;MACA,KAAK,OAAO;QACV,OAAOxB,QAAQ,CAACmB,OAAO,CAACA,OAAO,EAAE;UAC/BhB,KAAK,EAAE,QAAQ;UACfqB,OAAO,EAAE;QACX,CAAC,CAAC;MACJ;MACA,KAAK,MAAM;MACX;QACE,OAAOxB,QAAQ,CAACmB,OAAO,CAACA,OAAO,EAAE;UAC/BhB,KAAK,EAAE,MAAM;UACbqB,OAAO,EAAE;QACX,CAAC,CAAC;IACN;EACF,CAAC;EAED;EACAC,CAAC,EAAE,SAAAA,CAAU3B,IAAI,EAAEC,KAAK,EAAEC,QAAQ,EAAE;IAClC,MAAMmB,OAAO,GAAGC,IAAI,CAACC,IAAI,CAAC,CAACvB,IAAI,CAACwB,QAAQ,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;IACpD,QAAQvB,KAAK;MACX;MACA,KAAK,GAAG;QACN,OAAOwB,MAAM,CAACJ,OAAO,CAAC;MACxB;MACA,KAAK,IAAI;QACP,OAAOlC,eAAe,CAACkC,OAAO,EAAE,CAAC,CAAC;MACpC;MACA,KAAK,IAAI;QACP,OAAOnB,QAAQ,CAACO,aAAa,CAACY,OAAO,EAAE;UAAEX,IAAI,EAAE;QAAU,CAAC,CAAC;MAC7D;MACA,KAAK,KAAK;QACR,OAAOR,QAAQ,CAACmB,OAAO,CAACA,OAAO,EAAE;UAC/BhB,KAAK,EAAE,aAAa;UACpBqB,OAAO,EAAE;QACX,CAAC,CAAC;MACJ;MACA,KAAK,OAAO;QACV,OAAOxB,QAAQ,CAACmB,OAAO,CAACA,OAAO,EAAE;UAC/BhB,KAAK,EAAE,QAAQ;UACfqB,OAAO,EAAE;QACX,CAAC,CAAC;MACJ;MACA,KAAK,MAAM;MACX;QACE,OAAOxB,QAAQ,CAACmB,OAAO,CAACA,OAAO,EAAE;UAC/BhB,KAAK,EAAE,MAAM;UACbqB,OAAO,EAAE;QACX,CAAC,CAAC;IACN;EACF,CAAC;EAED;EACAE,CAAC,EAAE,SAAAA,CAAU5B,IAAI,EAAEC,KAAK,EAAEC,QAAQ,EAAE;IAClC,MAAM2B,KAAK,GAAG7B,IAAI,CAACwB,QAAQ,CAAC,CAAC;IAC7B,QAAQvB,KAAK;MACX,KAAK,GAAG;MACR,KAAK,IAAI;QACP,OAAOb,eAAe,CAACwC,CAAC,CAAC5B,IAAI,EAAEC,KAAK,CAAC;MACvC;MACA,KAAK,IAAI;QACP,OAAOC,QAAQ,CAACO,aAAa,CAACoB,KAAK,GAAG,CAAC,EAAE;UAAEnB,IAAI,EAAE;QAAQ,CAAC,CAAC;MAC7D;MACA,KAAK,KAAK;QACR,OAAOR,QAAQ,CAAC2B,KAAK,CAACA,KAAK,EAAE;UAC3BxB,KAAK,EAAE,aAAa;UACpBqB,OAAO,EAAE;QACX,CAAC,CAAC;MACJ;MACA,KAAK,OAAO;QACV,OAAOxB,QAAQ,CAAC2B,KAAK,CAACA,KAAK,EAAE;UAC3BxB,KAAK,EAAE,QAAQ;UACfqB,OAAO,EAAE;QACX,CAAC,CAAC;MACJ;MACA,KAAK,MAAM;MACX;QACE,OAAOxB,QAAQ,CAAC2B,KAAK,CAACA,KAAK,EAAE;UAAExB,KAAK,EAAE,MAAM;UAAEqB,OAAO,EAAE;QAAa,CAAC,CAAC;IAC1E;EACF,CAAC;EAED;EACAI,CAAC,EAAE,SAAAA,CAAU9B,IAAI,EAAEC,KAAK,EAAEC,QAAQ,EAAE;IAClC,MAAM2B,KAAK,GAAG7B,IAAI,CAACwB,QAAQ,CAAC,CAAC;IAC7B,QAAQvB,KAAK;MACX;MACA,KAAK,GAAG;QACN,OAAOwB,MAAM,CAACI,KAAK,GAAG,CAAC,CAAC;MAC1B;MACA,KAAK,IAAI;QACP,OAAO1C,eAAe,CAAC0C,KAAK,GAAG,CAAC,EAAE,CAAC,CAAC;MACtC;MACA,KAAK,IAAI;QACP,OAAO3B,QAAQ,CAACO,aAAa,CAACoB,KAAK,GAAG,CAAC,EAAE;UAAEnB,IAAI,EAAE;QAAQ,CAAC,CAAC;MAC7D;MACA,KAAK,KAAK;QACR,OAAOR,QAAQ,CAAC2B,KAAK,CAACA,KAAK,EAAE;UAC3BxB,KAAK,EAAE,aAAa;UACpBqB,OAAO,EAAE;QACX,CAAC,CAAC;MACJ;MACA,KAAK,OAAO;QACV,OAAOxB,QAAQ,CAAC2B,KAAK,CAACA,KAAK,EAAE;UAC3BxB,KAAK,EAAE,QAAQ;UACfqB,OAAO,EAAE;QACX,CAAC,CAAC;MACJ;MACA,KAAK,MAAM;MACX;QACE,OAAOxB,QAAQ,CAAC2B,KAAK,CAACA,KAAK,EAAE;UAAExB,KAAK,EAAE,MAAM;UAAEqB,OAAO,EAAE;QAAa,CAAC,CAAC;IAC1E;EACF,CAAC;EAED;EACAK,CAAC,EAAE,SAAAA,CAAU/B,IAAI,EAAEC,KAAK,EAAEC,QAAQ,EAAEU,OAAO,EAAE;IAC3C,MAAMoB,IAAI,GAAG/C,OAAO,CAACe,IAAI,EAAEY,OAAO,CAAC;IAEnC,IAAIX,KAAK,KAAK,IAAI,EAAE;MAClB,OAAOC,QAAQ,CAACO,aAAa,CAACuB,IAAI,EAAE;QAAEtB,IAAI,EAAE;MAAO,CAAC,CAAC;IACvD;IAEA,OAAOvB,eAAe,CAAC6C,IAAI,EAAE/B,KAAK,CAACe,MAAM,CAAC;EAC5C,CAAC;EAED;EACAiB,CAAC,EAAE,SAAAA,CAAUjC,IAAI,EAAEC,KAAK,EAAEC,QAAQ,EAAE;IAClC,MAAMgC,OAAO,GAAGnD,UAAU,CAACiB,IAAI,CAAC;IAEhC,IAAIC,KAAK,KAAK,IAAI,EAAE;MAClB,OAAOC,QAAQ,CAACO,aAAa,CAACyB,OAAO,EAAE;QAAExB,IAAI,EAAE;MAAO,CAAC,CAAC;IAC1D;IAEA,OAAOvB,eAAe,CAAC+C,OAAO,EAAEjC,KAAK,CAACe,MAAM,CAAC;EAC/C,CAAC;EAED;EACAmB,CAAC,EAAE,SAAAA,CAAUnC,IAAI,EAAEC,KAAK,EAAEC,QAAQ,EAAE;IAClC,IAAID,KAAK,KAAK,IAAI,EAAE;MAClB,OAAOC,QAAQ,CAACO,aAAa,CAACT,IAAI,CAACoC,OAAO,CAAC,CAAC,EAAE;QAAE1B,IAAI,EAAE;MAAO,CAAC,CAAC;IACjE;IAEA,OAAOtB,eAAe,CAAC+C,CAAC,CAACnC,IAAI,EAAEC,KAAK,CAAC;EACvC,CAAC;EAED;EACAoC,CAAC,EAAE,SAAAA,CAAUrC,IAAI,EAAEC,KAAK,EAAEC,QAAQ,EAAE;IAClC,MAAMoC,SAAS,GAAGxD,YAAY,CAACkB,IAAI,CAAC;IAEpC,IAAIC,KAAK,KAAK,IAAI,EAAE;MAClB,OAAOC,QAAQ,CAACO,aAAa,CAAC6B,SAAS,EAAE;QAAE5B,IAAI,EAAE;MAAY,CAAC,CAAC;IACjE;IAEA,OAAOvB,eAAe,CAACmD,SAAS,EAAErC,KAAK,CAACe,MAAM,CAAC;EACjD,CAAC;EAED;EACAuB,CAAC,EAAE,SAAAA,CAAUvC,IAAI,EAAEC,KAAK,EAAEC,QAAQ,EAAE;IAClC,MAAMsC,SAAS,GAAGxC,IAAI,CAACyC,MAAM,CAAC,CAAC;IAC/B,QAAQxC,KAAK;MACX;MACA,KAAK,GAAG;MACR,KAAK,IAAI;MACT,KAAK,KAAK;QACR,OAAOC,QAAQ,CAACwC,GAAG,CAACF,SAAS,EAAE;UAC7BnC,KAAK,EAAE,aAAa;UACpBqB,OAAO,EAAE;QACX,CAAC,CAAC;MACJ;MACA,KAAK,OAAO;QACV,OAAOxB,QAAQ,CAACwC,GAAG,CAACF,SAAS,EAAE;UAC7BnC,KAAK,EAAE,QAAQ;UACfqB,OAAO,EAAE;QACX,CAAC,CAAC;MACJ;MACA,KAAK,QAAQ;QACX,OAAOxB,QAAQ,CAACwC,GAAG,CAACF,SAAS,EAAE;UAC7BnC,KAAK,EAAE,OAAO;UACdqB,OAAO,EAAE;QACX,CAAC,CAAC;MACJ;MACA,KAAK,MAAM;MACX;QACE,OAAOxB,QAAQ,CAACwC,GAAG,CAACF,SAAS,EAAE;UAC7BnC,KAAK,EAAE,MAAM;UACbqB,OAAO,EAAE;QACX,CAAC,CAAC;IACN;EACF,CAAC;EAED;EACAiB,CAAC,EAAE,SAAAA,CAAU3C,IAAI,EAAEC,KAAK,EAAEC,QAAQ,EAAEU,OAAO,EAAE;IAC3C,MAAM4B,SAAS,GAAGxC,IAAI,CAACyC,MAAM,CAAC,CAAC;IAC/B,MAAMG,cAAc,GAAG,CAACJ,SAAS,GAAG5B,OAAO,CAACiC,YAAY,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC;IACtE,QAAQ5C,KAAK;MACX;MACA,KAAK,GAAG;QACN,OAAOwB,MAAM,CAACmB,cAAc,CAAC;MAC/B;MACA,KAAK,IAAI;QACP,OAAOzD,eAAe,CAACyD,cAAc,EAAE,CAAC,CAAC;MAC3C;MACA,KAAK,IAAI;QACP,OAAO1C,QAAQ,CAACO,aAAa,CAACmC,cAAc,EAAE;UAAElC,IAAI,EAAE;QAAM,CAAC,CAAC;MAChE,KAAK,KAAK;QACR,OAAOR,QAAQ,CAACwC,GAAG,CAACF,SAAS,EAAE;UAC7BnC,KAAK,EAAE,aAAa;UACpBqB,OAAO,EAAE;QACX,CAAC,CAAC;MACJ;MACA,KAAK,OAAO;QACV,OAAOxB,QAAQ,CAACwC,GAAG,CAACF,SAAS,EAAE;UAC7BnC,KAAK,EAAE,QAAQ;UACfqB,OAAO,EAAE;QACX,CAAC,CAAC;MACJ;MACA,KAAK,QAAQ;QACX,OAAOxB,QAAQ,CAACwC,GAAG,CAACF,SAAS,EAAE;UAC7BnC,KAAK,EAAE,OAAO;UACdqB,OAAO,EAAE;QACX,CAAC,CAAC;MACJ;MACA,KAAK,MAAM;MACX;QACE,OAAOxB,QAAQ,CAACwC,GAAG,CAACF,SAAS,EAAE;UAC7BnC,KAAK,EAAE,MAAM;UACbqB,OAAO,EAAE;QACX,CAAC,CAAC;IACN;EACF,CAAC;EAED;EACAoB,CAAC,EAAE,SAAAA,CAAU9C,IAAI,EAAEC,KAAK,EAAEC,QAAQ,EAAEU,OAAO,EAAE;IAC3C,MAAM4B,SAAS,GAAGxC,IAAI,CAACyC,MAAM,CAAC,CAAC;IAC/B,MAAMG,cAAc,GAAG,CAACJ,SAAS,GAAG5B,OAAO,CAACiC,YAAY,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC;IACtE,QAAQ5C,KAAK;MACX;MACA,KAAK,GAAG;QACN,OAAOwB,MAAM,CAACmB,cAAc,CAAC;MAC/B;MACA,KAAK,IAAI;QACP,OAAOzD,eAAe,CAACyD,cAAc,EAAE3C,KAAK,CAACe,MAAM,CAAC;MACtD;MACA,KAAK,IAAI;QACP,OAAOd,QAAQ,CAACO,aAAa,CAACmC,cAAc,EAAE;UAAElC,IAAI,EAAE;QAAM,CAAC,CAAC;MAChE,KAAK,KAAK;QACR,OAAOR,QAAQ,CAACwC,GAAG,CAACF,SAAS,EAAE;UAC7BnC,KAAK,EAAE,aAAa;UACpBqB,OAAO,EAAE;QACX,CAAC,CAAC;MACJ;MACA,KAAK,OAAO;QACV,OAAOxB,QAAQ,CAACwC,GAAG,CAACF,SAAS,EAAE;UAC7BnC,KAAK,EAAE,QAAQ;UACfqB,OAAO,EAAE;QACX,CAAC,CAAC;MACJ;MACA,KAAK,QAAQ;QACX,OAAOxB,QAAQ,CAACwC,GAAG,CAACF,SAAS,EAAE;UAC7BnC,KAAK,EAAE,OAAO;UACdqB,OAAO,EAAE;QACX,CAAC,CAAC;MACJ;MACA,KAAK,MAAM;MACX;QACE,OAAOxB,QAAQ,CAACwC,GAAG,CAACF,SAAS,EAAE;UAC7BnC,KAAK,EAAE,MAAM;UACbqB,OAAO,EAAE;QACX,CAAC,CAAC;IACN;EACF,CAAC;EAED;EACAqB,CAAC,EAAE,SAAAA,CAAU/C,IAAI,EAAEC,KAAK,EAAEC,QAAQ,EAAE;IAClC,MAAMsC,SAAS,GAAGxC,IAAI,CAACyC,MAAM,CAAC,CAAC;IAC/B,MAAMO,YAAY,GAAGR,SAAS,KAAK,CAAC,GAAG,CAAC,GAAGA,SAAS;IACpD,QAAQvC,KAAK;MACX;MACA,KAAK,GAAG;QACN,OAAOwB,MAAM,CAACuB,YAAY,CAAC;MAC7B;MACA,KAAK,IAAI;QACP,OAAO7D,eAAe,CAAC6D,YAAY,EAAE/C,KAAK,CAACe,MAAM,CAAC;MACpD;MACA,KAAK,IAAI;QACP,OAAOd,QAAQ,CAACO,aAAa,CAACuC,YAAY,EAAE;UAAEtC,IAAI,EAAE;QAAM,CAAC,CAAC;MAC9D;MACA,KAAK,KAAK;QACR,OAAOR,QAAQ,CAACwC,GAAG,CAACF,SAAS,EAAE;UAC7BnC,KAAK,EAAE,aAAa;UACpBqB,OAAO,EAAE;QACX,CAAC,CAAC;MACJ;MACA,KAAK,OAAO;QACV,OAAOxB,QAAQ,CAACwC,GAAG,CAACF,SAAS,EAAE;UAC7BnC,KAAK,EAAE,QAAQ;UACfqB,OAAO,EAAE;QACX,CAAC,CAAC;MACJ;MACA,KAAK,QAAQ;QACX,OAAOxB,QAAQ,CAACwC,GAAG,CAACF,SAAS,EAAE;UAC7BnC,KAAK,EAAE,OAAO;UACdqB,OAAO,EAAE;QACX,CAAC,CAAC;MACJ;MACA,KAAK,MAAM;MACX;QACE,OAAOxB,QAAQ,CAACwC,GAAG,CAACF,SAAS,EAAE;UAC7BnC,KAAK,EAAE,MAAM;UACbqB,OAAO,EAAE;QACX,CAAC,CAAC;IACN;EACF,CAAC;EAED;EACAuB,CAAC,EAAE,SAAAA,CAAUjD,IAAI,EAAEC,KAAK,EAAEC,QAAQ,EAAE;IAClC,MAAMgD,KAAK,GAAGlD,IAAI,CAACmD,QAAQ,CAAC,CAAC;IAC7B,MAAMC,kBAAkB,GAAGF,KAAK,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,GAAG,IAAI;IAExD,QAAQjD,KAAK;MACX,KAAK,GAAG;MACR,KAAK,IAAI;QACP,OAAOC,QAAQ,CAACmD,SAAS,CAACD,kBAAkB,EAAE;UAC5C/C,KAAK,EAAE,aAAa;UACpBqB,OAAO,EAAE;QACX,CAAC,CAAC;MACJ,KAAK,KAAK;QACR,OAAOxB,QAAQ,CACZmD,SAAS,CAACD,kBAAkB,EAAE;UAC7B/C,KAAK,EAAE,aAAa;UACpBqB,OAAO,EAAE;QACX,CAAC,CAAC,CACD4B,WAAW,CAAC,CAAC;MAClB,KAAK,OAAO;QACV,OAAOpD,QAAQ,CAACmD,SAAS,CAACD,kBAAkB,EAAE;UAC5C/C,KAAK,EAAE,QAAQ;UACfqB,OAAO,EAAE;QACX,CAAC,CAAC;MACJ,KAAK,MAAM;MACX;QACE,OAAOxB,QAAQ,CAACmD,SAAS,CAACD,kBAAkB,EAAE;UAC5C/C,KAAK,EAAE,MAAM;UACbqB,OAAO,EAAE;QACX,CAAC,CAAC;IACN;EACF,CAAC;EAED;EACA6B,CAAC,EAAE,SAAAA,CAAUvD,IAAI,EAAEC,KAAK,EAAEC,QAAQ,EAAE;IAClC,MAAMgD,KAAK,GAAGlD,IAAI,CAACmD,QAAQ,CAAC,CAAC;IAC7B,IAAIC,kBAAkB;IACtB,IAAIF,KAAK,KAAK,EAAE,EAAE;MAChBE,kBAAkB,GAAG/D,aAAa,CAACI,IAAI;IACzC,CAAC,MAAM,IAAIyD,KAAK,KAAK,CAAC,EAAE;MACtBE,kBAAkB,GAAG/D,aAAa,CAACG,QAAQ;IAC7C,CAAC,MAAM;MACL4D,kBAAkB,GAAGF,KAAK,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,GAAG,IAAI;IACpD;IAEA,QAAQjD,KAAK;MACX,KAAK,GAAG;MACR,KAAK,IAAI;QACP,OAAOC,QAAQ,CAACmD,SAAS,CAACD,kBAAkB,EAAE;UAC5C/C,KAAK,EAAE,aAAa;UACpBqB,OAAO,EAAE;QACX,CAAC,CAAC;MACJ,KAAK,KAAK;QACR,OAAOxB,QAAQ,CACZmD,SAAS,CAACD,kBAAkB,EAAE;UAC7B/C,KAAK,EAAE,aAAa;UACpBqB,OAAO,EAAE;QACX,CAAC,CAAC,CACD4B,WAAW,CAAC,CAAC;MAClB,KAAK,OAAO;QACV,OAAOpD,QAAQ,CAACmD,SAAS,CAACD,kBAAkB,EAAE;UAC5C/C,KAAK,EAAE,QAAQ;UACfqB,OAAO,EAAE;QACX,CAAC,CAAC;MACJ,KAAK,MAAM;MACX;QACE,OAAOxB,QAAQ,CAACmD,SAAS,CAACD,kBAAkB,EAAE;UAC5C/C,KAAK,EAAE,MAAM;UACbqB,OAAO,EAAE;QACX,CAAC,CAAC;IACN;EACF,CAAC;EAED;EACA8B,CAAC,EAAE,SAAAA,CAAUxD,IAAI,EAAEC,KAAK,EAAEC,QAAQ,EAAE;IAClC,MAAMgD,KAAK,GAAGlD,IAAI,CAACmD,QAAQ,CAAC,CAAC;IAC7B,IAAIC,kBAAkB;IACtB,IAAIF,KAAK,IAAI,EAAE,EAAE;MACfE,kBAAkB,GAAG/D,aAAa,CAACO,OAAO;IAC5C,CAAC,MAAM,IAAIsD,KAAK,IAAI,EAAE,EAAE;MACtBE,kBAAkB,GAAG/D,aAAa,CAACM,SAAS;IAC9C,CAAC,MAAM,IAAIuD,KAAK,IAAI,CAAC,EAAE;MACrBE,kBAAkB,GAAG/D,aAAa,CAACK,OAAO;IAC5C,CAAC,MAAM;MACL0D,kBAAkB,GAAG/D,aAAa,CAACQ,KAAK;IAC1C;IAEA,QAAQI,KAAK;MACX,KAAK,GAAG;MACR,KAAK,IAAI;MACT,KAAK,KAAK;QACR,OAAOC,QAAQ,CAACmD,SAAS,CAACD,kBAAkB,EAAE;UAC5C/C,KAAK,EAAE,aAAa;UACpBqB,OAAO,EAAE;QACX,CAAC,CAAC;MACJ,KAAK,OAAO;QACV,OAAOxB,QAAQ,CAACmD,SAAS,CAACD,kBAAkB,EAAE;UAC5C/C,KAAK,EAAE,QAAQ;UACfqB,OAAO,EAAE;QACX,CAAC,CAAC;MACJ,KAAK,MAAM;MACX;QACE,OAAOxB,QAAQ,CAACmD,SAAS,CAACD,kBAAkB,EAAE;UAC5C/C,KAAK,EAAE,MAAM;UACbqB,OAAO,EAAE;QACX,CAAC,CAAC;IACN;EACF,CAAC;EAED;EACA+B,CAAC,EAAE,SAAAA,CAAUzD,IAAI,EAAEC,KAAK,EAAEC,QAAQ,EAAE;IAClC,IAAID,KAAK,KAAK,IAAI,EAAE;MAClB,IAAIiD,KAAK,GAAGlD,IAAI,CAACmD,QAAQ,CAAC,CAAC,GAAG,EAAE;MAChC,IAAID,KAAK,KAAK,CAAC,EAAEA,KAAK,GAAG,EAAE;MAC3B,OAAOhD,QAAQ,CAACO,aAAa,CAACyC,KAAK,EAAE;QAAExC,IAAI,EAAE;MAAO,CAAC,CAAC;IACxD;IAEA,OAAOtB,eAAe,CAACqE,CAAC,CAACzD,IAAI,EAAEC,KAAK,CAAC;EACvC,CAAC;EAED;EACAyD,CAAC,EAAE,SAAAA,CAAU1D,IAAI,EAAEC,KAAK,EAAEC,QAAQ,EAAE;IAClC,IAAID,KAAK,KAAK,IAAI,EAAE;MAClB,OAAOC,QAAQ,CAACO,aAAa,CAACT,IAAI,CAACmD,QAAQ,CAAC,CAAC,EAAE;QAAEzC,IAAI,EAAE;MAAO,CAAC,CAAC;IAClE;IAEA,OAAOtB,eAAe,CAACsE,CAAC,CAAC1D,IAAI,EAAEC,KAAK,CAAC;EACvC,CAAC;EAED;EACA0D,CAAC,EAAE,SAAAA,CAAU3D,IAAI,EAAEC,KAAK,EAAEC,QAAQ,EAAE;IAClC,MAAMgD,KAAK,GAAGlD,IAAI,CAACmD,QAAQ,CAAC,CAAC,GAAG,EAAE;IAElC,IAAIlD,KAAK,KAAK,IAAI,EAAE;MAClB,OAAOC,QAAQ,CAACO,aAAa,CAACyC,KAAK,EAAE;QAAExC,IAAI,EAAE;MAAO,CAAC,CAAC;IACxD;IAEA,OAAOvB,eAAe,CAAC+D,KAAK,EAAEjD,KAAK,CAACe,MAAM,CAAC;EAC7C,CAAC;EAED;EACA4C,CAAC,EAAE,SAAAA,CAAU5D,IAAI,EAAEC,KAAK,EAAEC,QAAQ,EAAE;IAClC,IAAIgD,KAAK,GAAGlD,IAAI,CAACmD,QAAQ,CAAC,CAAC;IAC3B,IAAID,KAAK,KAAK,CAAC,EAAEA,KAAK,GAAG,EAAE;IAE3B,IAAIjD,KAAK,KAAK,IAAI,EAAE;MAClB,OAAOC,QAAQ,CAACO,aAAa,CAACyC,KAAK,EAAE;QAAExC,IAAI,EAAE;MAAO,CAAC,CAAC;IACxD;IAEA,OAAOvB,eAAe,CAAC+D,KAAK,EAAEjD,KAAK,CAACe,MAAM,CAAC;EAC7C,CAAC;EAED;EACA6C,CAAC,EAAE,SAAAA,CAAU7D,IAAI,EAAEC,KAAK,EAAEC,QAAQ,EAAE;IAClC,IAAID,KAAK,KAAK,IAAI,EAAE;MAClB,OAAOC,QAAQ,CAACO,aAAa,CAACT,IAAI,CAAC8D,UAAU,CAAC,CAAC,EAAE;QAAEpD,IAAI,EAAE;MAAS,CAAC,CAAC;IACtE;IAEA,OAAOtB,eAAe,CAACyE,CAAC,CAAC7D,IAAI,EAAEC,KAAK,CAAC;EACvC,CAAC;EAED;EACA8D,CAAC,EAAE,SAAAA,CAAU/D,IAAI,EAAEC,KAAK,EAAEC,QAAQ,EAAE;IAClC,IAAID,KAAK,KAAK,IAAI,EAAE;MAClB,OAAOC,QAAQ,CAACO,aAAa,CAACT,IAAI,CAACgE,UAAU,CAAC,CAAC,EAAE;QAAEtD,IAAI,EAAE;MAAS,CAAC,CAAC;IACtE;IAEA,OAAOtB,eAAe,CAAC2E,CAAC,CAAC/D,IAAI,EAAEC,KAAK,CAAC;EACvC,CAAC;EAED;EACAgE,CAAC,EAAE,SAAAA,CAAUjE,IAAI,EAAEC,KAAK,EAAE;IACxB,OAAOb,eAAe,CAAC6E,CAAC,CAACjE,IAAI,EAAEC,KAAK,CAAC;EACvC,CAAC;EAED;EACAiE,CAAC,EAAE,SAAAA,CAAUlE,IAAI,EAAEC,KAAK,EAAEkE,SAAS,EAAE;IACnC,MAAMC,cAAc,GAAGpE,IAAI,CAACqE,iBAAiB,CAAC,CAAC;IAE/C,IAAID,cAAc,KAAK,CAAC,EAAE;MACxB,OAAO,GAAG;IACZ;IAEA,QAAQnE,KAAK;MACX;MACA,KAAK,GAAG;QACN,OAAOqE,iCAAiC,CAACF,cAAc,CAAC;;MAE1D;MACA;MACA;MACA,KAAK,MAAM;MACX,KAAK,IAAI;QAAE;QACT,OAAOG,cAAc,CAACH,cAAc,CAAC;;MAEvC;MACA;MACA;MACA,KAAK,OAAO;MACZ,KAAK,KAAK,CAAC,CAAC;MACZ;QACE,OAAOG,cAAc,CAACH,cAAc,EAAE,GAAG,CAAC;IAC9C;EACF,CAAC;EAED;EACAI,CAAC,EAAE,SAAAA,CAAUxE,IAAI,EAAEC,KAAK,EAAEkE,SAAS,EAAE;IACnC,MAAMC,cAAc,GAAGpE,IAAI,CAACqE,iBAAiB,CAAC,CAAC;IAE/C,QAAQpE,KAAK;MACX;MACA,KAAK,GAAG;QACN,OAAOqE,iCAAiC,CAACF,cAAc,CAAC;;MAE1D;MACA;MACA;MACA,KAAK,MAAM;MACX,KAAK,IAAI;QAAE;QACT,OAAOG,cAAc,CAACH,cAAc,CAAC;;MAEvC;MACA;MACA;MACA,KAAK,OAAO;MACZ,KAAK,KAAK,CAAC,CAAC;MACZ;QACE,OAAOG,cAAc,CAACH,cAAc,EAAE,GAAG,CAAC;IAC9C;EACF,CAAC;EAED;EACAK,CAAC,EAAE,SAAAA,CAAUzE,IAAI,EAAEC,KAAK,EAAEkE,SAAS,EAAE;IACnC,MAAMC,cAAc,GAAGpE,IAAI,CAACqE,iBAAiB,CAAC,CAAC;IAE/C,QAAQpE,KAAK;MACX;MACA,KAAK,GAAG;MACR,KAAK,IAAI;MACT,KAAK,KAAK;QACR,OAAO,KAAK,GAAGyE,mBAAmB,CAACN,cAAc,EAAE,GAAG,CAAC;MACzD;MACA,KAAK,MAAM;MACX;QACE,OAAO,KAAK,GAAGG,cAAc,CAACH,cAAc,EAAE,GAAG,CAAC;IACtD;EACF,CAAC;EAED;EACAO,CAAC,EAAE,SAAAA,CAAU3E,IAAI,EAAEC,KAAK,EAAEkE,SAAS,EAAE;IACnC,MAAMC,cAAc,GAAGpE,IAAI,CAACqE,iBAAiB,CAAC,CAAC;IAE/C,QAAQpE,KAAK;MACX;MACA,KAAK,GAAG;MACR,KAAK,IAAI;MACT,KAAK,KAAK;QACR,OAAO,KAAK,GAAGyE,mBAAmB,CAACN,cAAc,EAAE,GAAG,CAAC;MACzD;MACA,KAAK,MAAM;MACX;QACE,OAAO,KAAK,GAAGG,cAAc,CAACH,cAAc,EAAE,GAAG,CAAC;IACtD;EACF,CAAC;EAED;EACAQ,CAAC,EAAE,SAAAA,CAAU5E,IAAI,EAAEC,KAAK,EAAEkE,SAAS,EAAE;IACnC,MAAMU,SAAS,GAAGvD,IAAI,CAACwD,KAAK,CAAC,CAAC9E,IAAI,GAAG,IAAI,CAAC;IAC1C,OAAOb,eAAe,CAAC0F,SAAS,EAAE5E,KAAK,CAACe,MAAM,CAAC;EACjD,CAAC;EAED;EACA+D,CAAC,EAAE,SAAAA,CAAU/E,IAAI,EAAEC,KAAK,EAAEkE,SAAS,EAAE;IACnC,OAAOhF,eAAe,CAAC,CAACa,IAAI,EAAEC,KAAK,CAACe,MAAM,CAAC;EAC7C;AACF,CAAC;AAED,SAAS0D,mBAAmBA,CAACM,MAAM,EAAkB;EAAA,IAAhBC,SAAS,GAAAC,SAAA,CAAAlE,MAAA,QAAAkE,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAG,EAAE;EACjD,MAAME,IAAI,GAAGJ,MAAM,GAAG,CAAC,GAAG,GAAG,GAAG,GAAG;EACnC,MAAMK,SAAS,GAAG/D,IAAI,CAACgE,GAAG,CAACN,MAAM,CAAC;EAClC,MAAM9B,KAAK,GAAG5B,IAAI,CAACwD,KAAK,CAACO,SAAS,GAAG,EAAE,CAAC;EACxC,MAAME,OAAO,GAAGF,SAAS,GAAG,EAAE;EAC9B,IAAIE,OAAO,KAAK,CAAC,EAAE;IACjB,OAAOH,IAAI,GAAG3D,MAAM,CAACyB,KAAK,CAAC;EAC7B;EACA,OAAOkC,IAAI,GAAG3D,MAAM,CAACyB,KAAK,CAAC,GAAG+B,SAAS,GAAG9F,eAAe,CAACoG,OAAO,EAAE,CAAC,CAAC;AACvE;AAEA,SAASjB,iCAAiCA,CAACU,MAAM,EAAEC,SAAS,EAAE;EAC5D,IAAID,MAAM,GAAG,EAAE,KAAK,CAAC,EAAE;IACrB,MAAMI,IAAI,GAAGJ,MAAM,GAAG,CAAC,GAAG,GAAG,GAAG,GAAG;IACnC,OAAOI,IAAI,GAAGjG,eAAe,CAACmC,IAAI,CAACgE,GAAG,CAACN,MAAM,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC;EACzD;EACA,OAAOT,cAAc,CAACS,MAAM,EAAEC,SAAS,CAAC;AAC1C;AAEA,SAASV,cAAcA,CAACS,MAAM,EAAkB;EAAA,IAAhBC,SAAS,GAAAC,SAAA,CAAAlE,MAAA,QAAAkE,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAG,EAAE;EAC5C,MAAME,IAAI,GAAGJ,MAAM,GAAG,CAAC,GAAG,GAAG,GAAG,GAAG;EACnC,MAAMK,SAAS,GAAG/D,IAAI,CAACgE,GAAG,CAACN,MAAM,CAAC;EAClC,MAAM9B,KAAK,GAAG/D,eAAe,CAACmC,IAAI,CAACwD,KAAK,CAACO,SAAS,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC;EAC5D,MAAME,OAAO,GAAGpG,eAAe,CAACkG,SAAS,GAAG,EAAE,EAAE,CAAC,CAAC;EAClD,OAAOD,IAAI,GAAGlC,KAAK,GAAG+B,SAAS,GAAGM,OAAO;AAC3C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}