{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\\\u30C9\\u30AD\\u30E5\\u30E1\\u30F3\\u30C8\\\\Projects\\\\StudyHub\\\\studyhub-app\\\\src\\\\pages\\\\Resources.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Container, Typography, Grid, Card, CardContent, CardActions, Button, Box, Chip, TextField, InputAdornment, FormControl, InputLabel, Select, MenuItem, Fab, Dialog, DialogTitle, DialogContent, DialogActions } from '@mui/material';\nimport { Search, Add, PictureAsPdf, VideoLibrary, Article, Download, Favorite, FavoriteBorder } from '@mui/icons-material';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Resources = () => {\n  _s();\n  const [searchTerm, setSearchTerm] = useState('');\n  const [filterSubject, setFilterSubject] = useState('');\n  const [filterType, setFilterType] = useState('');\n  const [open, setOpen] = useState(false);\n\n  // Mock data - replace with actual data from Firebase\n  const resources = [{\n    id: 1,\n    title: 'React Hooks Complete Guide',\n    type: 'PDF',\n    subject: 'Web Development',\n    description: 'Comprehensive guide covering all React hooks with examples',\n    author: 'John Doe',\n    uploadDate: '2024-01-15',\n    downloads: 245,\n    isFavorite: true,\n    fileSize: '2.5 MB'\n  }, {\n    id: 2,\n    title: 'Data Structures Video Series',\n    type: 'Video',\n    subject: 'Computer Science',\n    description: 'Complete video series on data structures and algorithms',\n    author: 'Jane Smith',\n    uploadDate: '2024-01-10',\n    downloads: 189,\n    isFavorite: false,\n    fileSize: '1.2 GB'\n  }, {\n    id: 3,\n    title: 'Machine Learning Cheat Sheet',\n    type: 'Document',\n    subject: 'Data Science',\n    description: 'Quick reference for common ML algorithms and formulas',\n    author: 'Dr. Wilson',\n    uploadDate: '2024-01-08',\n    downloads: 156,\n    isFavorite: true,\n    fileSize: '850 KB'\n  }, {\n    id: 4,\n    title: 'JavaScript ES6+ Features',\n    type: 'PDF',\n    subject: 'Programming',\n    description: 'Modern JavaScript features with practical examples',\n    author: 'Alex Johnson',\n    uploadDate: '2024-01-05',\n    downloads: 203,\n    isFavorite: false,\n    fileSize: '1.8 MB'\n  }, {\n    id: 5,\n    title: 'Database Design Principles',\n    type: 'Document',\n    subject: 'Database',\n    description: 'Best practices for designing efficient databases',\n    author: 'Sarah Brown',\n    uploadDate: '2024-01-03',\n    downloads: 134,\n    isFavorite: false,\n    fileSize: '1.1 MB'\n  }, {\n    id: 6,\n    title: 'Python Programming Tutorial',\n    type: 'Video',\n    subject: 'Programming',\n    description: 'Beginner-friendly Python programming course',\n    author: 'Mike Davis',\n    uploadDate: '2024-01-01',\n    downloads: 298,\n    isFavorite: true,\n    fileSize: '2.8 GB'\n  }];\n  const getTypeIcon = type => {\n    switch (type) {\n      case 'PDF':\n        return /*#__PURE__*/_jsxDEV(PictureAsPdf, {\n          color: \"error\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 120,\n          columnNumber: 16\n        }, this);\n      case 'Video':\n        return /*#__PURE__*/_jsxDEV(VideoLibrary, {\n          color: \"primary\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 122,\n          columnNumber: 16\n        }, this);\n      case 'Document':\n        return /*#__PURE__*/_jsxDEV(Article, {\n          color: \"success\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 124,\n          columnNumber: 16\n        }, this);\n      default:\n        return /*#__PURE__*/_jsxDEV(Article, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 126,\n          columnNumber: 16\n        }, this);\n    }\n  };\n  const getTypeColor = type => {\n    switch (type) {\n      case 'PDF':\n        return 'error';\n      case 'Video':\n        return 'primary';\n      case 'Document':\n        return 'success';\n      default:\n        return 'default';\n    }\n  };\n  const filteredResources = resources.filter(resource => {\n    const matchesSearch = resource.title.toLowerCase().includes(searchTerm.toLowerCase()) || resource.description.toLowerCase().includes(searchTerm.toLowerCase());\n    const matchesSubject = !filterSubject || resource.subject === filterSubject;\n    const matchesType = !filterType || resource.type === filterType;\n    return matchesSearch && matchesSubject && matchesType;\n  });\n  const subjects = [...new Set(resources.map(r => r.subject))];\n  const types = [...new Set(resources.map(r => r.type))];\n  return /*#__PURE__*/_jsxDEV(Container, {\n    maxWidth: \"lg\",\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center',\n        mb: 4\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h4\",\n          component: \"h1\",\n          gutterBottom: true,\n          children: \"Study Resources\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 159,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body1\",\n          color: \"text.secondary\",\n          children: \"Access and share educational materials with the community\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 162,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 158,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 157,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        mb: 4\n      },\n      children: /*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: 2,\n        alignItems: \"center\",\n        children: [/*#__PURE__*/_jsxDEV(Grid, {\n          size: {\n            xs: 12,\n            md: 6\n          },\n          children: /*#__PURE__*/_jsxDEV(TextField, {\n            fullWidth: true,\n            placeholder: \"Search resources...\",\n            value: searchTerm,\n            onChange: e => setSearchTerm(e.target.value),\n            InputProps: {\n              startAdornment: /*#__PURE__*/_jsxDEV(InputAdornment, {\n                position: \"start\",\n                children: /*#__PURE__*/_jsxDEV(Search, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 180,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 179,\n                columnNumber: 19\n              }, this)\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 172,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 171,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          size: {\n            xs: 12,\n            sm: 6,\n            md: 3\n          },\n          children: /*#__PURE__*/_jsxDEV(FormControl, {\n            fullWidth: true,\n            children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n              children: \"Subject\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 188,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Select, {\n              value: filterSubject,\n              label: \"Subject\",\n              onChange: e => setFilterSubject(e.target.value),\n              children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"\",\n                children: \"All Subjects\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 194,\n                columnNumber: 17\n              }, this), subjects.map(subject => /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: subject,\n                children: subject\n              }, subject, false, {\n                fileName: _jsxFileName,\n                lineNumber: 196,\n                columnNumber: 19\n              }, this))]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 189,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 187,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 186,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          size: {\n            xs: 12,\n            sm: 6,\n            md: 3\n          },\n          children: /*#__PURE__*/_jsxDEV(FormControl, {\n            fullWidth: true,\n            children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n              children: \"Type\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 205,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Select, {\n              value: filterType,\n              label: \"Type\",\n              onChange: e => setFilterType(e.target.value),\n              children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"\",\n                children: \"All Types\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 211,\n                columnNumber: 17\n              }, this), types.map(type => /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: type,\n                children: type\n              }, type, false, {\n                fileName: _jsxFileName,\n                lineNumber: 213,\n                columnNumber: 19\n              }, this))]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 206,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 204,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 203,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 170,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 169,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 3,\n      children: filteredResources.map(resource => /*#__PURE__*/_jsxDEV(Grid, {\n        size: {\n          xs: 12,\n          md: 6,\n          lg: 4\n        },\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          sx: {\n            height: '100%',\n            display: 'flex',\n            flexDirection: 'column',\n            transition: 'transform 0.2s',\n            '&:hover': {\n              transform: 'translateY(-4px)',\n              boxShadow: 3\n            }\n          },\n          children: [/*#__PURE__*/_jsxDEV(CardContent, {\n            sx: {\n              flexGrow: 1\n            },\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                justifyContent: 'space-between',\n                alignItems: 'flex-start',\n                mb: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: 1\n                },\n                children: [getTypeIcon(resource.type), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h6\",\n                  component: \"h3\",\n                  children: resource.title\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 243,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 241,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                size: \"small\",\n                sx: {\n                  minWidth: 'auto',\n                  p: 0.5\n                },\n                children: resource.isFavorite ? /*#__PURE__*/_jsxDEV(Favorite, {\n                  color: \"error\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 248,\n                  columnNumber: 44\n                }, this) : /*#__PURE__*/_jsxDEV(FavoriteBorder, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 248,\n                  columnNumber: 73\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 247,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 240,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                gap: 1,\n                mb: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(Chip, {\n                label: resource.type,\n                size: \"small\",\n                color: getTypeColor(resource.type),\n                variant: \"outlined\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 253,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                label: resource.subject,\n                size: \"small\",\n                color: \"primary\",\n                variant: \"outlined\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 259,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 252,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"text.secondary\",\n              sx: {\n                mb: 2\n              },\n              children: resource.description\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 267,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              color: \"text.secondary\",\n              display: \"block\",\n              children: [\"By \", resource.author, \" \\u2022 \", resource.uploadDate]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 271,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              color: \"text.secondary\",\n              display: \"block\",\n              children: [resource.downloads, \" downloads \\u2022 \", resource.fileSize]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 274,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 239,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(CardActions, {\n            children: [/*#__PURE__*/_jsxDEV(Button, {\n              size: \"small\",\n              startIcon: /*#__PURE__*/_jsxDEV(Download, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 280,\n                columnNumber: 49\n              }, this),\n              children: \"Download\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 280,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              size: \"small\",\n              children: \"Preview\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 283,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 279,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 227,\n          columnNumber: 13\n        }, this)\n      }, resource.id, false, {\n        fileName: _jsxFileName,\n        lineNumber: 226,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 224,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Fab, {\n      color: \"primary\",\n      \"aria-label\": \"upload resource\",\n      sx: {\n        position: 'fixed',\n        bottom: 16,\n        right: 16\n      },\n      onClick: () => setOpen(true),\n      children: /*#__PURE__*/_jsxDEV(Add, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 299,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 293,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: open,\n      onClose: () => setOpen(false),\n      maxWidth: \"sm\",\n      fullWidth: true,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: \"Upload New Resource\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 304,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          color: \"text.secondary\",\n          sx: {\n            mb: 2\n          },\n          children: \"Share your study materials with the community\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 306,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          children: \"Upload form will be implemented here...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 310,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 305,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: () => setOpen(false),\n          children: \"Cancel\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 315,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"contained\",\n          children: \"Upload\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 316,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 314,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 303,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 156,\n    columnNumber: 5\n  }, this);\n};\n_s(Resources, \"7BQTKu9FBL+kJrjQNj+QTJD4GxQ=\");\n_c = Resources;\nexport default Resources;\nvar _c;\n$RefreshReg$(_c, \"Resources\");", "map": {"version": 3, "names": ["React", "useState", "Container", "Typography", "Grid", "Card", "<PERSON><PERSON><PERSON><PERSON>", "CardActions", "<PERSON><PERSON>", "Box", "Chip", "TextField", "InputAdornment", "FormControl", "InputLabel", "Select", "MenuItem", "Fab", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "Search", "Add", "PictureAsPdf", "VideoLibrary", "Article", "Download", "Favorite", "FavoriteBorder", "jsxDEV", "_jsxDEV", "Resources", "_s", "searchTerm", "setSearchTerm", "filterSubject", "setFilterSubject", "filterType", "setFilterType", "open", "<PERSON><PERSON><PERSON>", "resources", "id", "title", "type", "subject", "description", "author", "uploadDate", "downloads", "isFavorite", "fileSize", "getTypeIcon", "color", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "getTypeColor", "filteredResources", "filter", "resource", "matchesSearch", "toLowerCase", "includes", "matchesSubject", "matchesType", "subjects", "Set", "map", "r", "types", "max<PERSON><PERSON><PERSON>", "children", "sx", "display", "justifyContent", "alignItems", "mb", "variant", "component", "gutterBottom", "container", "spacing", "size", "xs", "md", "fullWidth", "placeholder", "value", "onChange", "e", "target", "InputProps", "startAdornment", "position", "sm", "label", "lg", "height", "flexDirection", "transition", "transform", "boxShadow", "flexGrow", "gap", "min<PERSON><PERSON><PERSON>", "p", "startIcon", "bottom", "right", "onClick", "onClose", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/ドキュメント/Projects/StudyHub/studyhub-app/src/pages/Resources.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport {\n  Container,\n  <PERSON><PERSON><PERSON>,\n  <PERSON>rid,\n  Card,\n  Card<PERSON>ontent,\n  CardActions,\n  Button,\n  Box,\n  Chip,\n  TextField,\n  InputAdornment,\n  FormControl,\n  InputLabel,\n  Select,\n  MenuItem,\n  Fab,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n} from '@mui/material';\nimport {\n  Search,\n  Add,\n  PictureAsPdf,\n  VideoLibrary,\n  Article,\n  Download,\n  Favorite,\n  FavoriteBorder,\n} from '@mui/icons-material';\n\nconst Resources: React.FC = () => {\n  const [searchTerm, setSearchTerm] = useState('');\n  const [filterSubject, setFilterSubject] = useState('');\n  const [filterType, setFilterType] = useState('');\n  const [open, setOpen] = useState(false);\n\n  // Mock data - replace with actual data from Firebase\n  const resources = [\n    {\n      id: 1,\n      title: 'React Hooks Complete Guide',\n      type: 'PDF',\n      subject: 'Web Development',\n      description: 'Comprehensive guide covering all React hooks with examples',\n      author: '<PERSON>',\n      uploadDate: '2024-01-15',\n      downloads: 245,\n      isFavorite: true,\n      fileSize: '2.5 MB',\n    },\n    {\n      id: 2,\n      title: 'Data Structures Video Series',\n      type: 'Video',\n      subject: 'Computer Science',\n      description: 'Complete video series on data structures and algorithms',\n      author: 'Jane <PERSON>',\n      uploadDate: '2024-01-10',\n      downloads: 189,\n      isFavorite: false,\n      fileSize: '1.2 GB',\n    },\n    {\n      id: 3,\n      title: 'Machine Learning Cheat Sheet',\n      type: 'Document',\n      subject: 'Data Science',\n      description: 'Quick reference for common ML algorithms and formulas',\n      author: 'Dr. Wilson',\n      uploadDate: '2024-01-08',\n      downloads: 156,\n      isFavorite: true,\n      fileSize: '850 KB',\n    },\n    {\n      id: 4,\n      title: 'JavaScript ES6+ Features',\n      type: 'PDF',\n      subject: 'Programming',\n      description: 'Modern JavaScript features with practical examples',\n      author: 'Alex Johnson',\n      uploadDate: '2024-01-05',\n      downloads: 203,\n      isFavorite: false,\n      fileSize: '1.8 MB',\n    },\n    {\n      id: 5,\n      title: 'Database Design Principles',\n      type: 'Document',\n      subject: 'Database',\n      description: 'Best practices for designing efficient databases',\n      author: 'Sarah Brown',\n      uploadDate: '2024-01-03',\n      downloads: 134,\n      isFavorite: false,\n      fileSize: '1.1 MB',\n    },\n    {\n      id: 6,\n      title: 'Python Programming Tutorial',\n      type: 'Video',\n      subject: 'Programming',\n      description: 'Beginner-friendly Python programming course',\n      author: 'Mike Davis',\n      uploadDate: '2024-01-01',\n      downloads: 298,\n      isFavorite: true,\n      fileSize: '2.8 GB',\n    },\n  ];\n\n  const getTypeIcon = (type: string) => {\n    switch (type) {\n      case 'PDF':\n        return <PictureAsPdf color=\"error\" />;\n      case 'Video':\n        return <VideoLibrary color=\"primary\" />;\n      case 'Document':\n        return <Article color=\"success\" />;\n      default:\n        return <Article />;\n    }\n  };\n\n  const getTypeColor = (type: string) => {\n    switch (type) {\n      case 'PDF':\n        return 'error';\n      case 'Video':\n        return 'primary';\n      case 'Document':\n        return 'success';\n      default:\n        return 'default';\n    }\n  };\n\n  const filteredResources = resources.filter((resource) => {\n    const matchesSearch = resource.title.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                         resource.description.toLowerCase().includes(searchTerm.toLowerCase());\n    const matchesSubject = !filterSubject || resource.subject === filterSubject;\n    const matchesType = !filterType || resource.type === filterType;\n    \n    return matchesSearch && matchesSubject && matchesType;\n  });\n\n  const subjects = [...new Set(resources.map(r => r.subject))];\n  const types = [...new Set(resources.map(r => r.type))];\n\n  return (\n    <Container maxWidth=\"lg\">\n      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 4 }}>\n        <div>\n          <Typography variant=\"h4\" component=\"h1\" gutterBottom>\n            Study Resources\n          </Typography>\n          <Typography variant=\"body1\" color=\"text.secondary\">\n            Access and share educational materials with the community\n          </Typography>\n        </div>\n      </Box>\n\n      {/* Search and Filters */}\n      <Box sx={{ mb: 4 }}>\n        <Grid container spacing={2} alignItems=\"center\">\n          <Grid size={{ xs: 12, md: 6 }}>\n            <TextField\n              fullWidth\n              placeholder=\"Search resources...\"\n              value={searchTerm}\n              onChange={(e) => setSearchTerm(e.target.value)}\n              InputProps={{\n                startAdornment: (\n                  <InputAdornment position=\"start\">\n                    <Search />\n                  </InputAdornment>\n                ),\n              }}\n            />\n          </Grid>\n          <Grid size={{ xs: 12, sm: 6, md: 3 }}>\n            <FormControl fullWidth>\n              <InputLabel>Subject</InputLabel>\n              <Select\n                value={filterSubject}\n                label=\"Subject\"\n                onChange={(e) => setFilterSubject(e.target.value)}\n              >\n                <MenuItem value=\"\">All Subjects</MenuItem>\n                {subjects.map((subject) => (\n                  <MenuItem key={subject} value={subject}>\n                    {subject}\n                  </MenuItem>\n                ))}\n              </Select>\n            </FormControl>\n          </Grid>\n          <Grid size={{ xs: 12, sm: 6, md: 3 }}>\n            <FormControl fullWidth>\n              <InputLabel>Type</InputLabel>\n              <Select\n                value={filterType}\n                label=\"Type\"\n                onChange={(e) => setFilterType(e.target.value)}\n              >\n                <MenuItem value=\"\">All Types</MenuItem>\n                {types.map((type) => (\n                  <MenuItem key={type} value={type}>\n                    {type}\n                  </MenuItem>\n                ))}\n              </Select>\n            </FormControl>\n          </Grid>\n        </Grid>\n      </Box>\n\n      {/* Resources Grid */}\n      <Grid container spacing={3}>\n        {filteredResources.map((resource) => (\n          <Grid size={{ xs: 12, md: 6, lg: 4 }} key={resource.id}>\n            <Card\n              sx={{\n                height: '100%',\n                display: 'flex',\n                flexDirection: 'column',\n                transition: 'transform 0.2s',\n                '&:hover': {\n                  transform: 'translateY(-4px)',\n                  boxShadow: 3,\n                },\n              }}\n            >\n              <CardContent sx={{ flexGrow: 1 }}>\n                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>\n                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\n                    {getTypeIcon(resource.type)}\n                    <Typography variant=\"h6\" component=\"h3\">\n                      {resource.title}\n                    </Typography>\n                  </Box>\n                  <Button size=\"small\" sx={{ minWidth: 'auto', p: 0.5 }}>\n                    {resource.isFavorite ? <Favorite color=\"error\" /> : <FavoriteBorder />}\n                  </Button>\n                </Box>\n\n                <Box sx={{ display: 'flex', gap: 1, mb: 2 }}>\n                  <Chip\n                    label={resource.type}\n                    size=\"small\"\n                    color={getTypeColor(resource.type) as any}\n                    variant=\"outlined\"\n                  />\n                  <Chip\n                    label={resource.subject}\n                    size=\"small\"\n                    color=\"primary\"\n                    variant=\"outlined\"\n                  />\n                </Box>\n\n                <Typography variant=\"body2\" color=\"text.secondary\" sx={{ mb: 2 }}>\n                  {resource.description}\n                </Typography>\n\n                <Typography variant=\"caption\" color=\"text.secondary\" display=\"block\">\n                  By {resource.author} • {resource.uploadDate}\n                </Typography>\n                <Typography variant=\"caption\" color=\"text.secondary\" display=\"block\">\n                  {resource.downloads} downloads • {resource.fileSize}\n                </Typography>\n              </CardContent>\n\n              <CardActions>\n                <Button size=\"small\" startIcon={<Download />}>\n                  Download\n                </Button>\n                <Button size=\"small\">\n                  Preview\n                </Button>\n              </CardActions>\n            </Card>\n          </Grid>\n        ))}\n      </Grid>\n\n      {/* Floating Action Button */}\n      <Fab\n        color=\"primary\"\n        aria-label=\"upload resource\"\n        sx={{ position: 'fixed', bottom: 16, right: 16 }}\n        onClick={() => setOpen(true)}\n      >\n        <Add />\n      </Fab>\n\n      {/* Upload Resource Dialog */}\n      <Dialog open={open} onClose={() => setOpen(false)} maxWidth=\"sm\" fullWidth>\n        <DialogTitle>Upload New Resource</DialogTitle>\n        <DialogContent>\n          <Typography variant=\"body2\" color=\"text.secondary\" sx={{ mb: 2 }}>\n            Share your study materials with the community\n          </Typography>\n          {/* TODO: Add upload form */}\n          <Typography variant=\"body2\">\n            Upload form will be implemented here...\n          </Typography>\n        </DialogContent>\n        <DialogActions>\n          <Button onClick={() => setOpen(false)}>Cancel</Button>\n          <Button variant=\"contained\">Upload</Button>\n        </DialogActions>\n      </Dialog>\n    </Container>\n  );\n};\n\nexport default Resources;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SACEC,SAAS,EACTC,UAAU,EACVC,IAAI,EACJC,IAAI,EACJC,WAAW,EACXC,WAAW,EACXC,MAAM,EACNC,GAAG,EACHC,IAAI,EACJC,SAAS,EACTC,cAAc,EACdC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,QAAQ,EACRC,GAAG,EACHC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,QACR,eAAe;AACtB,SACEC,MAAM,EACNC,GAAG,EACHC,YAAY,EACZC,YAAY,EACZC,OAAO,EACPC,QAAQ,EACRC,QAAQ,EACRC,cAAc,QACT,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE7B,MAAMC,SAAmB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAChC,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGlC,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACmC,aAAa,EAAEC,gBAAgB,CAAC,GAAGpC,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACqC,UAAU,EAAEC,aAAa,CAAC,GAAGtC,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACuC,IAAI,EAAEC,OAAO,CAAC,GAAGxC,QAAQ,CAAC,KAAK,CAAC;;EAEvC;EACA,MAAMyC,SAAS,GAAG,CAChB;IACEC,EAAE,EAAE,CAAC;IACLC,KAAK,EAAE,4BAA4B;IACnCC,IAAI,EAAE,KAAK;IACXC,OAAO,EAAE,iBAAiB;IAC1BC,WAAW,EAAE,4DAA4D;IACzEC,MAAM,EAAE,UAAU;IAClBC,UAAU,EAAE,YAAY;IACxBC,SAAS,EAAE,GAAG;IACdC,UAAU,EAAE,IAAI;IAChBC,QAAQ,EAAE;EACZ,CAAC,EACD;IACET,EAAE,EAAE,CAAC;IACLC,KAAK,EAAE,8BAA8B;IACrCC,IAAI,EAAE,OAAO;IACbC,OAAO,EAAE,kBAAkB;IAC3BC,WAAW,EAAE,yDAAyD;IACtEC,MAAM,EAAE,YAAY;IACpBC,UAAU,EAAE,YAAY;IACxBC,SAAS,EAAE,GAAG;IACdC,UAAU,EAAE,KAAK;IACjBC,QAAQ,EAAE;EACZ,CAAC,EACD;IACET,EAAE,EAAE,CAAC;IACLC,KAAK,EAAE,8BAA8B;IACrCC,IAAI,EAAE,UAAU;IAChBC,OAAO,EAAE,cAAc;IACvBC,WAAW,EAAE,uDAAuD;IACpEC,MAAM,EAAE,YAAY;IACpBC,UAAU,EAAE,YAAY;IACxBC,SAAS,EAAE,GAAG;IACdC,UAAU,EAAE,IAAI;IAChBC,QAAQ,EAAE;EACZ,CAAC,EACD;IACET,EAAE,EAAE,CAAC;IACLC,KAAK,EAAE,0BAA0B;IACjCC,IAAI,EAAE,KAAK;IACXC,OAAO,EAAE,aAAa;IACtBC,WAAW,EAAE,oDAAoD;IACjEC,MAAM,EAAE,cAAc;IACtBC,UAAU,EAAE,YAAY;IACxBC,SAAS,EAAE,GAAG;IACdC,UAAU,EAAE,KAAK;IACjBC,QAAQ,EAAE;EACZ,CAAC,EACD;IACET,EAAE,EAAE,CAAC;IACLC,KAAK,EAAE,4BAA4B;IACnCC,IAAI,EAAE,UAAU;IAChBC,OAAO,EAAE,UAAU;IACnBC,WAAW,EAAE,kDAAkD;IAC/DC,MAAM,EAAE,aAAa;IACrBC,UAAU,EAAE,YAAY;IACxBC,SAAS,EAAE,GAAG;IACdC,UAAU,EAAE,KAAK;IACjBC,QAAQ,EAAE;EACZ,CAAC,EACD;IACET,EAAE,EAAE,CAAC;IACLC,KAAK,EAAE,6BAA6B;IACpCC,IAAI,EAAE,OAAO;IACbC,OAAO,EAAE,aAAa;IACtBC,WAAW,EAAE,6CAA6C;IAC1DC,MAAM,EAAE,YAAY;IACpBC,UAAU,EAAE,YAAY;IACxBC,SAAS,EAAE,GAAG;IACdC,UAAU,EAAE,IAAI;IAChBC,QAAQ,EAAE;EACZ,CAAC,CACF;EAED,MAAMC,WAAW,GAAIR,IAAY,IAAK;IACpC,QAAQA,IAAI;MACV,KAAK,KAAK;QACR,oBAAOd,OAAA,CAACP,YAAY;UAAC8B,KAAK,EAAC;QAAO;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACvC,KAAK,OAAO;QACV,oBAAO3B,OAAA,CAACN,YAAY;UAAC6B,KAAK,EAAC;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACzC,KAAK,UAAU;QACb,oBAAO3B,OAAA,CAACL,OAAO;UAAC4B,KAAK,EAAC;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACpC;QACE,oBAAO3B,OAAA,CAACL,OAAO;UAAA6B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;IACtB;EACF,CAAC;EAED,MAAMC,YAAY,GAAId,IAAY,IAAK;IACrC,QAAQA,IAAI;MACV,KAAK,KAAK;QACR,OAAO,OAAO;MAChB,KAAK,OAAO;QACV,OAAO,SAAS;MAClB,KAAK,UAAU;QACb,OAAO,SAAS;MAClB;QACE,OAAO,SAAS;IACpB;EACF,CAAC;EAED,MAAMe,iBAAiB,GAAGlB,SAAS,CAACmB,MAAM,CAAEC,QAAQ,IAAK;IACvD,MAAMC,aAAa,GAAGD,QAAQ,CAAClB,KAAK,CAACoB,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC/B,UAAU,CAAC8B,WAAW,CAAC,CAAC,CAAC,IAChEF,QAAQ,CAACf,WAAW,CAACiB,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC/B,UAAU,CAAC8B,WAAW,CAAC,CAAC,CAAC;IAC1F,MAAME,cAAc,GAAG,CAAC9B,aAAa,IAAI0B,QAAQ,CAAChB,OAAO,KAAKV,aAAa;IAC3E,MAAM+B,WAAW,GAAG,CAAC7B,UAAU,IAAIwB,QAAQ,CAACjB,IAAI,KAAKP,UAAU;IAE/D,OAAOyB,aAAa,IAAIG,cAAc,IAAIC,WAAW;EACvD,CAAC,CAAC;EAEF,MAAMC,QAAQ,GAAG,CAAC,GAAG,IAAIC,GAAG,CAAC3B,SAAS,CAAC4B,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACzB,OAAO,CAAC,CAAC,CAAC;EAC5D,MAAM0B,KAAK,GAAG,CAAC,GAAG,IAAIH,GAAG,CAAC3B,SAAS,CAAC4B,GAAG,CAACC,CAAC,IAAIA,CAAC,CAAC1B,IAAI,CAAC,CAAC,CAAC;EAEtD,oBACEd,OAAA,CAAC7B,SAAS;IAACuE,QAAQ,EAAC,IAAI;IAAAC,QAAA,gBACtB3C,OAAA,CAACtB,GAAG;MAACkE,EAAE,EAAE;QAAEC,OAAO,EAAE,MAAM;QAAEC,cAAc,EAAE,eAAe;QAAEC,UAAU,EAAE,QAAQ;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAL,QAAA,eACzF3C,OAAA;QAAA2C,QAAA,gBACE3C,OAAA,CAAC5B,UAAU;UAAC6E,OAAO,EAAC,IAAI;UAACC,SAAS,EAAC,IAAI;UAACC,YAAY;UAAAR,QAAA,EAAC;QAErD;UAAAnB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACb3B,OAAA,CAAC5B,UAAU;UAAC6E,OAAO,EAAC,OAAO;UAAC1B,KAAK,EAAC,gBAAgB;UAAAoB,QAAA,EAAC;QAEnD;UAAAnB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN3B,OAAA,CAACtB,GAAG;MAACkE,EAAE,EAAE;QAAEI,EAAE,EAAE;MAAE,CAAE;MAAAL,QAAA,eACjB3C,OAAA,CAAC3B,IAAI;QAAC+E,SAAS;QAACC,OAAO,EAAE,CAAE;QAACN,UAAU,EAAC,QAAQ;QAAAJ,QAAA,gBAC7C3C,OAAA,CAAC3B,IAAI;UAACiF,IAAI,EAAE;YAAEC,EAAE,EAAE,EAAE;YAAEC,EAAE,EAAE;UAAE,CAAE;UAAAb,QAAA,eAC5B3C,OAAA,CAACpB,SAAS;YACR6E,SAAS;YACTC,WAAW,EAAC,qBAAqB;YACjCC,KAAK,EAAExD,UAAW;YAClByD,QAAQ,EAAGC,CAAC,IAAKzD,aAAa,CAACyD,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;YAC/CI,UAAU,EAAE;cACVC,cAAc,eACZhE,OAAA,CAACnB,cAAc;gBAACoF,QAAQ,EAAC,OAAO;gBAAAtB,QAAA,eAC9B3C,OAAA,CAACT,MAAM;kBAAAiC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI;YAEpB;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eACP3B,OAAA,CAAC3B,IAAI;UAACiF,IAAI,EAAE;YAAEC,EAAE,EAAE,EAAE;YAAEW,EAAE,EAAE,CAAC;YAAEV,EAAE,EAAE;UAAE,CAAE;UAAAb,QAAA,eACnC3C,OAAA,CAAClB,WAAW;YAAC2E,SAAS;YAAAd,QAAA,gBACpB3C,OAAA,CAACjB,UAAU;cAAA4D,QAAA,EAAC;YAAO;cAAAnB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAChC3B,OAAA,CAAChB,MAAM;cACL2E,KAAK,EAAEtD,aAAc;cACrB8D,KAAK,EAAC,SAAS;cACfP,QAAQ,EAAGC,CAAC,IAAKvD,gBAAgB,CAACuD,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;cAAAhB,QAAA,gBAElD3C,OAAA,CAACf,QAAQ;gBAAC0E,KAAK,EAAC,EAAE;gBAAAhB,QAAA,EAAC;cAAY;gBAAAnB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,EACzCU,QAAQ,CAACE,GAAG,CAAExB,OAAO,iBACpBf,OAAA,CAACf,QAAQ;gBAAe0E,KAAK,EAAE5C,OAAQ;gBAAA4B,QAAA,EACpC5B;cAAO,GADKA,OAAO;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEZ,CACX,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eACP3B,OAAA,CAAC3B,IAAI;UAACiF,IAAI,EAAE;YAAEC,EAAE,EAAE,EAAE;YAAEW,EAAE,EAAE,CAAC;YAAEV,EAAE,EAAE;UAAE,CAAE;UAAAb,QAAA,eACnC3C,OAAA,CAAClB,WAAW;YAAC2E,SAAS;YAAAd,QAAA,gBACpB3C,OAAA,CAACjB,UAAU;cAAA4D,QAAA,EAAC;YAAI;cAAAnB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAC7B3B,OAAA,CAAChB,MAAM;cACL2E,KAAK,EAAEpD,UAAW;cAClB4D,KAAK,EAAC,MAAM;cACZP,QAAQ,EAAGC,CAAC,IAAKrD,aAAa,CAACqD,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;cAAAhB,QAAA,gBAE/C3C,OAAA,CAACf,QAAQ;gBAAC0E,KAAK,EAAC,EAAE;gBAAAhB,QAAA,EAAC;cAAS;gBAAAnB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,EACtCc,KAAK,CAACF,GAAG,CAAEzB,IAAI,iBACdd,OAAA,CAACf,QAAQ;gBAAY0E,KAAK,EAAE7C,IAAK;gBAAA6B,QAAA,EAC9B7B;cAAI,GADQA,IAAI;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAET,CACX,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eAGN3B,OAAA,CAAC3B,IAAI;MAAC+E,SAAS;MAACC,OAAO,EAAE,CAAE;MAAAV,QAAA,EACxBd,iBAAiB,CAACU,GAAG,CAAER,QAAQ,iBAC9B/B,OAAA,CAAC3B,IAAI;QAACiF,IAAI,EAAE;UAAEC,EAAE,EAAE,EAAE;UAAEC,EAAE,EAAE,CAAC;UAAEY,EAAE,EAAE;QAAE,CAAE;QAAAzB,QAAA,eACnC3C,OAAA,CAAC1B,IAAI;UACHsE,EAAE,EAAE;YACFyB,MAAM,EAAE,MAAM;YACdxB,OAAO,EAAE,MAAM;YACfyB,aAAa,EAAE,QAAQ;YACvBC,UAAU,EAAE,gBAAgB;YAC5B,SAAS,EAAE;cACTC,SAAS,EAAE,kBAAkB;cAC7BC,SAAS,EAAE;YACb;UACF,CAAE;UAAA9B,QAAA,gBAEF3C,OAAA,CAACzB,WAAW;YAACqE,EAAE,EAAE;cAAE8B,QAAQ,EAAE;YAAE,CAAE;YAAA/B,QAAA,gBAC/B3C,OAAA,CAACtB,GAAG;cAACkE,EAAE,EAAE;gBAAEC,OAAO,EAAE,MAAM;gBAAEC,cAAc,EAAE,eAAe;gBAAEC,UAAU,EAAE,YAAY;gBAAEC,EAAE,EAAE;cAAE,CAAE;cAAAL,QAAA,gBAC7F3C,OAAA,CAACtB,GAAG;gBAACkE,EAAE,EAAE;kBAAEC,OAAO,EAAE,MAAM;kBAAEE,UAAU,EAAE,QAAQ;kBAAE4B,GAAG,EAAE;gBAAE,CAAE;gBAAAhC,QAAA,GACxDrB,WAAW,CAACS,QAAQ,CAACjB,IAAI,CAAC,eAC3Bd,OAAA,CAAC5B,UAAU;kBAAC6E,OAAO,EAAC,IAAI;kBAACC,SAAS,EAAC,IAAI;kBAAAP,QAAA,EACpCZ,QAAQ,CAAClB;gBAAK;kBAAAW,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC,eACN3B,OAAA,CAACvB,MAAM;gBAAC6E,IAAI,EAAC,OAAO;gBAACV,EAAE,EAAE;kBAAEgC,QAAQ,EAAE,MAAM;kBAAEC,CAAC,EAAE;gBAAI,CAAE;gBAAAlC,QAAA,EACnDZ,QAAQ,CAACX,UAAU,gBAAGpB,OAAA,CAACH,QAAQ;kBAAC0B,KAAK,EAAC;gBAAO;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,gBAAG3B,OAAA,CAACF,cAAc;kBAAA0B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eAEN3B,OAAA,CAACtB,GAAG;cAACkE,EAAE,EAAE;gBAAEC,OAAO,EAAE,MAAM;gBAAE8B,GAAG,EAAE,CAAC;gBAAE3B,EAAE,EAAE;cAAE,CAAE;cAAAL,QAAA,gBAC1C3C,OAAA,CAACrB,IAAI;gBACHwF,KAAK,EAAEpC,QAAQ,CAACjB,IAAK;gBACrBwC,IAAI,EAAC,OAAO;gBACZ/B,KAAK,EAAEK,YAAY,CAACG,QAAQ,CAACjB,IAAI,CAAS;gBAC1CmC,OAAO,EAAC;cAAU;gBAAAzB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnB,CAAC,eACF3B,OAAA,CAACrB,IAAI;gBACHwF,KAAK,EAAEpC,QAAQ,CAAChB,OAAQ;gBACxBuC,IAAI,EAAC,OAAO;gBACZ/B,KAAK,EAAC,SAAS;gBACf0B,OAAO,EAAC;cAAU;gBAAAzB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAEN3B,OAAA,CAAC5B,UAAU;cAAC6E,OAAO,EAAC,OAAO;cAAC1B,KAAK,EAAC,gBAAgB;cAACqB,EAAE,EAAE;gBAAEI,EAAE,EAAE;cAAE,CAAE;cAAAL,QAAA,EAC9DZ,QAAQ,CAACf;YAAW;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACX,CAAC,eAEb3B,OAAA,CAAC5B,UAAU;cAAC6E,OAAO,EAAC,SAAS;cAAC1B,KAAK,EAAC,gBAAgB;cAACsB,OAAO,EAAC,OAAO;cAAAF,QAAA,GAAC,KAChE,EAACZ,QAAQ,CAACd,MAAM,EAAC,UAAG,EAACc,QAAQ,CAACb,UAAU;YAAA;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjC,CAAC,eACb3B,OAAA,CAAC5B,UAAU;cAAC6E,OAAO,EAAC,SAAS;cAAC1B,KAAK,EAAC,gBAAgB;cAACsB,OAAO,EAAC,OAAO;cAAAF,QAAA,GACjEZ,QAAQ,CAACZ,SAAS,EAAC,oBAAa,EAACY,QAAQ,CAACV,QAAQ;YAAA;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eAEd3B,OAAA,CAACxB,WAAW;YAAAmE,QAAA,gBACV3C,OAAA,CAACvB,MAAM;cAAC6E,IAAI,EAAC,OAAO;cAACwB,SAAS,eAAE9E,OAAA,CAACJ,QAAQ;gBAAA4B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAAAgB,QAAA,EAAC;YAE9C;cAAAnB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACT3B,OAAA,CAACvB,MAAM;cAAC6E,IAAI,EAAC,OAAO;cAAAX,QAAA,EAAC;YAErB;cAAAnB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC,GA7DkCI,QAAQ,CAACnB,EAAE;QAAAY,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OA8DhD,CACP;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAGP3B,OAAA,CAACd,GAAG;MACFqC,KAAK,EAAC,SAAS;MACf,cAAW,iBAAiB;MAC5BqB,EAAE,EAAE;QAAEqB,QAAQ,EAAE,OAAO;QAAEc,MAAM,EAAE,EAAE;QAAEC,KAAK,EAAE;MAAG,CAAE;MACjDC,OAAO,EAAEA,CAAA,KAAMvE,OAAO,CAAC,IAAI,CAAE;MAAAiC,QAAA,eAE7B3C,OAAA,CAACR,GAAG;QAAAgC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eAGN3B,OAAA,CAACb,MAAM;MAACsB,IAAI,EAAEA,IAAK;MAACyE,OAAO,EAAEA,CAAA,KAAMxE,OAAO,CAAC,KAAK,CAAE;MAACgC,QAAQ,EAAC,IAAI;MAACe,SAAS;MAAAd,QAAA,gBACxE3C,OAAA,CAACZ,WAAW;QAAAuD,QAAA,EAAC;MAAmB;QAAAnB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAa,CAAC,eAC9C3B,OAAA,CAACX,aAAa;QAAAsD,QAAA,gBACZ3C,OAAA,CAAC5B,UAAU;UAAC6E,OAAO,EAAC,OAAO;UAAC1B,KAAK,EAAC,gBAAgB;UAACqB,EAAE,EAAE;YAAEI,EAAE,EAAE;UAAE,CAAE;UAAAL,QAAA,EAAC;QAElE;UAAAnB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAEb3B,OAAA,CAAC5B,UAAU;UAAC6E,OAAO,EAAC,OAAO;UAAAN,QAAA,EAAC;QAE5B;UAAAnB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC,eAChB3B,OAAA,CAACV,aAAa;QAAAqD,QAAA,gBACZ3C,OAAA,CAACvB,MAAM;UAACwG,OAAO,EAAEA,CAAA,KAAMvE,OAAO,CAAC,KAAK,CAAE;UAAAiC,QAAA,EAAC;QAAM;UAAAnB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACtD3B,OAAA,CAACvB,MAAM;UAACwE,OAAO,EAAC,WAAW;UAAAN,QAAA,EAAC;QAAM;UAAAnB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9B,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEhB,CAAC;AAACzB,EAAA,CA9RID,SAAmB;AAAAkF,EAAA,GAAnBlF,SAAmB;AAgSzB,eAAeA,SAAS;AAAC,IAAAkF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}