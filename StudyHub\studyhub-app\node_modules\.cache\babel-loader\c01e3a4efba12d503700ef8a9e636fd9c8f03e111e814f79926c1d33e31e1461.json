{"ast": null, "code": "import _defineProperty from \"C:/Users/<USER>/OneDrive/\\u30C9\\u30AD\\u30E5\\u30E1\\u30F3\\u30C8/Projects/StudyHub/studyhub-app/node_modules/@babel/runtime/helpers/esm/defineProperty.js\";\nimport { constructFrom } from \"../../constructFrom.js\";\nimport { transpose } from \"../../transpose.js\";\nconst TIMEZONE_UNIT_PRIORITY = 10;\nexport class Setter {\n  constructor() {\n    _defineProperty(this, \"subPriority\", 0);\n  }\n  validate(_utcDate, _options) {\n    return true;\n  }\n}\nexport class ValueSetter extends Setter {\n  constructor(value, validateValue, setValue, priority, subPriority) {\n    super();\n    this.value = value;\n    this.validateValue = validateValue;\n    this.setValue = setValue;\n    this.priority = priority;\n    if (subPriority) {\n      this.subPriority = subPriority;\n    }\n  }\n  validate(date, options) {\n    return this.validateValue(date, this.value, options);\n  }\n  set(date, flags, options) {\n    return this.setValue(date, flags, this.value, options);\n  }\n}\nexport class DateTimezoneSetter extends Setter {\n  constructor(context, reference) {\n    super();\n    _defineProperty(this, \"priority\", TIMEZONE_UNIT_PRIORITY);\n    _defineProperty(this, \"subPriority\", -1);\n    this.context = context || (date => constructFrom(reference, date));\n  }\n  set(date, flags) {\n    if (flags.timestampIsSet) return date;\n    return constructFrom(date, transpose(date, this.context));\n  }\n}", "map": {"version": 3, "names": ["constructFrom", "transpose", "TIMEZONE_UNIT_PRIORITY", "<PERSON>ter", "constructor", "_defineProperty", "validate", "_utcDate", "_options", "ValueSetter", "value", "validate<PERSON><PERSON>ue", "setValue", "priority", "subPriority", "date", "options", "set", "flags", "DateTimezoneSetter", "context", "reference", "timestampIsSet"], "sources": ["C:/Users/<USER>/OneDrive/ドキュメント/Projects/StudyHub/studyhub-app/node_modules/date-fns/parse/_lib/Setter.js"], "sourcesContent": ["import { constructFrom } from \"../../constructFrom.js\";\nimport { transpose } from \"../../transpose.js\";\n\nconst TIMEZONE_UNIT_PRIORITY = 10;\n\nexport class Setter {\n  subPriority = 0;\n\n  validate(_utcDate, _options) {\n    return true;\n  }\n}\n\nexport class ValueSetter extends Setter {\n  constructor(\n    value,\n\n    validateValue,\n\n    setValue,\n\n    priority,\n    subPriority,\n  ) {\n    super();\n    this.value = value;\n    this.validateValue = validateValue;\n    this.setValue = setValue;\n    this.priority = priority;\n    if (subPriority) {\n      this.subPriority = subPriority;\n    }\n  }\n\n  validate(date, options) {\n    return this.validateValue(date, this.value, options);\n  }\n\n  set(date, flags, options) {\n    return this.setValue(date, flags, this.value, options);\n  }\n}\n\nexport class DateTimezoneSetter extends Setter {\n  priority = TIMEZONE_UNIT_PRIORITY;\n  subPriority = -1;\n\n  constructor(context, reference) {\n    super();\n    this.context = context || ((date) => constructFrom(reference, date));\n  }\n\n  set(date, flags) {\n    if (flags.timestampIsSet) return date;\n    return constructFrom(date, transpose(date, this.context));\n  }\n}\n"], "mappings": ";AAAA,SAASA,aAAa,QAAQ,wBAAwB;AACtD,SAASC,SAAS,QAAQ,oBAAoB;AAE9C,MAAMC,sBAAsB,GAAG,EAAE;AAEjC,OAAO,MAAMC,MAAM,CAAC;EAAAC,YAAA;IAAAC,eAAA,sBACJ,CAAC;EAAA;EAEfC,QAAQA,CAACC,QAAQ,EAAEC,QAAQ,EAAE;IAC3B,OAAO,IAAI;EACb;AACF;AAEA,OAAO,MAAMC,WAAW,SAASN,MAAM,CAAC;EACtCC,WAAWA,CACTM,KAAK,EAELC,aAAa,EAEbC,QAAQ,EAERC,QAAQ,EACRC,WAAW,EACX;IACA,KAAK,CAAC,CAAC;IACP,IAAI,CAACJ,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACC,aAAa,GAAGA,aAAa;IAClC,IAAI,CAACC,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,QAAQ,GAAGA,QAAQ;IACxB,IAAIC,WAAW,EAAE;MACf,IAAI,CAACA,WAAW,GAAGA,WAAW;IAChC;EACF;EAEAR,QAAQA,CAACS,IAAI,EAAEC,OAAO,EAAE;IACtB,OAAO,IAAI,CAACL,aAAa,CAACI,IAAI,EAAE,IAAI,CAACL,KAAK,EAAEM,OAAO,CAAC;EACtD;EAEAC,GAAGA,CAACF,IAAI,EAAEG,KAAK,EAAEF,OAAO,EAAE;IACxB,OAAO,IAAI,CAACJ,QAAQ,CAACG,IAAI,EAAEG,KAAK,EAAE,IAAI,CAACR,KAAK,EAAEM,OAAO,CAAC;EACxD;AACF;AAEA,OAAO,MAAMG,kBAAkB,SAAShB,MAAM,CAAC;EAI7CC,WAAWA,CAACgB,OAAO,EAAEC,SAAS,EAAE;IAC9B,KAAK,CAAC,CAAC;IAAChB,eAAA,mBAJCH,sBAAsB;IAAAG,eAAA,sBACnB,CAAC,CAAC;IAId,IAAI,CAACe,OAAO,GAAGA,OAAO,KAAML,IAAI,IAAKf,aAAa,CAACqB,SAAS,EAAEN,IAAI,CAAC,CAAC;EACtE;EAEAE,GAAGA,CAACF,IAAI,EAAEG,KAAK,EAAE;IACf,IAAIA,KAAK,CAACI,cAAc,EAAE,OAAOP,IAAI;IACrC,OAAOf,aAAa,CAACe,IAAI,EAAEd,SAAS,CAACc,IAAI,EAAE,IAAI,CAACK,OAAO,CAAC,CAAC;EAC3D;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}