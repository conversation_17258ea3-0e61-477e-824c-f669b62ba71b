import React from 'react';
import {
  Container,
  Typography,
  Card,
  CardContent,
  CardActions,
  Button,
  Box,
  Paper,
  LinearProgress,
  Chip,
} from '@mui/material';
import { Grid } from '@mui/material';
import {
  TrendingUp,
  Group,
  LibraryBooks,
  Schedule,
  Assignment,
  Star,
} from '@mui/icons-material';

const Dashboard: React.FC = () => {
  // Mock data - replace with actual data from Firebase
  const stats = [
    {
      title: 'Study Groups',
      value: '5',
      icon: <Group fontSize="large" color="primary" />,
      change: '+2 this week',
    },
    {
      title: 'Completed Courses',
      value: '12',
      icon: <LibraryBooks fontSize="large" color="primary" />,
      change: '+1 this month',
    },
    {
      title: 'Study Hours',
      value: '48',
      icon: <Schedule fontSize="large" color="primary" />,
      change: '+8 this week',
    },
    {
      title: 'Assignments',
      value: '3',
      icon: <Assignment fontSize="large" color="primary" />,
      change: 'Due this week',
    },
  ];

  const recentActivities = [
    {
      title: 'Joined "Advanced React Concepts" study group',
      time: '2 hours ago',
      type: 'group',
    },
    {
      title: 'Completed "JavaScript Fundamentals" quiz',
      time: '1 day ago',
      type: 'quiz',
    },
    {
      title: 'Uploaded notes for "Data Structures"',
      time: '2 days ago',
      type: 'resource',
    },
    {
      title: 'Started "Machine Learning Basics" course',
      time: '3 days ago',
      type: 'course',
    },
  ];

  const upcomingEvents = [
    {
      title: 'React Study Group Meeting',
      date: 'Today, 3:00 PM',
      type: 'meeting',
    },
    {
      title: 'Algorithm Quiz Due',
      date: 'Tomorrow, 11:59 PM',
      type: 'assignment',
    },
    {
      title: 'Web Development Workshop',
      date: 'Friday, 2:00 PM',
      type: 'workshop',
    },
  ];

  const currentCourses = [
    {
      title: 'Advanced React Development',
      progress: 75,
      instructor: 'John Doe',
      nextLesson: 'State Management with Redux',
    },
    {
      title: 'Data Structures & Algorithms',
      progress: 45,
      instructor: 'Jane Smith',
      nextLesson: 'Binary Trees',
    },
    {
      title: 'Machine Learning Fundamentals',
      progress: 20,
      instructor: 'Dr. Wilson',
      nextLesson: 'Linear Regression',
    },
  ];

  return (
    <Container maxWidth="lg">
      <Typography variant="h4" component="h1" gutterBottom>
        Dashboard
      </Typography>
      <Typography variant="body1" color="text.secondary" sx={{ mb: 4 }}>
        Welcome back! Here's what's happening with your studies.
      </Typography>

      {/* Stats Cards */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        {stats.map((stat, index) => (
          <Grid xs={12} sm={6} md={3} key={index}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  {stat.icon}
                  <Box sx={{ ml: 2 }}>
                    <Typography variant="h4" component="div">
                      {stat.value}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      {stat.title}
                    </Typography>
                  </Box>
                </Box>
                <Typography variant="caption" color="success.main">
                  <TrendingUp fontSize="small" sx={{ mr: 0.5 }} />
                  {stat.change}
                </Typography>
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>

      <Grid container spacing={3}>
        {/* Current Courses */}
        <Grid xs={12} md={8}>
          <Paper sx={{ p: 3, mb: 3 }}>
            <Typography variant="h6" gutterBottom>
              Current Courses
            </Typography>
            {currentCourses.map((course, index) => (
              <Card key={index} sx={{ mb: 2 }}>
                <CardContent>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                    <Typography variant="h6">{course.title}</Typography>
                    <Chip
                      label={`${course.progress}%`}
                      color="primary"
                      size="small"
                    />
                  </Box>
                  <LinearProgress
                    variant="determinate"
                    value={course.progress}
                    sx={{ mb: 2 }}
                  />
                  <Typography variant="body2" color="text.secondary" gutterBottom>
                    Instructor: {course.instructor}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Next: {course.nextLesson}
                  </Typography>
                </CardContent>
                <CardActions>
                  <Button size="small">Continue Learning</Button>
                  <Button size="small">View Details</Button>
                </CardActions>
              </Card>
            ))}
          </Paper>

          {/* Recent Activities */}
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              Recent Activities
            </Typography>
            {recentActivities.map((activity, index) => (
              <Box key={index} sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <Star color="primary" sx={{ mr: 2 }} />
                <Box>
                  <Typography variant="body2">{activity.title}</Typography>
                  <Typography variant="caption" color="text.secondary">
                    {activity.time}
                  </Typography>
                </Box>
              </Box>
            ))}
          </Paper>
        </Grid>

        {/* Upcoming Events */}
        <Grid xs={12} md={4}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              Upcoming Events
            </Typography>
            {upcomingEvents.map((event, index) => (
              <Card key={index} sx={{ mb: 2 }}>
                <CardContent>
                  <Typography variant="subtitle1" gutterBottom>
                    {event.title}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    {event.date}
                  </Typography>
                  <Chip
                    label={event.type}
                    size="small"
                    sx={{ mt: 1 }}
                    color={event.type === 'assignment' ? 'warning' : 'primary'}
                  />
                </CardContent>
              </Card>
            ))}
            <Button variant="outlined" fullWidth sx={{ mt: 2 }}>
              View All Events
            </Button>
          </Paper>
        </Grid>
      </Grid>
    </Container>
  );
};

export default Dashboard;
