{"ast": null, "code": "'use strict';\n\nmodule.exports = function (bitmap, value) {\n  return {\n    enumerable: !(bitmap & 1),\n    configurable: !(bitmap & 2),\n    writable: !(bitmap & 4),\n    value: value\n  };\n};", "map": {"version": 3, "names": ["module", "exports", "bitmap", "value", "enumerable", "configurable", "writable"], "sources": ["C:/Users/<USER>/OneDrive/ドキュメント/Projects/StudyHub/studyhub-app/node_modules/core-js-pure/internals/create-property-descriptor.js"], "sourcesContent": ["'use strict';\nmodule.exports = function (bitmap, value) {\n  return {\n    enumerable: !(bitmap & 1),\n    configurable: !(bitmap & 2),\n    writable: !(bitmap & 4),\n    value: value\n  };\n};\n"], "mappings": "AAAA,YAAY;;AACZA,MAAM,CAACC,OAAO,GAAG,UAAUC,MAAM,EAAEC,KAAK,EAAE;EACxC,OAAO;IACLC,UAAU,EAAE,EAAEF,MAAM,GAAG,CAAC,CAAC;IACzBG,YAAY,EAAE,EAAEH,MAAM,GAAG,CAAC,CAAC;IAC3BI,QAAQ,EAAE,EAAEJ,MAAM,GAAG,CAAC,CAAC;IACvBC,KAAK,EAAEA;EACT,CAAC;AACH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}