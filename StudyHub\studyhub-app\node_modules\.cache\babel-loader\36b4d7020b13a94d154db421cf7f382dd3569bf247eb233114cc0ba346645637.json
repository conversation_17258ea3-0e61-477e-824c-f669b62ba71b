{"ast": null, "code": "'use client';\n\nimport _objectSpread from \"C:/Users/<USER>/OneDrive/\\u30C9\\u30AD\\u30E5\\u30E1\\u30F3\\u30C8/Projects/StudyHub/studyhub-app/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nimport _objectWithoutProperties from \"C:/Users/<USER>/OneDrive/\\u30C9\\u30AD\\u30E5\\u30E1\\u30F3\\u30C8/Projects/StudyHub/studyhub-app/node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js\";\nconst _excluded = [\"className\", \"elementType\", \"ownerState\", \"externalForwardedProps\", \"internalForwardedProps\", \"shouldForwardComponentProp\"],\n  _excluded2 = [\"component\", \"slots\", \"slotProps\"],\n  _excluded3 = [\"component\"];\nimport useForkRef from '@mui/utils/useForkRef';\nimport appendOwnerState from '@mui/utils/appendOwnerState';\nimport resolveComponentProps from '@mui/utils/resolveComponentProps';\nimport mergeSlotProps from '@mui/utils/mergeSlotProps';\n/**\n * An internal function to create a Material UI slot.\n *\n * This is an advanced version of Base UI `useSlotProps` because Material UI allows leaf component to be customized via `component` prop\n * while Base UI does not need to support leaf component customization.\n *\n * @param {string} name: name of the slot\n * @param {object} parameters\n * @returns {[Slot, slotProps]} The slot's React component and the slot's props\n *\n * Note: the returned slot's props\n * - will never contain `component` prop.\n * - might contain `as` prop.\n */\nexport default function useSlot(\n/**\n * The slot's name. All Material UI components should have `root` slot.\n *\n * If the name is `root`, the logic behaves differently from other slots,\n * e.g. the `externalForwardedProps` are spread to `root` slot but not other slots.\n */\nname, parameters) {\n  const {\n      className,\n      elementType: initialElementType,\n      ownerState,\n      externalForwardedProps,\n      internalForwardedProps,\n      shouldForwardComponentProp = false\n    } = parameters,\n    useSlotPropsParams = _objectWithoutProperties(parameters, _excluded);\n  const {\n      component: rootComponent,\n      slots = {\n        [name]: undefined\n      },\n      slotProps = {\n        [name]: undefined\n      }\n    } = externalForwardedProps,\n    other = _objectWithoutProperties(externalForwardedProps, _excluded2);\n  const elementType = slots[name] || initialElementType;\n\n  // `slotProps[name]` can be a callback that receives the component's ownerState.\n  // `resolvedComponentsProps` is always a plain object.\n  const resolvedComponentsProps = resolveComponentProps(slotProps[name], ownerState);\n  const _mergeSlotProps = mergeSlotProps(_objectSpread(_objectSpread({\n      className\n    }, useSlotPropsParams), {}, {\n      externalForwardedProps: name === 'root' ? other : undefined,\n      externalSlotProps: resolvedComponentsProps\n    })),\n    {\n      props: {\n        component: slotComponent\n      },\n      internalRef\n    } = _mergeSlotProps,\n    mergedProps = _objectWithoutProperties(_mergeSlotProps.props, _excluded3);\n  const ref = useForkRef(internalRef, resolvedComponentsProps === null || resolvedComponentsProps === void 0 ? void 0 : resolvedComponentsProps.ref, parameters.ref);\n  const LeafComponent = name === 'root' ? slotComponent || rootComponent : slotComponent;\n  const props = appendOwnerState(elementType, _objectSpread(_objectSpread(_objectSpread(_objectSpread(_objectSpread(_objectSpread({}, name === 'root' && !rootComponent && !slots[name] && internalForwardedProps), name !== 'root' && !slots[name] && internalForwardedProps), mergedProps), LeafComponent && !shouldForwardComponentProp && {\n    as: LeafComponent\n  }), LeafComponent && shouldForwardComponentProp && {\n    component: LeafComponent\n  }), {}, {\n    ref\n  }), ownerState);\n  return [elementType, props];\n}", "map": {"version": 3, "names": ["_objectSpread", "_objectWithoutProperties", "_excluded", "_excluded2", "_excluded3", "useForkRef", "appendOwnerState", "resolveComponentProps", "mergeSlotProps", "useSlot", "name", "parameters", "className", "elementType", "initialElementType", "ownerState", "externalForwardedProps", "internalForwardedProps", "shouldForwardComponentProp", "useSlotPropsParams", "component", "rootComponent", "slots", "undefined", "slotProps", "other", "resolvedComponentsProps", "_mergeSlotProps", "externalSlotProps", "props", "slotComponent", "internalRef", "mergedProps", "ref", "LeafComponent", "as"], "sources": ["C:/Users/<USER>/OneDrive/ドキュメント/Projects/StudyHub/studyhub-app/node_modules/@mui/material/esm/utils/useSlot.js"], "sourcesContent": ["'use client';\n\nimport useForkRef from '@mui/utils/useForkRef';\nimport appendOwnerState from '@mui/utils/appendOwnerState';\nimport resolveComponentProps from '@mui/utils/resolveComponentProps';\nimport mergeSlotProps from '@mui/utils/mergeSlotProps';\n/**\n * An internal function to create a Material UI slot.\n *\n * This is an advanced version of Base UI `useSlotProps` because Material UI allows leaf component to be customized via `component` prop\n * while Base UI does not need to support leaf component customization.\n *\n * @param {string} name: name of the slot\n * @param {object} parameters\n * @returns {[Slot, slotProps]} The slot's React component and the slot's props\n *\n * Note: the returned slot's props\n * - will never contain `component` prop.\n * - might contain `as` prop.\n */\nexport default function useSlot(\n/**\n * The slot's name. All Material UI components should have `root` slot.\n *\n * If the name is `root`, the logic behaves differently from other slots,\n * e.g. the `externalForwardedProps` are spread to `root` slot but not other slots.\n */\nname, parameters) {\n  const {\n    className,\n    elementType: initialElementType,\n    ownerState,\n    externalForwardedProps,\n    internalForwardedProps,\n    shouldForwardComponentProp = false,\n    ...useSlotPropsParams\n  } = parameters;\n  const {\n    component: rootComponent,\n    slots = {\n      [name]: undefined\n    },\n    slotProps = {\n      [name]: undefined\n    },\n    ...other\n  } = externalForwardedProps;\n  const elementType = slots[name] || initialElementType;\n\n  // `slotProps[name]` can be a callback that receives the component's ownerState.\n  // `resolvedComponentsProps` is always a plain object.\n  const resolvedComponentsProps = resolveComponentProps(slotProps[name], ownerState);\n  const {\n    props: {\n      component: slotComponent,\n      ...mergedProps\n    },\n    internalRef\n  } = mergeSlotProps({\n    className,\n    ...useSlotPropsParams,\n    externalForwardedProps: name === 'root' ? other : undefined,\n    externalSlotProps: resolvedComponentsProps\n  });\n  const ref = useForkRef(internalRef, resolvedComponentsProps?.ref, parameters.ref);\n  const LeafComponent = name === 'root' ? slotComponent || rootComponent : slotComponent;\n  const props = appendOwnerState(elementType, {\n    ...(name === 'root' && !rootComponent && !slots[name] && internalForwardedProps),\n    ...(name !== 'root' && !slots[name] && internalForwardedProps),\n    ...mergedProps,\n    ...(LeafComponent && !shouldForwardComponentProp && {\n      as: LeafComponent\n    }),\n    ...(LeafComponent && shouldForwardComponentProp && {\n      component: LeafComponent\n    }),\n    ref\n  }, ownerState);\n  return [elementType, props];\n}"], "mappings": "AAAA,YAAY;;AAAC,OAAAA,aAAA;AAAA,OAAAC,wBAAA;AAAA,MAAAC,SAAA;EAAAC,UAAA;EAAAC,UAAA;AAEb,OAAOC,UAAU,MAAM,uBAAuB;AAC9C,OAAOC,gBAAgB,MAAM,6BAA6B;AAC1D,OAAOC,qBAAqB,MAAM,kCAAkC;AACpE,OAAOC,cAAc,MAAM,2BAA2B;AACtD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASC,OAAOA;AAC/B;AACA;AACA;AACA;AACA;AACA;AACAC,IAAI,EAAEC,UAAU,EAAE;EAChB,MAAM;MACJC,SAAS;MACTC,WAAW,EAAEC,kBAAkB;MAC/BC,UAAU;MACVC,sBAAsB;MACtBC,sBAAsB;MACtBC,0BAA0B,GAAG;IAE/B,CAAC,GAAGP,UAAU;IADTQ,kBAAkB,GAAAlB,wBAAA,CACnBU,UAAU,EAAAT,SAAA;EACd,MAAM;MACJkB,SAAS,EAAEC,aAAa;MACxBC,KAAK,GAAG;QACN,CAACZ,IAAI,GAAGa;MACV,CAAC;MACDC,SAAS,GAAG;QACV,CAACd,IAAI,GAAGa;MACV;IAEF,CAAC,GAAGP,sBAAsB;IADrBS,KAAK,GAAAxB,wBAAA,CACNe,sBAAsB,EAAAb,UAAA;EAC1B,MAAMU,WAAW,GAAGS,KAAK,CAACZ,IAAI,CAAC,IAAII,kBAAkB;;EAErD;EACA;EACA,MAAMY,uBAAuB,GAAGnB,qBAAqB,CAACiB,SAAS,CAACd,IAAI,CAAC,EAAEK,UAAU,CAAC;EAClF,MAAAY,eAAA,GAMInB,cAAc,CAAAR,aAAA,CAAAA,aAAA;MAChBY;IAAS,GACNO,kBAAkB;MACrBH,sBAAsB,EAAEN,IAAI,KAAK,MAAM,GAAGe,KAAK,GAAGF,SAAS;MAC3DK,iBAAiB,EAAEF;IAAuB,EAC3C,CAAC;IAXI;MACJG,KAAK,EAAE;QACLT,SAAS,EAAEU;MAEb,CAAC;MACDC;IACF,CAAC,GAAAJ,eAAA;IAHMK,WAAW,GAAA/B,wBAAA,CAAA0B,eAAA,CAFhBE,KAAK,EAAAzB,UAAA;EAWP,MAAM6B,GAAG,GAAG5B,UAAU,CAAC0B,WAAW,EAAEL,uBAAuB,aAAvBA,uBAAuB,uBAAvBA,uBAAuB,CAAEO,GAAG,EAAEtB,UAAU,CAACsB,GAAG,CAAC;EACjF,MAAMC,aAAa,GAAGxB,IAAI,KAAK,MAAM,GAAGoB,aAAa,IAAIT,aAAa,GAAGS,aAAa;EACtF,MAAMD,KAAK,GAAGvB,gBAAgB,CAACO,WAAW,EAAAb,aAAA,CAAAA,aAAA,CAAAA,aAAA,CAAAA,aAAA,CAAAA,aAAA,CAAAA,aAAA,KACpCU,IAAI,KAAK,MAAM,IAAI,CAACW,aAAa,IAAI,CAACC,KAAK,CAACZ,IAAI,CAAC,IAAIO,sBAAsB,GAC3EP,IAAI,KAAK,MAAM,IAAI,CAACY,KAAK,CAACZ,IAAI,CAAC,IAAIO,sBAAsB,GAC1De,WAAW,GACVE,aAAa,IAAI,CAAChB,0BAA0B,IAAI;IAClDiB,EAAE,EAAED;EACN,CAAC,GACGA,aAAa,IAAIhB,0BAA0B,IAAI;IACjDE,SAAS,EAAEc;EACb,CAAC;IACDD;EAAG,IACFlB,UAAU,CAAC;EACd,OAAO,CAACF,WAAW,EAAEgB,KAAK,CAAC;AAC7B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}