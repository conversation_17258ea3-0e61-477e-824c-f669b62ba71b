{"ast": null, "code": "import createStyled from './createStyled';\nconst styled = createStyled();\nexport default styled;", "map": {"version": 3, "names": ["createStyled", "styled"], "sources": ["C:/Users/<USER>/OneDrive/ドキュメント/Projects/StudyHub/studyhub-app/node_modules/@mui/system/esm/styled.js"], "sourcesContent": ["import createStyled from './createStyled';\nconst styled = createStyled();\nexport default styled;"], "mappings": "AAAA,OAAOA,YAAY,MAAM,gBAAgB;AACzC,MAAMC,MAAM,GAAGD,YAAY,CAAC,CAAC;AAC7B,eAAeC,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}