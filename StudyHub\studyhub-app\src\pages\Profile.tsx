import React, { useState } from 'react';
import {
  Container,
  <PERSON><PERSON><PERSON>,
  Card,
  CardContent,
  Button,
  Box,
  Avatar,
  TextField,
  Chip,
  Paper,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Divider,
  LinearProgress,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  IconButton,
  Alert,
  CircularProgress,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
} from '@mui/material';
import { Grid } from '@mui/material';
import {
  Edit,
  Save,
  Cancel,
  School,
  Group,
  LibraryBooks,
  Star,
  TrendingUp,
  PhotoCamera,
  CalendarToday,
} from '@mui/icons-material';
import { useAuth } from '../contexts/AuthContext';
import { ref, uploadBytes, getDownloadURL } from 'firebase/storage';
import { storage } from '../firebase/config';

const Profile: React.FC = () => {
  const { currentUser, userProfile, updateUserProfile } = useAuth();
  const [isEditing, setIsEditing] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [profileData, setProfileData] = useState({
    firstName: '',
    lastName: '',
    bio: '',
    university: '',
    major: '',
    year: '',
  });
  const [profilePictureFile, setProfilePictureFile] = useState<File | null>(null);
  const [profilePicturePreview, setProfilePicturePreview] = useState<string>('');

  // Initialize profile data when userProfile changes
  React.useEffect(() => {
    if (userProfile) {
      setProfileData({
        firstName: userProfile.firstName || '',
        lastName: userProfile.lastName || '',
        bio: userProfile.bio || '',
        university: userProfile.university || '',
        major: userProfile.major || '',
        year: userProfile.year || '',
      });
      setProfilePicturePreview(userProfile.profilePicture || '');
    }
  }, [userProfile]);

  const handleInputChange = (field: string, value: string) => {
    setProfileData(prev => ({ ...prev, [field]: value }));
  };

  const handleProfilePictureChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setProfilePictureFile(file);
      const reader = new FileReader();
      reader.onload = (e) => {
        setProfilePicturePreview(e.target?.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  const uploadProfilePicture = async (file: File): Promise<string> => {
    if (!currentUser) throw new Error('No user logged in');

    const storageRef = ref(storage, `profile-pictures/${currentUser.uid}`);
    const snapshot = await uploadBytes(storageRef, file);
    return await getDownloadURL(snapshot.ref);
  };

  const handleSave = async () => {
    if (!currentUser) return;

    setLoading(true);
    setError('');
    setSuccess('');

    try {
      let profilePictureUrl = userProfile?.profilePicture || '';

      // Upload new profile picture if selected
      if (profilePictureFile) {
        profilePictureUrl = await uploadProfilePicture(profilePictureFile);
      }

      // Update profile
      await updateUserProfile({
        ...profileData,
        profilePicture: profilePictureUrl,
      });

      setSuccess('Profile updated successfully!');
      setIsEditing(false);
      setProfilePictureFile(null);
    } catch (error: any) {
      setError(error.message || 'Failed to update profile');
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    if (userProfile) {
      setProfileData({
        firstName: userProfile.firstName || '',
        lastName: userProfile.lastName || '',
        bio: userProfile.bio || '',
        university: userProfile.university || '',
        major: userProfile.major || '',
        year: userProfile.year || '',
      });
      setProfilePicturePreview(userProfile.profilePicture || '');
    }
    setProfilePictureFile(null);
    setIsEditing(false);
    setError('');
    setSuccess('');
  };

  // Mock data - replace with actual data from Firebase
  const stats = [
    { label: 'Study Groups Joined', value: 5, icon: <Group color="primary" /> },
    { label: 'Courses Completed', value: 12, icon: <School color="primary" /> },
    { label: 'Resources Shared', value: 8, icon: <LibraryBooks color="primary" /> },
    { label: 'Total Study Hours', value: 156, icon: <TrendingUp color="primary" /> },
  ];

  const achievements = [
    { title: 'Early Bird', description: 'Completed 5 courses', icon: '🏆' },
    { title: 'Team Player', description: 'Joined 3 study groups', icon: '🤝' },
    { title: 'Knowledge Sharer', description: 'Uploaded 5 resources', icon: '📚' },
    { title: 'Consistent Learner', description: '30-day study streak', icon: '🔥' },
  ];

  const recentActivity = [
    { action: 'Completed "Advanced React" course', date: '2 days ago' },
    { action: 'Joined "Machine Learning" study group', date: '1 week ago' },
    { action: 'Uploaded "JavaScript Notes"', date: '1 week ago' },
    { action: 'Started "Data Structures" course', date: '2 weeks ago' },
  ];

  const currentCourses = [
    { name: 'Advanced React Development', progress: 75 },
    { name: 'Machine Learning Fundamentals', progress: 45 },
    { name: 'Database Design', progress: 30 },
  ];

  if (!currentUser || !userProfile) {
    return (
      <Container maxWidth="lg">
        <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
          <CircularProgress />
        </Box>
      </Container>
    );
  }

  return (
    <Container maxWidth="lg">
      <Grid container spacing={3}>
        {/* Profile Information */}
        <Grid xs={12} md={4}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <Box sx={{ position: 'relative', display: 'inline-block', mb: 2 }}>
                <Avatar
                  src={profilePicturePreview}
                  sx={{
                    width: 120,
                    height: 120,
                    mx: 'auto',
                    fontSize: '3rem',
                  }}
                >
                  {profileData.firstName[0]}{profileData.lastName[0]}
                </Avatar>
                {isEditing && (
                  <IconButton
                    component="label"
                    sx={{
                      position: 'absolute',
                      bottom: 0,
                      right: 0,
                      backgroundColor: 'primary.main',
                      color: 'white',
                      '&:hover': { backgroundColor: 'primary.dark' },
                    }}
                  >
                    <PhotoCamera />
                    <input
                      type="file"
                      hidden
                      accept="image/*"
                      onChange={handleProfilePictureChange}
                    />
                  </IconButton>
                )}
              </Box>

              {error && (
                <Alert severity="error" sx={{ mb: 2 }}>
                  {error}
                </Alert>
              )}

              {success && (
                <Alert severity="success" sx={{ mb: 2 }}>
                  {success}
                </Alert>
              )}

              {isEditing ? (
                <Box sx={{ mb: 2 }}>
                  <TextField
                    fullWidth
                    label="First Name"
                    value={profileData.firstName}
                    onChange={(e) => handleInputChange('firstName', e.target.value)}
                    sx={{ mb: 1 }}
                  />
                  <TextField
                    fullWidth
                    label="Last Name"
                    value={profileData.lastName}
                    onChange={(e) => handleInputChange('lastName', e.target.value)}
                    sx={{ mb: 1 }}
                  />
                  <TextField
                    fullWidth
                    label="Email"
                    value={userProfile.email}
                    disabled
                    sx={{ mb: 1 }}
                  />
                  <TextField
                    fullWidth
                    label="University"
                    value={profileData.university}
                    onChange={(e) => handleInputChange('university', e.target.value)}
                    sx={{ mb: 1 }}
                  />
                  <TextField
                    fullWidth
                    label="Major"
                    value={profileData.major}
                    onChange={(e) => handleInputChange('major', e.target.value)}
                    sx={{ mb: 1 }}
                  />
                  <FormControl fullWidth sx={{ mb: 1 }}>
                    <InputLabel>Year</InputLabel>
                    <Select
                      value={profileData.year}
                      label="Year"
                      onChange={(e) => handleInputChange('year', e.target.value)}
                    >
                      <MenuItem value="Freshman">Freshman</MenuItem>
                      <MenuItem value="Sophomore">Sophomore</MenuItem>
                      <MenuItem value="Junior">Junior</MenuItem>
                      <MenuItem value="Senior">Senior</MenuItem>
                      <MenuItem value="Graduate">Graduate</MenuItem>
                      <MenuItem value="PhD">PhD</MenuItem>
                    </Select>
                  </FormControl>
                  <TextField
                    fullWidth
                    label="Bio"
                    multiline
                    rows={3}
                    value={profileData.bio}
                    onChange={(e) => handleInputChange('bio', e.target.value)}
                  />
                </Box>
              ) : (
                <Box sx={{ mb: 2 }}>
                  <Typography variant="h5" gutterBottom>
                    {userProfile.displayName}
                  </Typography>
                  <Typography variant="body2" color="text.secondary" gutterBottom>
                    {userProfile.email}
                  </Typography>
                  <Box sx={{ display: 'flex', justifyContent: 'center', gap: 1, mb: 2 }}>
                    {userProfile.university && <Chip label={userProfile.university} size="small" />}
                    {userProfile.major && <Chip label={userProfile.major} size="small" />}
                    {userProfile.year && <Chip label={userProfile.year} size="small" />}
                  </Box>
                  <Typography variant="body2" color="text.secondary">
                    {userProfile.bio || 'No bio available'}
                  </Typography>
                  <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', gap: 1, mt: 2 }}>
                    <CalendarToday fontSize="small" color="action" />
                    <Typography variant="caption" color="text.secondary">
                      Joined {userProfile.createdAt?.toDate?.()?.toLocaleDateString() || 'Recently'}
                    </Typography>
                  </Box>
                </Box>
              )}

              {isEditing ? (
                <Box sx={{ display: 'flex', gap: 1, justifyContent: 'center' }}>
                  <Button
                    variant="contained"
                    startIcon={loading ? <CircularProgress size={20} /> : <Save />}
                    onClick={handleSave}
                    disabled={loading}
                  >
                    {loading ? 'Saving...' : 'Save'}
                  </Button>
                  <Button
                    variant="outlined"
                    startIcon={<Cancel />}
                    onClick={handleCancel}
                    disabled={loading}
                  >
                    Cancel
                  </Button>
                </Box>
              ) : (
                <Button
                  variant="contained"
                  startIcon={<Edit />}
                  onClick={() => setIsEditing(true)}
                >
                  Edit Profile
                </Button>
              )}
            </CardContent>
          </Card>

          {/* Stats */}
          <Card sx={{ mt: 3 }}>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Statistics
              </Typography>
              {stats.map((stat, index) => (
                <Box key={index} sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  {stat.icon}
                  <Box sx={{ ml: 2, flexGrow: 1 }}>
                    <Typography variant="body2" color="text.secondary">
                      {stat.label}
                    </Typography>
                    <Typography variant="h6">
                      {stat.value}
                    </Typography>
                  </Box>
                </Box>
              ))}
            </CardContent>
          </Card>
        </Grid>

        {/* Main Content */}
        <Grid xs={12} md={8}>
          {/* Current Courses */}
          <Card sx={{ mb: 3 }}>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Current Courses
              </Typography>
              {currentCourses.map((course, index) => (
                <Box key={index} sx={{ mb: 2 }}>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                    <Typography variant="body1">{course.name}</Typography>
                    <Typography variant="body2" color="text.secondary">
                      {course.progress}%
                    </Typography>
                  </Box>
                  <LinearProgress variant="determinate" value={course.progress} />
                </Box>
              ))}
            </CardContent>
          </Card>

          {/* Achievements */}
          <Card sx={{ mb: 3 }}>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Achievements
              </Typography>
              <Grid container spacing={2}>
                {achievements.map((achievement, index) => (
                  <Grid xs={12} sm={6} key={index}>
                    <Paper
                      sx={{
                        p: 2,
                        display: 'flex',
                        alignItems: 'center',
                        backgroundColor: 'primary.light',
                        color: 'primary.contrastText',
                      }}
                    >
                      <Typography variant="h4" sx={{ mr: 2 }}>
                        {achievement.icon}
                      </Typography>
                      <Box>
                        <Typography variant="subtitle1" fontWeight="bold">
                          {achievement.title}
                        </Typography>
                        <Typography variant="body2">
                          {achievement.description}
                        </Typography>
                      </Box>
                    </Paper>
                  </Grid>
                ))}
              </Grid>
            </CardContent>
          </Card>

          {/* Recent Activity */}
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Recent Activity
              </Typography>
              <List>
                {recentActivity.map((activity, index) => (
                  <React.Fragment key={index}>
                    <ListItem>
                      <ListItemIcon>
                        <Star color="primary" />
                      </ListItemIcon>
                      <ListItemText
                        primary={activity.action}
                        secondary={activity.date}
                      />
                    </ListItem>
                    {index < recentActivity.length - 1 && <Divider />}
                  </React.Fragment>
                ))}
              </List>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Container>
  );
};

export default Profile;
