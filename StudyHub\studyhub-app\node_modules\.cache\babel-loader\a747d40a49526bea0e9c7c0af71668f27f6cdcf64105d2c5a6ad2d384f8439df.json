{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\\\u30C9\\u30AD\\u30E5\\u30E1\\u30F3\\u30C8\\\\Projects\\\\StudyHub\\\\studyhub-app\\\\src\\\\components\\\\Navbar.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { AppBar, Toolbar, Typography, Button, IconButton, Menu, MenuItem, Box, Avatar, useTheme, useMediaQuery, Drawer, List, ListItem, ListItemIcon, ListItemText, Divider } from '@mui/material';\nimport { Home, Dashboard, Group, AccountCircle, Chat, Message, Explore, TrendingUp, Menu as MenuIcon } from '@mui/icons-material';\nimport { Link, useNavigate } from 'react-router-dom';\nimport { useAuth } from '../contexts/AuthContext';\nimport NotificationBell from './NotificationBell';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst Navbar = () => {\n  _s();\n  var _userProfile$firstNam, _userProfile$lastName, _userProfile$firstNam2, _userProfile$lastName2;\n  const [anchorEl, setAnchorEl] = useState(null);\n  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);\n  const navigate = useNavigate();\n  const {\n    currentUser,\n    userProfile,\n    logout\n  } = useAuth();\n  const theme = useTheme();\n  const isMobile = useMediaQuery(theme.breakpoints.down('md'));\n  const handleMenu = event => {\n    setAnchorEl(event.currentTarget);\n  };\n  const handleClose = () => {\n    setAnchorEl(null);\n  };\n  const handleMobileMenuToggle = () => {\n    setMobileMenuOpen(!mobileMenuOpen);\n  };\n  const handleMobileMenuClose = () => {\n    setMobileMenuOpen(false);\n  };\n  const handleLogout = async () => {\n    try {\n      await logout();\n      handleClose();\n      handleMobileMenuClose();\n      navigate('/');\n    } catch (error) {\n      console.error('Logout error:', error);\n    }\n  };\n  const navigationItems = [{\n    label: 'Feed',\n    path: '/feed',\n    icon: /*#__PURE__*/_jsxDEV(Dashboard, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 73,\n      columnNumber: 43\n    }, this)\n  }, {\n    label: 'Explore',\n    path: '/explore',\n    icon: /*#__PURE__*/_jsxDEV(Explore, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 74,\n      columnNumber: 49\n    }, this)\n  }, {\n    label: 'Communities',\n    path: '/communities',\n    icon: /*#__PURE__*/_jsxDEV(Group, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 75,\n      columnNumber: 57\n    }, this)\n  }, {\n    label: 'Trending',\n    path: '/trending',\n    icon: /*#__PURE__*/_jsxDEV(TrendingUp, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 76,\n      columnNumber: 51\n    }, this)\n  }, {\n    label: 'Chat',\n    path: '/chat',\n    icon: /*#__PURE__*/_jsxDEV(Message, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 77,\n      columnNumber: 43\n    }, this)\n  }];\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(AppBar, {\n      position: \"static\",\n      elevation: 1,\n      children: /*#__PURE__*/_jsxDEV(Toolbar, {\n        children: [/*#__PURE__*/_jsxDEV(Chat, {\n          sx: {\n            mr: 2\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 84,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          component: \"div\",\n          sx: {\n            flexGrow: 1\n          },\n          children: \"ChatRoom\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 85,\n          columnNumber: 11\n        }, this), !isMobile && /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center',\n            gap: 1\n          },\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            color: \"inherit\",\n            component: Link,\n            to: \"/\",\n            startIcon: /*#__PURE__*/_jsxDEV(Home, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 96,\n              columnNumber: 28\n            }, this),\n            sx: {\n              textTransform: 'none'\n            },\n            children: \"Home\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 92,\n            columnNumber: 15\n          }, this), currentUser && navigationItems.map(item => /*#__PURE__*/_jsxDEV(Button, {\n            color: \"inherit\",\n            component: Link,\n            to: item.path,\n            startIcon: item.icon,\n            sx: {\n              textTransform: 'none'\n            },\n            children: item.label\n          }, item.path, false, {\n            fileName: _jsxFileName,\n            lineNumber: 103,\n            columnNumber: 17\n          }, this)), currentUser ? /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              alignItems: 'center',\n              gap: 1,\n              ml: 1\n            },\n            children: [/*#__PURE__*/_jsxDEV(NotificationBell, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 117,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n              size: \"large\",\n              \"aria-label\": \"account of current user\",\n              \"aria-controls\": \"menu-appbar\",\n              \"aria-haspopup\": \"true\",\n              onClick: handleMenu,\n              color: \"inherit\",\n              children: /*#__PURE__*/_jsxDEV(Avatar, {\n                sx: {\n                  width: 32,\n                  height: 32\n                },\n                src: userProfile === null || userProfile === void 0 ? void 0 : userProfile.profilePicture,\n                children: [userProfile === null || userProfile === void 0 ? void 0 : (_userProfile$firstNam = userProfile.firstName) === null || _userProfile$firstNam === void 0 ? void 0 : _userProfile$firstNam[0], userProfile === null || userProfile === void 0 ? void 0 : (_userProfile$lastName = userProfile.lastName) === null || _userProfile$lastName === void 0 ? void 0 : _userProfile$lastName[0]]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 126,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 118,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Menu, {\n              id: \"menu-appbar\",\n              anchorEl: anchorEl,\n              anchorOrigin: {\n                vertical: 'bottom',\n                horizontal: 'right'\n              },\n              keepMounted: true,\n              transformOrigin: {\n                vertical: 'top',\n                horizontal: 'right'\n              },\n              open: Boolean(anchorEl),\n              onClose: handleClose,\n              children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                onClick: () => {\n                  handleClose();\n                  navigate('/profile');\n                },\n                children: [/*#__PURE__*/_jsxDEV(AccountCircle, {\n                  sx: {\n                    mr: 1\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 146,\n                  columnNumber: 23\n                }, this), \"Profile\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 145,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                onClick: handleLogout,\n                children: \"Logout\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 149,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 130,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 116,\n            columnNumber: 17\n          }, this) : /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              gap: 1,\n              ml: 1\n            },\n            children: [/*#__PURE__*/_jsxDEV(Button, {\n              color: \"inherit\",\n              component: Link,\n              to: \"/login\",\n              sx: {\n                textTransform: 'none'\n              },\n              children: \"Login\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 156,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              color: \"inherit\",\n              component: Link,\n              to: \"/register\",\n              variant: \"outlined\",\n              sx: {\n                borderColor: 'white',\n                color: 'white',\n                textTransform: 'none',\n                '&:hover': {\n                  borderColor: 'rgba(255, 255, 255, 0.7)',\n                  backgroundColor: 'rgba(255, 255, 255, 0.1)'\n                }\n              },\n              children: \"Register\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 164,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 155,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 91,\n          columnNumber: 13\n        }, this), isMobile && /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center',\n            gap: 1\n          },\n          children: [currentUser && /*#__PURE__*/_jsxDEV(NotificationBell, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 189,\n            columnNumber: 31\n          }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n            color: \"inherit\",\n            \"aria-label\": \"open drawer\",\n            onClick: handleMobileMenuToggle,\n            children: /*#__PURE__*/_jsxDEV(MenuIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 195,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 190,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 188,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 83,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 82,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Drawer, {\n      anchor: \"right\",\n      open: mobileMenuOpen,\n      onClose: handleMobileMenuClose,\n      sx: {\n        '& .MuiDrawer-paper': {\n          width: 280,\n          boxSizing: 'border-box'\n        }\n      },\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          p: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          sx: {\n            mb: 2\n          },\n          children: \"ChatRoom\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 215,\n          columnNumber: 11\n        }, this), currentUser && userProfile && /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center',\n            mb: 2,\n            p: 2,\n            bgcolor: 'grey.100',\n            borderRadius: 1\n          },\n          children: [/*#__PURE__*/_jsxDEV(Avatar, {\n            src: userProfile.profilePicture,\n            sx: {\n              mr: 2\n            },\n            children: [(_userProfile$firstNam2 = userProfile.firstName) === null || _userProfile$firstNam2 === void 0 ? void 0 : _userProfile$firstNam2[0], (_userProfile$lastName2 = userProfile.lastName) === null || _userProfile$lastName2 === void 0 ? void 0 : _userProfile$lastName2[0]]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 221,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle2\",\n              children: [userProfile.firstName, \" \", userProfile.lastName]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 225,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              color: \"text.secondary\",\n              children: userProfile.email\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 228,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 224,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 220,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 214,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 236,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(List, {\n        children: [/*#__PURE__*/_jsxDEV(ListItem, {\n          button: true,\n          component: Link,\n          to: \"/\",\n          onClick: handleMobileMenuClose,\n          children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n            children: /*#__PURE__*/_jsxDEV(Home, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 241,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 240,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n            primary: \"Home\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 243,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 239,\n          columnNumber: 11\n        }, this), currentUser && navigationItems.map(item => /*#__PURE__*/_jsxDEV(ListItem, {\n          button: true,\n          component: Link,\n          to: item.path,\n          onClick: handleMobileMenuClose,\n          children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n            children: item.icon\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 254,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n            primary: item.label\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 257,\n            columnNumber: 15\n          }, this)]\n        }, item.path, true, {\n          fileName: _jsxFileName,\n          lineNumber: 247,\n          columnNumber: 13\n        }, this)), currentUser && /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 263,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(ListItem, {\n            button: true,\n            onClick: () => {\n              navigate('/profile');\n              handleMobileMenuClose();\n            },\n            children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n              children: /*#__PURE__*/_jsxDEV(AccountCircle, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 266,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 265,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n              primary: \"Profile\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 268,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 264,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(ListItem, {\n            button: true,\n            onClick: handleLogout,\n            children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n              children: /*#__PURE__*/_jsxDEV(AccountCircle, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 272,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 271,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n              primary: \"Logout\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 274,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 270,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true), !currentUser && /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 281,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(ListItem, {\n            button: true,\n            component: Link,\n            to: \"/login\",\n            onClick: handleMobileMenuClose,\n            children: /*#__PURE__*/_jsxDEV(ListItemText, {\n              primary: \"Login\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 283,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 282,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(ListItem, {\n            button: true,\n            component: Link,\n            to: \"/register\",\n            onClick: handleMobileMenuClose,\n            children: /*#__PURE__*/_jsxDEV(ListItemText, {\n              primary: \"Register\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 286,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 285,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 238,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 203,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n_s(Navbar, \"PtWHiRt+uq/fKolLAMcQiuAXZJc=\", false, function () {\n  return [useNavigate, useAuth, useTheme, useMediaQuery];\n});\n_c = Navbar;\nexport default Navbar;\nvar _c;\n$RefreshReg$(_c, \"Navbar\");", "map": {"version": 3, "names": ["React", "useState", "AppBar", "<PERSON><PERSON><PERSON>", "Typography", "<PERSON><PERSON>", "IconButton", "<PERSON><PERSON>", "MenuItem", "Box", "Avatar", "useTheme", "useMediaQuery", "Drawer", "List", "ListItem", "ListItemIcon", "ListItemText", "Divider", "Home", "Dashboard", "Group", "AccountCircle", "Cha<PERSON>", "Message", "Explore", "TrendingUp", "MenuIcon", "Link", "useNavigate", "useAuth", "NotificationBell", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "<PERSON><PERSON><PERSON>", "_s", "_userProfile$firstNam", "_userProfile$lastName", "_userProfile$firstNam2", "_userProfile$lastName2", "anchorEl", "setAnchorEl", "mobileMenuOpen", "setMobileMenuOpen", "navigate", "currentUser", "userProfile", "logout", "theme", "isMobile", "breakpoints", "down", "handleMenu", "event", "currentTarget", "handleClose", "handleMobileMenuToggle", "handleMobileMenuClose", "handleLogout", "error", "console", "navigationItems", "label", "path", "icon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "children", "position", "elevation", "sx", "mr", "variant", "component", "flexGrow", "display", "alignItems", "gap", "color", "to", "startIcon", "textTransform", "map", "item", "ml", "size", "onClick", "width", "height", "src", "profilePicture", "firstName", "lastName", "id", "anchor<PERSON><PERSON><PERSON>", "vertical", "horizontal", "keepMounted", "transform<PERSON><PERSON>in", "open", "Boolean", "onClose", "borderColor", "backgroundColor", "anchor", "boxSizing", "p", "mb", "bgcolor", "borderRadius", "email", "button", "primary", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/ドキュメント/Projects/StudyHub/studyhub-app/src/components/Navbar.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport {\n  A<PERSON>Bar,\n  <PERSON><PERSON><PERSON>,\n  <PERSON><PERSON><PERSON>,\n  Button,\n  IconButton,\n  Menu,\n  MenuItem,\n  Box,\n  Avatar,\n  useTheme,\n  useMediaQuery,\n  Drawer,\n  List,\n  ListItem,\n  ListItemIcon,\n  ListItemText,\n  Divider,\n} from '@mui/material';\nimport {\n  Home,\n  Dashboard,\n  Group,\n  Forum,\n  AccountCircle,\n  Chat,\n  Message,\n  Explore,\n  TrendingUp,\n  Menu as MenuIcon,\n} from '@mui/icons-material';\nimport { Link, useNavigate } from 'react-router-dom';\nimport { useAuth } from '../contexts/AuthContext';\nimport NotificationBell from './NotificationBell';\n\nconst Navbar: React.FC = () => {\n  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);\n  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);\n  const navigate = useNavigate();\n  const { currentUser, userProfile, logout } = useAuth();\n  const theme = useTheme();\n  const isMobile = useMediaQuery(theme.breakpoints.down('md'));\n\n  const handleMenu = (event: React.MouseEvent<HTMLElement>) => {\n    setAnchorEl(event.currentTarget);\n  };\n\n  const handleClose = () => {\n    setAnchorEl(null);\n  };\n\n  const handleMobileMenuToggle = () => {\n    setMobileMenuOpen(!mobileMenuOpen);\n  };\n\n  const handleMobileMenuClose = () => {\n    setMobileMenuOpen(false);\n  };\n\n  const handleLogout = async () => {\n    try {\n      await logout();\n      handleClose();\n      handleMobileMenuClose();\n      navigate('/');\n    } catch (error) {\n      console.error('Logout error:', error);\n    }\n  };\n\n  const navigationItems = [\n    { label: 'Feed', path: '/feed', icon: <Dashboard /> },\n    { label: 'Explore', path: '/explore', icon: <Explore /> },\n    { label: 'Communities', path: '/communities', icon: <Group /> },\n    { label: 'Trending', path: '/trending', icon: <TrendingUp /> },\n    { label: 'Chat', path: '/chat', icon: <Message /> },\n  ];\n\n  return (\n    <>\n      <AppBar position=\"static\" elevation={1}>\n        <Toolbar>\n          <Chat sx={{ mr: 2 }} />\n          <Typography variant=\"h6\" component=\"div\" sx={{ flexGrow: 1 }}>\n            ChatRoom\n          </Typography>\n\n          {/* Desktop Navigation */}\n          {!isMobile && (\n            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\n              <Button\n                color=\"inherit\"\n                component={Link}\n                to=\"/\"\n                startIcon={<Home />}\n                sx={{ textTransform: 'none' }}\n              >\n                Home\n              </Button>\n\n              {currentUser && navigationItems.map((item) => (\n                <Button\n                  key={item.path}\n                  color=\"inherit\"\n                  component={Link}\n                  to={item.path}\n                  startIcon={item.icon}\n                  sx={{ textTransform: 'none' }}\n                >\n                  {item.label}\n                </Button>\n              ))}\n\n              {currentUser ? (\n                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, ml: 1 }}>\n                  <NotificationBell />\n                  <IconButton\n                    size=\"large\"\n                    aria-label=\"account of current user\"\n                    aria-controls=\"menu-appbar\"\n                    aria-haspopup=\"true\"\n                    onClick={handleMenu}\n                    color=\"inherit\"\n                  >\n                    <Avatar sx={{ width: 32, height: 32 }} src={userProfile?.profilePicture}>\n                      {userProfile?.firstName?.[0]}{userProfile?.lastName?.[0]}\n                    </Avatar>\n                  </IconButton>\n                  <Menu\n                    id=\"menu-appbar\"\n                    anchorEl={anchorEl}\n                    anchorOrigin={{\n                      vertical: 'bottom',\n                      horizontal: 'right',\n                    }}\n                    keepMounted\n                    transformOrigin={{\n                      vertical: 'top',\n                      horizontal: 'right',\n                    }}\n                    open={Boolean(anchorEl)}\n                    onClose={handleClose}\n                  >\n                    <MenuItem onClick={() => { handleClose(); navigate('/profile'); }}>\n                      <AccountCircle sx={{ mr: 1 }} />\n                      Profile\n                    </MenuItem>\n                    <MenuItem onClick={handleLogout}>\n                      Logout\n                    </MenuItem>\n                  </Menu>\n                </Box>\n              ) : (\n                <Box sx={{ display: 'flex', gap: 1, ml: 1 }}>\n                  <Button\n                    color=\"inherit\"\n                    component={Link}\n                    to=\"/login\"\n                    sx={{ textTransform: 'none' }}\n                  >\n                    Login\n                  </Button>\n                  <Button\n                    color=\"inherit\"\n                    component={Link}\n                    to=\"/register\"\n                    variant=\"outlined\"\n                    sx={{\n                      borderColor: 'white',\n                      color: 'white',\n                      textTransform: 'none',\n                      '&:hover': {\n                        borderColor: 'rgba(255, 255, 255, 0.7)',\n                        backgroundColor: 'rgba(255, 255, 255, 0.1)',\n                      }\n                    }}\n                  >\n                    Register\n                  </Button>\n                </Box>\n              )}\n            </Box>\n          )}\n\n          {/* Mobile Navigation */}\n          {isMobile && (\n            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\n              {currentUser && <NotificationBell />}\n              <IconButton\n                color=\"inherit\"\n                aria-label=\"open drawer\"\n                onClick={handleMobileMenuToggle}\n              >\n                <MenuIcon />\n              </IconButton>\n            </Box>\n          )}\n        </Toolbar>\n      </AppBar>\n\n      {/* Mobile Drawer */}\n      <Drawer\n        anchor=\"right\"\n        open={mobileMenuOpen}\n        onClose={handleMobileMenuClose}\n        sx={{\n          '& .MuiDrawer-paper': {\n            width: 280,\n            boxSizing: 'border-box',\n          },\n        }}\n      >\n        <Box sx={{ p: 2 }}>\n          <Typography variant=\"h6\" sx={{ mb: 2 }}>\n            ChatRoom\n          </Typography>\n\n          {currentUser && userProfile && (\n            <Box sx={{ display: 'flex', alignItems: 'center', mb: 2, p: 2, bgcolor: 'grey.100', borderRadius: 1 }}>\n              <Avatar src={userProfile.profilePicture} sx={{ mr: 2 }}>\n                {userProfile.firstName?.[0]}{userProfile.lastName?.[0]}\n              </Avatar>\n              <Box>\n                <Typography variant=\"subtitle2\">\n                  {userProfile.firstName} {userProfile.lastName}\n                </Typography>\n                <Typography variant=\"caption\" color=\"text.secondary\">\n                  {userProfile.email}\n                </Typography>\n              </Box>\n            </Box>\n          )}\n        </Box>\n\n        <Divider />\n\n        <List>\n          <ListItem button component={Link} to=\"/\" onClick={handleMobileMenuClose}>\n            <ListItemIcon>\n              <Home />\n            </ListItemIcon>\n            <ListItemText primary=\"Home\" />\n          </ListItem>\n\n          {currentUser && navigationItems.map((item) => (\n            <ListItem\n              key={item.path}\n              button\n              component={Link}\n              to={item.path}\n              onClick={handleMobileMenuClose}\n            >\n              <ListItemIcon>\n                {item.icon}\n              </ListItemIcon>\n              <ListItemText primary={item.label} />\n            </ListItem>\n          ))}\n\n          {currentUser && (\n            <>\n              <Divider />\n              <ListItem button onClick={() => { navigate('/profile'); handleMobileMenuClose(); }}>\n                <ListItemIcon>\n                  <AccountCircle />\n                </ListItemIcon>\n                <ListItemText primary=\"Profile\" />\n              </ListItem>\n              <ListItem button onClick={handleLogout}>\n                <ListItemIcon>\n                  <AccountCircle />\n                </ListItemIcon>\n                <ListItemText primary=\"Logout\" />\n              </ListItem>\n            </>\n          )}\n\n          {!currentUser && (\n            <>\n              <Divider />\n              <ListItem button component={Link} to=\"/login\" onClick={handleMobileMenuClose}>\n                <ListItemText primary=\"Login\" />\n              </ListItem>\n              <ListItem button component={Link} to=\"/register\" onClick={handleMobileMenuClose}>\n                <ListItemText primary=\"Register\" />\n              </ListItem>\n            </>\n          )}\n        </List>\n      </Drawer>\n    </>\n  );\n};\n\nexport default Navbar;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SACEC,MAAM,EACNC,OAAO,EACPC,UAAU,EACVC,MAAM,EACNC,UAAU,EACVC,IAAI,EACJC,QAAQ,EACRC,GAAG,EACHC,MAAM,EACNC,QAAQ,EACRC,aAAa,EACbC,MAAM,EACNC,IAAI,EACJC,QAAQ,EACRC,YAAY,EACZC,YAAY,EACZC,OAAO,QACF,eAAe;AACtB,SACEC,IAAI,EACJC,SAAS,EACTC,KAAK,EAELC,aAAa,EACbC,IAAI,EACJC,OAAO,EACPC,OAAO,EACPC,UAAU,EACVnB,IAAI,IAAIoB,QAAQ,QACX,qBAAqB;AAC5B,SAASC,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AACpD,SAASC,OAAO,QAAQ,yBAAyB;AACjD,OAAOC,gBAAgB,MAAM,oBAAoB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAElD,MAAMC,MAAgB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,qBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA;EAC7B,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAG1C,QAAQ,CAAqB,IAAI,CAAC;EAClE,MAAM,CAAC2C,cAAc,EAAEC,iBAAiB,CAAC,GAAG5C,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM6C,QAAQ,GAAGjB,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEkB,WAAW;IAAEC,WAAW;IAAEC;EAAO,CAAC,GAAGnB,OAAO,CAAC,CAAC;EACtD,MAAMoB,KAAK,GAAGvC,QAAQ,CAAC,CAAC;EACxB,MAAMwC,QAAQ,GAAGvC,aAAa,CAACsC,KAAK,CAACE,WAAW,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC;EAE5D,MAAMC,UAAU,GAAIC,KAAoC,IAAK;IAC3DZ,WAAW,CAACY,KAAK,CAACC,aAAa,CAAC;EAClC,CAAC;EAED,MAAMC,WAAW,GAAGA,CAAA,KAAM;IACxBd,WAAW,CAAC,IAAI,CAAC;EACnB,CAAC;EAED,MAAMe,sBAAsB,GAAGA,CAAA,KAAM;IACnCb,iBAAiB,CAAC,CAACD,cAAc,CAAC;EACpC,CAAC;EAED,MAAMe,qBAAqB,GAAGA,CAAA,KAAM;IAClCd,iBAAiB,CAAC,KAAK,CAAC;EAC1B,CAAC;EAED,MAAMe,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MACF,MAAMX,MAAM,CAAC,CAAC;MACdQ,WAAW,CAAC,CAAC;MACbE,qBAAqB,CAAC,CAAC;MACvBb,QAAQ,CAAC,GAAG,CAAC;IACf,CAAC,CAAC,OAAOe,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,eAAe,EAAEA,KAAK,CAAC;IACvC;EACF,CAAC;EAED,MAAME,eAAe,GAAG,CACtB;IAAEC,KAAK,EAAE,MAAM;IAAEC,IAAI,EAAE,OAAO;IAAEC,IAAI,eAAEjC,OAAA,CAACb,SAAS;MAAA+C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAE,CAAC,EACrD;IAAEN,KAAK,EAAE,SAAS;IAAEC,IAAI,EAAE,UAAU;IAAEC,IAAI,eAAEjC,OAAA,CAACR,OAAO;MAAA0C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAE,CAAC,EACzD;IAAEN,KAAK,EAAE,aAAa;IAAEC,IAAI,EAAE,cAAc;IAAEC,IAAI,eAAEjC,OAAA,CAACZ,KAAK;MAAA8C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAE,CAAC,EAC/D;IAAEN,KAAK,EAAE,UAAU;IAAEC,IAAI,EAAE,WAAW;IAAEC,IAAI,eAAEjC,OAAA,CAACP,UAAU;MAAAyC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAE,CAAC,EAC9D;IAAEN,KAAK,EAAE,MAAM;IAAEC,IAAI,EAAE,OAAO;IAAEC,IAAI,eAAEjC,OAAA,CAACT,OAAO;MAAA2C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAE,CAAC,CACpD;EAED,oBACErC,OAAA,CAAAE,SAAA;IAAAoC,QAAA,gBACEtC,OAAA,CAAC/B,MAAM;MAACsE,QAAQ,EAAC,QAAQ;MAACC,SAAS,EAAE,CAAE;MAAAF,QAAA,eACrCtC,OAAA,CAAC9B,OAAO;QAAAoE,QAAA,gBACNtC,OAAA,CAACV,IAAI;UAACmD,EAAE,EAAE;YAAEC,EAAE,EAAE;UAAE;QAAE;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACvBrC,OAAA,CAAC7B,UAAU;UAACwE,OAAO,EAAC,IAAI;UAACC,SAAS,EAAC,KAAK;UAACH,EAAE,EAAE;YAAEI,QAAQ,EAAE;UAAE,CAAE;UAAAP,QAAA,EAAC;QAE9D;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,EAGZ,CAACnB,QAAQ,iBACRlB,OAAA,CAACxB,GAAG;UAACiE,EAAE,EAAE;YAAEK,OAAO,EAAE,MAAM;YAAEC,UAAU,EAAE,QAAQ;YAAEC,GAAG,EAAE;UAAE,CAAE;UAAAV,QAAA,gBACzDtC,OAAA,CAAC5B,MAAM;YACL6E,KAAK,EAAC,SAAS;YACfL,SAAS,EAAEjD,IAAK;YAChBuD,EAAE,EAAC,GAAG;YACNC,SAAS,eAAEnD,OAAA,CAACd,IAAI;cAAAgD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACpBI,EAAE,EAAE;cAAEW,aAAa,EAAE;YAAO,CAAE;YAAAd,QAAA,EAC/B;UAED;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,EAERvB,WAAW,IAAIgB,eAAe,CAACuB,GAAG,CAAEC,IAAI,iBACvCtD,OAAA,CAAC5B,MAAM;YAEL6E,KAAK,EAAC,SAAS;YACfL,SAAS,EAAEjD,IAAK;YAChBuD,EAAE,EAAEI,IAAI,CAACtB,IAAK;YACdmB,SAAS,EAAEG,IAAI,CAACrB,IAAK;YACrBQ,EAAE,EAAE;cAAEW,aAAa,EAAE;YAAO,CAAE;YAAAd,QAAA,EAE7BgB,IAAI,CAACvB;UAAK,GAPNuB,IAAI,CAACtB,IAAI;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAQR,CACT,CAAC,EAEDvB,WAAW,gBACVd,OAAA,CAACxB,GAAG;YAACiE,EAAE,EAAE;cAAEK,OAAO,EAAE,MAAM;cAAEC,UAAU,EAAE,QAAQ;cAAEC,GAAG,EAAE,CAAC;cAAEO,EAAE,EAAE;YAAE,CAAE;YAAAjB,QAAA,gBAChEtC,OAAA,CAACF,gBAAgB;cAAAoC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACpBrC,OAAA,CAAC3B,UAAU;cACTmF,IAAI,EAAC,OAAO;cACZ,cAAW,yBAAyB;cACpC,iBAAc,aAAa;cAC3B,iBAAc,MAAM;cACpBC,OAAO,EAAEpC,UAAW;cACpB4B,KAAK,EAAC,SAAS;cAAAX,QAAA,eAEftC,OAAA,CAACvB,MAAM;gBAACgE,EAAE,EAAE;kBAAEiB,KAAK,EAAE,EAAE;kBAAEC,MAAM,EAAE;gBAAG,CAAE;gBAACC,GAAG,EAAE7C,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAE8C,cAAe;gBAAAvB,QAAA,GACrEvB,WAAW,aAAXA,WAAW,wBAAAV,qBAAA,GAAXU,WAAW,CAAE+C,SAAS,cAAAzD,qBAAA,uBAAtBA,qBAAA,CAAyB,CAAC,CAAC,EAAEU,WAAW,aAAXA,WAAW,wBAAAT,qBAAA,GAAXS,WAAW,CAAEgD,QAAQ,cAAAzD,qBAAA,uBAArBA,qBAAA,CAAwB,CAAC,CAAC;cAAA;gBAAA4B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACbrC,OAAA,CAAC1B,IAAI;cACH0F,EAAE,EAAC,aAAa;cAChBvD,QAAQ,EAAEA,QAAS;cACnBwD,YAAY,EAAE;gBACZC,QAAQ,EAAE,QAAQ;gBAClBC,UAAU,EAAE;cACd,CAAE;cACFC,WAAW;cACXC,eAAe,EAAE;gBACfH,QAAQ,EAAE,KAAK;gBACfC,UAAU,EAAE;cACd,CAAE;cACFG,IAAI,EAAEC,OAAO,CAAC9D,QAAQ,CAAE;cACxB+D,OAAO,EAAEhD,WAAY;cAAAc,QAAA,gBAErBtC,OAAA,CAACzB,QAAQ;gBAACkF,OAAO,EAAEA,CAAA,KAAM;kBAAEjC,WAAW,CAAC,CAAC;kBAAEX,QAAQ,CAAC,UAAU,CAAC;gBAAE,CAAE;gBAAAyB,QAAA,gBAChEtC,OAAA,CAACX,aAAa;kBAACoD,EAAE,EAAE;oBAAEC,EAAE,EAAE;kBAAE;gBAAE;kBAAAR,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,WAElC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eACXrC,OAAA,CAACzB,QAAQ;gBAACkF,OAAO,EAAE9B,YAAa;gBAAAW,QAAA,EAAC;cAEjC;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACP,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,gBAENrC,OAAA,CAACxB,GAAG;YAACiE,EAAE,EAAE;cAAEK,OAAO,EAAE,MAAM;cAAEE,GAAG,EAAE,CAAC;cAAEO,EAAE,EAAE;YAAE,CAAE;YAAAjB,QAAA,gBAC1CtC,OAAA,CAAC5B,MAAM;cACL6E,KAAK,EAAC,SAAS;cACfL,SAAS,EAAEjD,IAAK;cAChBuD,EAAE,EAAC,QAAQ;cACXT,EAAE,EAAE;gBAAEW,aAAa,EAAE;cAAO,CAAE;cAAAd,QAAA,EAC/B;YAED;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTrC,OAAA,CAAC5B,MAAM;cACL6E,KAAK,EAAC,SAAS;cACfL,SAAS,EAAEjD,IAAK;cAChBuD,EAAE,EAAC,WAAW;cACdP,OAAO,EAAC,UAAU;cAClBF,EAAE,EAAE;gBACFgC,WAAW,EAAE,OAAO;gBACpBxB,KAAK,EAAE,OAAO;gBACdG,aAAa,EAAE,MAAM;gBACrB,SAAS,EAAE;kBACTqB,WAAW,EAAE,0BAA0B;kBACvCC,eAAe,EAAE;gBACnB;cACF,CAAE;cAAApC,QAAA,EACH;YAED;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CACN,EAGAnB,QAAQ,iBACPlB,OAAA,CAACxB,GAAG;UAACiE,EAAE,EAAE;YAAEK,OAAO,EAAE,MAAM;YAAEC,UAAU,EAAE,QAAQ;YAAEC,GAAG,EAAE;UAAE,CAAE;UAAAV,QAAA,GACxDxB,WAAW,iBAAId,OAAA,CAACF,gBAAgB;YAAAoC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACpCrC,OAAA,CAAC3B,UAAU;YACT4E,KAAK,EAAC,SAAS;YACf,cAAW,aAAa;YACxBQ,OAAO,EAAEhC,sBAAuB;YAAAa,QAAA,eAEhCtC,OAAA,CAACN,QAAQ;cAAAwC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eAGTrC,OAAA,CAACpB,MAAM;MACL+F,MAAM,EAAC,OAAO;MACdL,IAAI,EAAE3D,cAAe;MACrB6D,OAAO,EAAE9C,qBAAsB;MAC/Be,EAAE,EAAE;QACF,oBAAoB,EAAE;UACpBiB,KAAK,EAAE,GAAG;UACVkB,SAAS,EAAE;QACb;MACF,CAAE;MAAAtC,QAAA,gBAEFtC,OAAA,CAACxB,GAAG;QAACiE,EAAE,EAAE;UAAEoC,CAAC,EAAE;QAAE,CAAE;QAAAvC,QAAA,gBAChBtC,OAAA,CAAC7B,UAAU;UAACwE,OAAO,EAAC,IAAI;UAACF,EAAE,EAAE;YAAEqC,EAAE,EAAE;UAAE,CAAE;UAAAxC,QAAA,EAAC;QAExC;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,EAEZvB,WAAW,IAAIC,WAAW,iBACzBf,OAAA,CAACxB,GAAG;UAACiE,EAAE,EAAE;YAAEK,OAAO,EAAE,MAAM;YAAEC,UAAU,EAAE,QAAQ;YAAE+B,EAAE,EAAE,CAAC;YAAED,CAAC,EAAE,CAAC;YAAEE,OAAO,EAAE,UAAU;YAAEC,YAAY,EAAE;UAAE,CAAE;UAAA1C,QAAA,gBACpGtC,OAAA,CAACvB,MAAM;YAACmF,GAAG,EAAE7C,WAAW,CAAC8C,cAAe;YAACpB,EAAE,EAAE;cAAEC,EAAE,EAAE;YAAE,CAAE;YAAAJ,QAAA,IAAA/B,sBAAA,GACpDQ,WAAW,CAAC+C,SAAS,cAAAvD,sBAAA,uBAArBA,sBAAA,CAAwB,CAAC,CAAC,GAAAC,sBAAA,GAAEO,WAAW,CAACgD,QAAQ,cAAAvD,sBAAA,uBAApBA,sBAAA,CAAuB,CAAC,CAAC;UAAA;YAAA0B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChD,CAAC,eACTrC,OAAA,CAACxB,GAAG;YAAA8D,QAAA,gBACFtC,OAAA,CAAC7B,UAAU;cAACwE,OAAO,EAAC,WAAW;cAAAL,QAAA,GAC5BvB,WAAW,CAAC+C,SAAS,EAAC,GAAC,EAAC/C,WAAW,CAACgD,QAAQ;YAAA;cAAA7B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnC,CAAC,eACbrC,OAAA,CAAC7B,UAAU;cAACwE,OAAO,EAAC,SAAS;cAACM,KAAK,EAAC,gBAAgB;cAAAX,QAAA,EACjDvB,WAAW,CAACkE;YAAK;cAAA/C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAENrC,OAAA,CAACf,OAAO;QAAAiD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAEXrC,OAAA,CAACnB,IAAI;QAAAyD,QAAA,gBACHtC,OAAA,CAAClB,QAAQ;UAACoG,MAAM;UAACtC,SAAS,EAAEjD,IAAK;UAACuD,EAAE,EAAC,GAAG;UAACO,OAAO,EAAE/B,qBAAsB;UAAAY,QAAA,gBACtEtC,OAAA,CAACjB,YAAY;YAAAuD,QAAA,eACXtC,OAAA,CAACd,IAAI;cAAAgD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC,eACfrC,OAAA,CAAChB,YAAY;YAACmG,OAAO,EAAC;UAAM;YAAAjD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvB,CAAC,EAEVvB,WAAW,IAAIgB,eAAe,CAACuB,GAAG,CAAEC,IAAI,iBACvCtD,OAAA,CAAClB,QAAQ;UAEPoG,MAAM;UACNtC,SAAS,EAAEjD,IAAK;UAChBuD,EAAE,EAAEI,IAAI,CAACtB,IAAK;UACdyB,OAAO,EAAE/B,qBAAsB;UAAAY,QAAA,gBAE/BtC,OAAA,CAACjB,YAAY;YAAAuD,QAAA,EACVgB,IAAI,CAACrB;UAAI;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACfrC,OAAA,CAAChB,YAAY;YAACmG,OAAO,EAAE7B,IAAI,CAACvB;UAAM;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA,GAThCiB,IAAI,CAACtB,IAAI;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAUN,CACX,CAAC,EAEDvB,WAAW,iBACVd,OAAA,CAAAE,SAAA;UAAAoC,QAAA,gBACEtC,OAAA,CAACf,OAAO;YAAAiD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACXrC,OAAA,CAAClB,QAAQ;YAACoG,MAAM;YAACzB,OAAO,EAAEA,CAAA,KAAM;cAAE5C,QAAQ,CAAC,UAAU,CAAC;cAAEa,qBAAqB,CAAC,CAAC;YAAE,CAAE;YAAAY,QAAA,gBACjFtC,OAAA,CAACjB,YAAY;cAAAuD,QAAA,eACXtC,OAAA,CAACX,aAAa;gBAAA6C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eACfrC,OAAA,CAAChB,YAAY;cAACmG,OAAO,EAAC;YAAS;cAAAjD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1B,CAAC,eACXrC,OAAA,CAAClB,QAAQ;YAACoG,MAAM;YAACzB,OAAO,EAAE9B,YAAa;YAAAW,QAAA,gBACrCtC,OAAA,CAACjB,YAAY;cAAAuD,QAAA,eACXtC,OAAA,CAACX,aAAa;gBAAA6C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eACfrC,OAAA,CAAChB,YAAY;cAACmG,OAAO,EAAC;YAAQ;cAAAjD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzB,CAAC;QAAA,eACX,CACH,EAEA,CAACvB,WAAW,iBACXd,OAAA,CAAAE,SAAA;UAAAoC,QAAA,gBACEtC,OAAA,CAACf,OAAO;YAAAiD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACXrC,OAAA,CAAClB,QAAQ;YAACoG,MAAM;YAACtC,SAAS,EAAEjD,IAAK;YAACuD,EAAE,EAAC,QAAQ;YAACO,OAAO,EAAE/B,qBAAsB;YAAAY,QAAA,eAC3EtC,OAAA,CAAChB,YAAY;cAACmG,OAAO,EAAC;YAAO;cAAAjD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxB,CAAC,eACXrC,OAAA,CAAClB,QAAQ;YAACoG,MAAM;YAACtC,SAAS,EAAEjD,IAAK;YAACuD,EAAE,EAAC,WAAW;YAACO,OAAO,EAAE/B,qBAAsB;YAAAY,QAAA,eAC9EtC,OAAA,CAAChB,YAAY;cAACmG,OAAO,EAAC;YAAU;cAAAjD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3B,CAAC;QAAA,eACX,CACH;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC;EAAA,eACT,CAAC;AAEP,CAAC;AAACjC,EAAA,CAjQID,MAAgB;EAAA,QAGHP,WAAW,EACiBC,OAAO,EACtCnB,QAAQ,EACLC,aAAa;AAAA;AAAAyG,EAAA,GAN1BjF,MAAgB;AAmQtB,eAAeA,MAAM;AAAC,IAAAiF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}