import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>rid,
  Card,
  CardContent,
  CardActions,
  Typography,
  Button,
  TextField,
  Box,
  Avatar,
  IconButton,
  Fab,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Chip,
  Paper,
  Divider,
} from '@mui/material';
import {
  Add,
  Favorite,
  FavoriteBorder,
  Comment,
  Share,
  MoreVert,
  Image as ImageIcon,
  Send,
} from '@mui/icons-material';
import { useAuth } from '../contexts/AuthContext';

interface Post {
  id: string;
  authorId: string;
  authorName: string;
  authorAvatar?: string;
  content: string;
  imageUrl?: string;
  timestamp: Date;
  likes: number;
  comments: number;
  isLiked: boolean;
  tags: string[];
}

interface Comment {
  id: string;
  authorId: string;
  authorName: string;
  authorAvatar?: string;
  content: string;
  timestamp: Date;
}

const Feed: React.FC = () => {
  const { currentUser, userProfile } = useAuth();
  const [posts, setPosts] = useState<Post[]>([]);
  const [newPostContent, setNewPostContent] = useState('');
  const [createPostOpen, setCreatePostOpen] = useState(false);
  const [selectedTags, setSelectedTags] = useState<string[]>([]);
  const [loading, setLoading] = useState(false);

  // Sample data for demonstration
  useEffect(() => {
    const samplePosts: Post[] = [
      {
        id: '1',
        authorId: 'user1',
        authorName: 'Alice Johnson',
        authorAvatar: '',
        content: 'Just had an amazing discussion about climate change solutions! What are your thoughts on renewable energy adoption?',
        timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2 hours ago
        likes: 24,
        comments: 8,
        isLiked: false,
        tags: ['climate', 'environment', 'discussion'],
      },
      {
        id: '2',
        authorId: 'user2',
        authorName: 'Bob Smith',
        authorAvatar: '',
        content: 'The future of AI is fascinating! I believe we\'re on the verge of breakthrough innovations that will change everything.',
        timestamp: new Date(Date.now() - 4 * 60 * 60 * 1000), // 4 hours ago
        likes: 42,
        comments: 15,
        isLiked: true,
        tags: ['AI', 'technology', 'future'],
      },
      {
        id: '3',
        authorId: 'user3',
        authorName: 'Carol Davis',
        authorAvatar: '',
        content: 'Sharing my thoughts on work-life balance. It\'s crucial to set boundaries and prioritize mental health.',
        timestamp: new Date(Date.now() - 6 * 60 * 60 * 1000), // 6 hours ago
        likes: 18,
        comments: 6,
        isLiked: false,
        tags: ['wellness', 'work', 'mentalhealth'],
      },
    ];
    setPosts(samplePosts);
  }, []);

  const handleCreatePost = () => {
    if (!newPostContent.trim()) return;

    const newPost: Post = {
      id: Date.now().toString(),
      authorId: currentUser?.uid || '',
      authorName: userProfile?.firstName + ' ' + userProfile?.lastName || 'Anonymous',
      authorAvatar: userProfile?.profilePicture || '',
      content: newPostContent,
      timestamp: new Date(),
      likes: 0,
      comments: 0,
      isLiked: false,
      tags: selectedTags,
    };

    setPosts([newPost, ...posts]);
    setNewPostContent('');
    setSelectedTags([]);
    setCreatePostOpen(false);
  };

  const handleLike = (postId: string) => {
    setPosts(posts.map(post => 
      post.id === postId 
        ? { 
            ...post, 
            isLiked: !post.isLiked, 
            likes: post.isLiked ? post.likes - 1 : post.likes + 1 
          }
        : post
    ));
  };

  const formatTimeAgo = (timestamp: Date) => {
    const now = new Date();
    const diffInHours = Math.floor((now.getTime() - timestamp.getTime()) / (1000 * 60 * 60));
    
    if (diffInHours < 1) return 'Just now';
    if (diffInHours < 24) return `${diffInHours}h ago`;
    return `${Math.floor(diffInHours / 24)}d ago`;
  };

  const availableTags = ['technology', 'AI', 'climate', 'environment', 'wellness', 'work', 'mentalhealth', 'discussion', 'future', 'innovation'];

  return (
    <Container maxWidth="md">
      <Box sx={{ py: 3 }}>
        <Typography variant="h4" gutterBottom>
          Your Feed
        </Typography>
        <Typography variant="body1" color="text.secondary" sx={{ mb: 4 }}>
          Share your thoughts and discover what others are talking about
        </Typography>

        {/* Create Post Section */}
        {currentUser && (
          <Paper sx={{ p: 3, mb: 3 }}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
              <Avatar src={userProfile?.profilePicture}>
                {userProfile?.firstName?.[0]}{userProfile?.lastName?.[0]}
              </Avatar>
              <TextField
                fullWidth
                placeholder="What's on your mind?"
                variant="outlined"
                onClick={() => setCreatePostOpen(true)}
                sx={{ cursor: 'pointer' }}
                InputProps={{
                  readOnly: true,
                }}
              />
            </Box>
          </Paper>
        )}

        {/* Posts */}
        <Grid container spacing={3}>
          {posts.map((post) => (
            <Grid item xs={12} key={post.id}>
              <Card>
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <Avatar src={post.authorAvatar} sx={{ mr: 2 }}>
                      {post.authorName.split(' ').map(n => n[0]).join('')}
                    </Avatar>
                    <Box sx={{ flexGrow: 1 }}>
                      <Typography variant="subtitle1" fontWeight="bold">
                        {post.authorName}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        {formatTimeAgo(post.timestamp)}
                      </Typography>
                    </Box>
                    <IconButton>
                      <MoreVert />
                    </IconButton>
                  </Box>

                  <Typography variant="body1" sx={{ mb: 2 }}>
                    {post.content}
                  </Typography>

                  {post.tags.length > 0 && (
                    <Box sx={{ mb: 2 }}>
                      {post.tags.map((tag) => (
                        <Chip
                          key={tag}
                          label={`#${tag}`}
                          size="small"
                          sx={{ mr: 1, mb: 1 }}
                          color="primary"
                          variant="outlined"
                        />
                      ))}
                    </Box>
                  )}
                </CardContent>

                <Divider />

                <CardActions sx={{ justifyContent: 'space-between', px: 2 }}>
                  <Box sx={{ display: 'flex', gap: 1 }}>
                    <Button
                      startIcon={post.isLiked ? <Favorite color="error" /> : <FavoriteBorder />}
                      onClick={() => handleLike(post.id)}
                      color={post.isLiked ? "error" : "inherit"}
                    >
                      {post.likes}
                    </Button>
                    <Button startIcon={<Comment />}>
                      {post.comments}
                    </Button>
                    <Button startIcon={<Share />}>
                      Share
                    </Button>
                  </Box>
                </CardActions>
              </Card>
            </Grid>
          ))}
        </Grid>

        {/* Floating Action Button */}
        {currentUser && (
          <Fab
            color="primary"
            aria-label="create post"
            sx={{ position: 'fixed', bottom: 16, right: 16 }}
            onClick={() => setCreatePostOpen(true)}
          >
            <Add />
          </Fab>
        )}

        {/* Create Post Dialog */}
        <Dialog open={createPostOpen} onClose={() => setCreatePostOpen(false)} maxWidth="sm" fullWidth>
          <DialogTitle>Create New Post</DialogTitle>
          <DialogContent>
            <TextField
              autoFocus
              margin="dense"
              label="What's on your mind?"
              fullWidth
              multiline
              rows={4}
              variant="outlined"
              value={newPostContent}
              onChange={(e) => setNewPostContent(e.target.value)}
              sx={{ mb: 2 }}
            />
            
            <Typography variant="subtitle2" gutterBottom>
              Add Tags:
            </Typography>
            <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mb: 2 }}>
              {availableTags.map((tag) => (
                <Chip
                  key={tag}
                  label={`#${tag}`}
                  clickable
                  color={selectedTags.includes(tag) ? "primary" : "default"}
                  onClick={() => {
                    setSelectedTags(prev => 
                      prev.includes(tag) 
                        ? prev.filter(t => t !== tag)
                        : [...prev, tag]
                    );
                  }}
                />
              ))}
            </Box>
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setCreatePostOpen(false)}>Cancel</Button>
            <Button 
              onClick={handleCreatePost} 
              variant="contained"
              disabled={!newPostContent.trim()}
              startIcon={<Send />}
            >
              Post
            </Button>
          </DialogActions>
        </Dialog>
      </Box>
    </Container>
  );
};

export default Feed;
