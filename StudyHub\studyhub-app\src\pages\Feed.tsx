import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON><PERSON>,
  Grid,
  Card,
  CardContent,
  CardActions,
  Typo<PERSON>,
  Button,
  TextField,
  Box,
  Avatar,
  IconButton,
  Fab,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Chip,
  Paper,
  Divider,
  CircularProgress,
  Alert,
} from '@mui/material';
import {
  Add,
  Favorite,
  FavoriteBorder,
  Comment,
  Share,
  MoreVert,
  Send,
} from '@mui/icons-material';
import { useAuth } from '../contexts/AuthContext';
import { PostService, Post } from '../services/postService';

// Post interface is now imported from PostService

const Feed: React.FC = () => {
  const { currentUser, userProfile } = useAuth();
  const [posts, setPosts] = useState<Post[]>([]);
  const [newPostContent, setNewPostContent] = useState('');
  const [createPostOpen, setCreatePostOpen] = useState(false);
  const [selectedTags, setSelectedTags] = useState<string[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [posting, setPosting] = useState(false);

  // Load posts from Firebase
  useEffect(() => {
    setLoading(true);
    setError(null);

    // Subscribe to real-time posts updates
    const unsubscribe = PostService.subscribeToPosts((fetchedPosts) => {
      setPosts(fetchedPosts);
      setLoading(false);
    });

    // Cleanup subscription on unmount
    return () => unsubscribe();
  }, []);

  const handleCreatePost = async () => {
    if (!newPostContent.trim() || !currentUser) return;

    setPosting(true);
    setError(null);

    try {
      const postData = {
        authorId: currentUser.uid,
        authorName: `${userProfile?.firstName || ''} ${userProfile?.lastName || ''}`.trim() || 'Anonymous',
        authorAvatar: userProfile?.profilePicture || '',
        content: newPostContent.trim(),
        tags: selectedTags,
      };

      await PostService.createPost(postData);

      // Clear form
      setNewPostContent('');
      setSelectedTags([]);
      setCreatePostOpen(false);
    } catch (error) {
      console.error('Error creating post:', error);
      setError('Failed to create post. Please try again.');
    } finally {
      setPosting(false);
    }
  };

  const handleLike = async (postId: string) => {
    if (!currentUser) return;

    try {
      const post = posts.find(p => p.id === postId);
      if (!post) return;

      const isLiked = post.likes.includes(currentUser.uid);

      if (isLiked) {
        await PostService.unlikePost(postId, currentUser.uid);
      } else {
        await PostService.likePost(postId, currentUser.uid);
      }
      // The UI will update automatically through the real-time subscription
    } catch (error) {
      console.error('Error toggling like:', error);
      setError('Failed to update like. Please try again.');
    }
  };

  const formatTimeAgo = (timestamp: Date) => {
    const now = new Date();
    const diffInHours = Math.floor((now.getTime() - timestamp.getTime()) / (1000 * 60 * 60));
    
    if (diffInHours < 1) return 'Just now';
    if (diffInHours < 24) return `${diffInHours}h ago`;
    return `${Math.floor(diffInHours / 24)}d ago`;
  };

  const availableTags = ['technology', 'AI', 'climate', 'environment', 'wellness', 'work', 'mentalhealth', 'discussion', 'future', 'innovation'];

  return (
    <Container maxWidth="md">
      <Box sx={{ py: 3 }}>
        <Typography variant="h4" gutterBottom>
          Your Feed
        </Typography>
        <Typography variant="body1" color="text.secondary" sx={{ mb: 4 }}>
          Share your thoughts and discover what others are talking about
        </Typography>

        {/* Error Alert */}
        {error && (
          <Alert severity="error" sx={{ mb: 3 }} onClose={() => setError(null)}>
            {error}
          </Alert>
        )}

        {/* Create Post Section */}
        {currentUser && (
          <Paper sx={{ p: 3, mb: 3 }}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
              <Avatar src={userProfile?.profilePicture}>
                {userProfile?.firstName?.[0]}{userProfile?.lastName?.[0]}
              </Avatar>
              <TextField
                fullWidth
                placeholder="What's on your mind?"
                variant="outlined"
                onClick={() => setCreatePostOpen(true)}
                sx={{ cursor: 'pointer' }}
                InputProps={{
                  readOnly: true,
                }}
              />
            </Box>
          </Paper>
        )}

        {/* Loading State */}
        {loading && (
          <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
            <CircularProgress />
          </Box>
        )}

        {/* Posts */}
        {!loading && (
          <Grid container spacing={3}>
            {posts.length === 0 ? (
              <Grid item xs={12}>
                <Paper sx={{ p: 4, textAlign: 'center' }}>
                  <Typography variant="h6" color="text.secondary">
                    No posts yet
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Be the first to share your thoughts!
                  </Typography>
                </Paper>
              </Grid>
            ) : (
              posts.map((post) => {
                const isLiked = currentUser ? post.likes.includes(currentUser.uid) : false;
                const likesCount = post.likes.length;

                return (
                  <Grid item xs={12} key={post.id}>
                    <Card>
                      <CardContent>
                        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                          <Avatar src={post.authorAvatar} sx={{ mr: 2 }}>
                            {post.authorName.split(' ').map(n => n[0]).join('')}
                          </Avatar>
                          <Box sx={{ flexGrow: 1 }}>
                            <Typography variant="subtitle1" fontWeight="bold">
                              {post.authorName}
                            </Typography>
                            <Typography variant="caption" color="text.secondary">
                              {formatTimeAgo(post.timestamp)}
                            </Typography>
                          </Box>
                          <IconButton>
                            <MoreVert />
                          </IconButton>
                        </Box>

                        <Typography variant="body1" sx={{ mb: 2 }}>
                          {post.content}
                        </Typography>

                        {post.tags.length > 0 && (
                          <Box sx={{ mb: 2 }}>
                            {post.tags.map((tag) => (
                              <Chip
                                key={tag}
                                label={`#${tag}`}
                                size="small"
                                sx={{ mr: 1, mb: 1 }}
                                color="primary"
                                variant="outlined"
                              />
                            ))}
                          </Box>
                        )}
                      </CardContent>

                      <Divider />

                      <CardActions sx={{ justifyContent: 'space-between', px: 2 }}>
                        <Box sx={{ display: 'flex', gap: 1 }}>
                          <Button
                            startIcon={isLiked ? <Favorite color="error" /> : <FavoriteBorder />}
                            onClick={() => handleLike(post.id)}
                            color={isLiked ? "error" : "inherit"}
                            disabled={!currentUser}
                          >
                            {likesCount}
                          </Button>
                          <Button startIcon={<Comment />}>
                            {post.comments}
                          </Button>
                          <Button startIcon={<Share />}>
                            Share
                          </Button>
                        </Box>
                      </CardActions>
                    </Card>
                  </Grid>
                );
              })
            )}
          </Grid>
        )}

        {/* Floating Action Button */}
        {currentUser && (
          <Fab
            color="primary"
            aria-label="create post"
            sx={{ position: 'fixed', bottom: 16, right: 16 }}
            onClick={() => setCreatePostOpen(true)}
          >
            <Add />
          </Fab>
        )}

        {/* Create Post Dialog */}
        <Dialog open={createPostOpen} onClose={() => setCreatePostOpen(false)} maxWidth="sm" fullWidth>
          <DialogTitle>Create New Post</DialogTitle>
          <DialogContent>
            <TextField
              autoFocus
              margin="dense"
              label="What's on your mind?"
              fullWidth
              multiline
              rows={4}
              variant="outlined"
              value={newPostContent}
              onChange={(e) => setNewPostContent(e.target.value)}
              sx={{ mb: 2 }}
            />
            
            <Typography variant="subtitle2" gutterBottom>
              Add Tags:
            </Typography>
            <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mb: 2 }}>
              {availableTags.map((tag) => (
                <Chip
                  key={tag}
                  label={`#${tag}`}
                  clickable
                  color={selectedTags.includes(tag) ? "primary" : "default"}
                  onClick={() => {
                    setSelectedTags(prev => 
                      prev.includes(tag) 
                        ? prev.filter(t => t !== tag)
                        : [...prev, tag]
                    );
                  }}
                />
              ))}
            </Box>
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setCreatePostOpen(false)}>Cancel</Button>
            <Button
              onClick={handleCreatePost}
              variant="contained"
              disabled={!newPostContent.trim() || posting}
              startIcon={posting ? <CircularProgress size={20} /> : <Send />}
            >
              {posting ? 'Posting...' : 'Post'}
            </Button>
          </DialogActions>
        </Dialog>
      </Box>
    </Container>
  );
};

export default Feed;
