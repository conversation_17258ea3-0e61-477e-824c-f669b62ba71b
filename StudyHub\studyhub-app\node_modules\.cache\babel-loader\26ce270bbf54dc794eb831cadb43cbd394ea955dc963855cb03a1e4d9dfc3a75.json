{"ast": null, "code": "/* eslint-disable import/prefer-default-export */\nexport function createGetColorSchemeSelector(selector) {\n  return function getColorSchemeSelector(colorScheme) {\n    if (selector === 'media') {\n      if (process.env.NODE_ENV !== 'production') {\n        if (colorScheme !== 'light' && colorScheme !== 'dark') {\n          console.error(\"MUI: @media (prefers-color-scheme) supports only 'light' or 'dark', but receive '\".concat(colorScheme, \"'.\"));\n        }\n      }\n      return \"@media (prefers-color-scheme: \".concat(colorScheme, \")\");\n    }\n    if (selector) {\n      if (selector.startsWith('data-') && !selector.includes('%s')) {\n        return \"[\".concat(selector, \"=\\\"\").concat(colorScheme, \"\\\"] &\");\n      }\n      if (selector === 'class') {\n        return \".\".concat(colorScheme, \" &\");\n      }\n      if (selector === 'data') {\n        return \"[data-\".concat(colorScheme, \"] &\");\n      }\n      return \"\".concat(selector.replace('%s', colorScheme), \" &\");\n    }\n    return '&';\n  };\n}", "map": {"version": 3, "names": ["createGetColorSchemeSelector", "selector", "getColorSchemeSelector", "colorScheme", "process", "env", "NODE_ENV", "console", "error", "concat", "startsWith", "includes", "replace"], "sources": ["C:/Users/<USER>/OneDrive/ドキュメント/Projects/StudyHub/studyhub-app/node_modules/@mui/system/esm/cssVars/getColorSchemeSelector.js"], "sourcesContent": ["/* eslint-disable import/prefer-default-export */\nexport function createGetColorSchemeSelector(selector) {\n  return function getColorSchemeSelector(colorScheme) {\n    if (selector === 'media') {\n      if (process.env.NODE_ENV !== 'production') {\n        if (colorScheme !== 'light' && colorScheme !== 'dark') {\n          console.error(`MUI: @media (prefers-color-scheme) supports only 'light' or 'dark', but receive '${colorScheme}'.`);\n        }\n      }\n      return `@media (prefers-color-scheme: ${colorScheme})`;\n    }\n    if (selector) {\n      if (selector.startsWith('data-') && !selector.includes('%s')) {\n        return `[${selector}=\"${colorScheme}\"] &`;\n      }\n      if (selector === 'class') {\n        return `.${colorScheme} &`;\n      }\n      if (selector === 'data') {\n        return `[data-${colorScheme}] &`;\n      }\n      return `${selector.replace('%s', colorScheme)} &`;\n    }\n    return '&';\n  };\n}"], "mappings": "AAAA;AACA,OAAO,SAASA,4BAA4BA,CAACC,QAAQ,EAAE;EACrD,OAAO,SAASC,sBAAsBA,CAACC,WAAW,EAAE;IAClD,IAAIF,QAAQ,KAAK,OAAO,EAAE;MACxB,IAAIG,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;QACzC,IAAIH,WAAW,KAAK,OAAO,IAAIA,WAAW,KAAK,MAAM,EAAE;UACrDI,OAAO,CAACC,KAAK,qFAAAC,MAAA,CAAqFN,WAAW,OAAI,CAAC;QACpH;MACF;MACA,wCAAAM,MAAA,CAAwCN,WAAW;IACrD;IACA,IAAIF,QAAQ,EAAE;MACZ,IAAIA,QAAQ,CAACS,UAAU,CAAC,OAAO,CAAC,IAAI,CAACT,QAAQ,CAACU,QAAQ,CAAC,IAAI,CAAC,EAAE;QAC5D,WAAAF,MAAA,CAAWR,QAAQ,SAAAQ,MAAA,CAAKN,WAAW;MACrC;MACA,IAAIF,QAAQ,KAAK,OAAO,EAAE;QACxB,WAAAQ,MAAA,CAAWN,WAAW;MACxB;MACA,IAAIF,QAAQ,KAAK,MAAM,EAAE;QACvB,gBAAAQ,MAAA,CAAgBN,WAAW;MAC7B;MACA,UAAAM,MAAA,CAAUR,QAAQ,CAACW,OAAO,CAAC,IAAI,EAAET,WAAW,CAAC;IAC/C;IACA,OAAO,GAAG;EACZ,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}