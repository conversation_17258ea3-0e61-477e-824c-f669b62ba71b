{"ast": null, "code": "import _objectSpread from \"C:/Users/<USER>/OneDrive/\\u30C9\\u30AD\\u30E5\\u30E1\\u30F3\\u30C8/Projects/StudyHub/studyhub-app/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nimport _objectWithoutProperties from \"C:/Users/<USER>/OneDrive/\\u30C9\\u30AD\\u30E5\\u30E1\\u30F3\\u30C8/Projects/StudyHub/studyhub-app/node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js\";\nconst _excluded = [\"variants\"],\n  _excluded2 = [\"name\", \"slot\", \"skipVariantsResolver\", \"skipSx\", \"overridesResolver\"];\nimport styledEngineStyled, { internal_mutateStyles as mutateStyles, internal_serializeStyles as serializeStyles } from '@mui/styled-engine';\nimport { isPlainObject } from '@mui/utils/deepmerge';\nimport capitalize from '@mui/utils/capitalize';\nimport getDisplayName from '@mui/utils/getDisplayName';\nimport createTheme from \"../createTheme/index.js\";\nimport styleFunctionSx from \"../styleFunctionSx/index.js\";\nimport preprocessStyles from \"../preprocessStyles.js\";\n\n/* eslint-disable no-underscore-dangle */\n/* eslint-disable no-labels */\n/* eslint-disable no-lone-blocks */\n\nexport const systemDefaultTheme = createTheme();\n\n// Update /system/styled/#api in case if this changes\nexport function shouldForwardProp(prop) {\n  return prop !== 'ownerState' && prop !== 'theme' && prop !== 'sx' && prop !== 'as';\n}\nfunction shallowLayer(serialized, layerName) {\n  if (layerName && serialized && typeof serialized === 'object' && serialized.styles && !serialized.styles.startsWith('@layer') // only add the layer if it is not already there.\n  ) {\n    serialized.styles = \"@layer \".concat(layerName, \"{\").concat(String(serialized.styles), \"}\");\n  }\n  return serialized;\n}\nfunction defaultOverridesResolver(slot) {\n  if (!slot) {\n    return null;\n  }\n  return (_props, styles) => styles[slot];\n}\nfunction attachTheme(props, themeId, defaultTheme) {\n  props.theme = isObjectEmpty(props.theme) ? defaultTheme : props.theme[themeId] || props.theme;\n}\nfunction processStyle(props, style, layerName) {\n  /*\n   * Style types:\n   *  - null/undefined\n   *  - string\n   *  - CSS style object: { [cssKey]: [cssValue], variants }\n   *  - Processed style object: { style, variants, isProcessed: true }\n   *  - Array of any of the above\n   */\n\n  const resolvedStyle = typeof style === 'function' ? style(props) : style;\n  if (Array.isArray(resolvedStyle)) {\n    return resolvedStyle.flatMap(subStyle => processStyle(props, subStyle, layerName));\n  }\n  if (Array.isArray(resolvedStyle === null || resolvedStyle === void 0 ? void 0 : resolvedStyle.variants)) {\n    let rootStyle;\n    if (resolvedStyle.isProcessed) {\n      rootStyle = layerName ? shallowLayer(resolvedStyle.style, layerName) : resolvedStyle.style;\n    } else {\n      const {\n          variants\n        } = resolvedStyle,\n        otherStyles = _objectWithoutProperties(resolvedStyle, _excluded);\n      rootStyle = layerName ? shallowLayer(serializeStyles(otherStyles), layerName) : otherStyles;\n    }\n    return processStyleVariants(props, resolvedStyle.variants, [rootStyle], layerName);\n  }\n  if (resolvedStyle !== null && resolvedStyle !== void 0 && resolvedStyle.isProcessed) {\n    return layerName ? shallowLayer(serializeStyles(resolvedStyle.style), layerName) : resolvedStyle.style;\n  }\n  return layerName ? shallowLayer(serializeStyles(resolvedStyle), layerName) : resolvedStyle;\n}\nfunction processStyleVariants(props, variants) {\n  let results = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : [];\n  let layerName = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : undefined;\n  let mergedState; // We might not need it, initialized lazily\n\n  variantLoop: for (let i = 0; i < variants.length; i += 1) {\n    const variant = variants[i];\n    if (typeof variant.props === 'function') {\n      mergedState !== null && mergedState !== void 0 ? mergedState : mergedState = _objectSpread(_objectSpread(_objectSpread({}, props), props.ownerState), {}, {\n        ownerState: props.ownerState\n      });\n      if (!variant.props(mergedState)) {\n        continue;\n      }\n    } else {\n      for (const key in variant.props) {\n        var _props$ownerState;\n        if (props[key] !== variant.props[key] && ((_props$ownerState = props.ownerState) === null || _props$ownerState === void 0 ? void 0 : _props$ownerState[key]) !== variant.props[key]) {\n          continue variantLoop;\n        }\n      }\n    }\n    if (typeof variant.style === 'function') {\n      mergedState !== null && mergedState !== void 0 ? mergedState : mergedState = _objectSpread(_objectSpread(_objectSpread({}, props), props.ownerState), {}, {\n        ownerState: props.ownerState\n      });\n      results.push(layerName ? shallowLayer(serializeStyles(variant.style(mergedState)), layerName) : variant.style(mergedState));\n    } else {\n      results.push(layerName ? shallowLayer(serializeStyles(variant.style), layerName) : variant.style);\n    }\n  }\n  return results;\n}\nexport default function createStyled() {\n  let input = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n  const {\n    themeId,\n    defaultTheme = systemDefaultTheme,\n    rootShouldForwardProp = shouldForwardProp,\n    slotShouldForwardProp = shouldForwardProp\n  } = input;\n  function styleAttachTheme(props) {\n    attachTheme(props, themeId, defaultTheme);\n  }\n  const styled = function (tag) {\n    let inputOptions = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    // If `tag` is already a styled component, filter out the `sx` style function\n    // to prevent unnecessary styles generated by the composite components.\n    mutateStyles(tag, styles => styles.filter(style => style !== styleFunctionSx));\n    const {\n        name: componentName,\n        slot: componentSlot,\n        skipVariantsResolver: inputSkipVariantsResolver,\n        skipSx: inputSkipSx,\n        // TODO v6: remove `lowercaseFirstLetter()` in the next major release\n        // For more details: https://github.com/mui/material-ui/pull/37908\n        overridesResolver = defaultOverridesResolver(lowercaseFirstLetter(componentSlot))\n      } = inputOptions,\n      options = _objectWithoutProperties(inputOptions, _excluded2);\n    const layerName = componentName && componentName.startsWith('Mui') || !!componentSlot ? 'components' : 'custom';\n\n    // if skipVariantsResolver option is defined, take the value, otherwise, true for root and false for other slots.\n    const skipVariantsResolver = inputSkipVariantsResolver !== undefined ? inputSkipVariantsResolver :\n    // TODO v6: remove `Root` in the next major release\n    // For more details: https://github.com/mui/material-ui/pull/37908\n    componentSlot && componentSlot !== 'Root' && componentSlot !== 'root' || false;\n    const skipSx = inputSkipSx || false;\n    let shouldForwardPropOption = shouldForwardProp;\n\n    // TODO v6: remove `Root` in the next major release\n    // For more details: https://github.com/mui/material-ui/pull/37908\n    if (componentSlot === 'Root' || componentSlot === 'root') {\n      shouldForwardPropOption = rootShouldForwardProp;\n    } else if (componentSlot) {\n      // any other slot specified\n      shouldForwardPropOption = slotShouldForwardProp;\n    } else if (isStringTag(tag)) {\n      // for string (html) tag, preserve the behavior in emotion & styled-components.\n      shouldForwardPropOption = undefined;\n    }\n    const defaultStyledResolver = styledEngineStyled(tag, _objectSpread({\n      shouldForwardProp: shouldForwardPropOption,\n      label: generateStyledLabel(componentName, componentSlot)\n    }, options));\n    const transformStyle = style => {\n      // - On the server Emotion doesn't use React.forwardRef for creating components, so the created\n      //   component stays as a function. This condition makes sure that we do not interpolate functions\n      //   which are basically components used as a selectors.\n      // - `style` could be a styled component from a babel plugin for component selectors, This condition\n      //   makes sure that we do not interpolate them.\n      if (style.__emotion_real === style) {\n        return style;\n      }\n      if (typeof style === 'function') {\n        return function styleFunctionProcessor(props) {\n          return processStyle(props, style, props.theme.modularCssLayers ? layerName : undefined);\n        };\n      }\n      if (isPlainObject(style)) {\n        const serialized = preprocessStyles(style);\n        return function styleObjectProcessor(props) {\n          if (!serialized.variants) {\n            return props.theme.modularCssLayers ? shallowLayer(serialized.style, layerName) : serialized.style;\n          }\n          return processStyle(props, serialized, props.theme.modularCssLayers ? layerName : undefined);\n        };\n      }\n      return style;\n    };\n    const muiStyledResolver = function () {\n      const expressionsHead = [];\n      for (var _len = arguments.length, expressionsInput = new Array(_len), _key = 0; _key < _len; _key++) {\n        expressionsInput[_key] = arguments[_key];\n      }\n      const expressionsBody = expressionsInput.map(transformStyle);\n      const expressionsTail = [];\n\n      // Preprocess `props` to set the scoped theme value.\n      // This must run before any other expression.\n      expressionsHead.push(styleAttachTheme);\n      if (componentName && overridesResolver) {\n        expressionsTail.push(function styleThemeOverrides(props) {\n          var _theme$components;\n          const theme = props.theme;\n          const styleOverrides = (_theme$components = theme.components) === null || _theme$components === void 0 || (_theme$components = _theme$components[componentName]) === null || _theme$components === void 0 ? void 0 : _theme$components.styleOverrides;\n          if (!styleOverrides) {\n            return null;\n          }\n          const resolvedStyleOverrides = {};\n\n          // TODO: v7 remove iteration and use `resolveStyleArg(styleOverrides[slot])` directly\n          // eslint-disable-next-line guard-for-in\n          for (const slotKey in styleOverrides) {\n            resolvedStyleOverrides[slotKey] = processStyle(props, styleOverrides[slotKey], props.theme.modularCssLayers ? 'theme' : undefined);\n          }\n          return overridesResolver(props, resolvedStyleOverrides);\n        });\n      }\n      if (componentName && !skipVariantsResolver) {\n        expressionsTail.push(function styleThemeVariants(props) {\n          var _theme$components2;\n          const theme = props.theme;\n          const themeVariants = theme === null || theme === void 0 || (_theme$components2 = theme.components) === null || _theme$components2 === void 0 || (_theme$components2 = _theme$components2[componentName]) === null || _theme$components2 === void 0 ? void 0 : _theme$components2.variants;\n          if (!themeVariants) {\n            return null;\n          }\n          return processStyleVariants(props, themeVariants, [], props.theme.modularCssLayers ? 'theme' : undefined);\n        });\n      }\n      if (!skipSx) {\n        expressionsTail.push(styleFunctionSx);\n      }\n\n      // This function can be called as a tagged template, so the first argument would contain\n      // CSS `string[]` values.\n      if (Array.isArray(expressionsBody[0])) {\n        const inputStrings = expressionsBody.shift();\n\n        // We need to add placeholders in the tagged template for the custom functions we have\n        // possibly added (attachTheme, overrides, variants, and sx).\n        const placeholdersHead = new Array(expressionsHead.length).fill('');\n        const placeholdersTail = new Array(expressionsTail.length).fill('');\n        let outputStrings;\n        // prettier-ignore\n        {\n          outputStrings = [...placeholdersHead, ...inputStrings, ...placeholdersTail];\n          outputStrings.raw = [...placeholdersHead, ...inputStrings.raw, ...placeholdersTail];\n        }\n\n        // The only case where we put something before `attachTheme`\n        expressionsHead.unshift(outputStrings);\n      }\n      const expressions = [...expressionsHead, ...expressionsBody, ...expressionsTail];\n      const Component = defaultStyledResolver(...expressions);\n      if (tag.muiName) {\n        Component.muiName = tag.muiName;\n      }\n      if (process.env.NODE_ENV !== 'production') {\n        Component.displayName = generateDisplayName(componentName, componentSlot, tag);\n      }\n      return Component;\n    };\n    if (defaultStyledResolver.withConfig) {\n      muiStyledResolver.withConfig = defaultStyledResolver.withConfig;\n    }\n    return muiStyledResolver;\n  };\n  return styled;\n}\nfunction generateDisplayName(componentName, componentSlot, tag) {\n  if (componentName) {\n    return \"\".concat(componentName).concat(capitalize(componentSlot || ''));\n  }\n  return \"Styled(\".concat(getDisplayName(tag), \")\");\n}\nfunction generateStyledLabel(componentName, componentSlot) {\n  let label;\n  if (process.env.NODE_ENV !== 'production') {\n    if (componentName) {\n      // TODO v6: remove `lowercaseFirstLetter()` in the next major release\n      // For more details: https://github.com/mui/material-ui/pull/37908\n      label = \"\".concat(componentName, \"-\").concat(lowercaseFirstLetter(componentSlot || 'Root'));\n    }\n  }\n  return label;\n}\nfunction isObjectEmpty(object) {\n  // eslint-disable-next-line\n  for (const _ in object) {\n    return false;\n  }\n  return true;\n}\n\n// https://github.com/emotion-js/emotion/blob/26ded6109fcd8ca9875cc2ce4564fee678a3f3c5/packages/styled/src/utils.js#L40\nfunction isStringTag(tag) {\n  return typeof tag === 'string' &&\n  // 96 is one less than the char code\n  // for \"a\" so this is checking that\n  // it's a lowercase character\n  tag.charCodeAt(0) > 96;\n}\nfunction lowercaseFirstLetter(string) {\n  if (!string) {\n    return string;\n  }\n  return string.charAt(0).toLowerCase() + string.slice(1);\n}", "map": {"version": 3, "names": ["styledEngineStyled", "internal_mutateStyles", "mutateStyles", "internal_serializeStyles", "serializeStyles", "isPlainObject", "capitalize", "getDisplayName", "createTheme", "styleFunctionSx", "preprocessStyles", "systemDefaultTheme", "shouldForwardProp", "prop", "shallowLayer", "serialized", "layerName", "styles", "startsWith", "concat", "String", "defaultOverridesResolver", "slot", "_props", "attachTheme", "props", "themeId", "defaultTheme", "theme", "isObjectEmpty", "processStyle", "style", "resolvedStyle", "Array", "isArray", "flatMap", "subStyle", "variants", "rootStyle", "isProcessed", "otherStyles", "_objectWithoutProperties", "_excluded", "processStyleVariants", "results", "arguments", "length", "undefined", "mergedState", "variantLoop", "i", "variant", "_objectSpread", "ownerState", "key", "_props$ownerState", "push", "createStyled", "input", "rootShouldForwardProp", "slotShouldForwardProp", "styleAttachTheme", "styled", "tag", "inputOptions", "filter", "name", "componentName", "componentSlot", "skipVariantsResolver", "inputSkipVariantsResolver", "skipSx", "inputSkipSx", "overridesResolver", "lowercaseFirstLetter", "options", "_excluded2", "shouldForwardPropOption", "isStringTag", "defaultStyledResolver", "label", "generateStyledLabel", "transformStyle", "__emotion_real", "styleFunctionProcessor", "modularCssLayers", "styleObjectProcessor", "muiStyledResolver", "expressionsHead", "_len", "expressionsInput", "_key", "expressionsBody", "map", "expressionsTail", "styleThemeOverrides", "_theme$components", "styleOverrides", "components", "resolvedStyleOverrides", "<PERSON><PERSON><PERSON>", "styleThemeVariants", "_theme$components2", "themeVariants", "inputStrings", "shift", "placeholdersHead", "fill", "placeholdersTail", "outputStrings", "raw", "unshift", "expressions", "Component", "mui<PERSON><PERSON>", "process", "env", "NODE_ENV", "displayName", "generateDisplayName", "withConfig", "object", "_", "charCodeAt", "string", "char<PERSON>t", "toLowerCase", "slice"], "sources": ["C:/Users/<USER>/OneDrive/ドキュメント/Projects/StudyHub/studyhub-app/node_modules/@mui/system/esm/createStyled/createStyled.js"], "sourcesContent": ["import styledEngineStyled, { internal_mutateStyles as mutateStyles, internal_serializeStyles as serializeStyles } from '@mui/styled-engine';\nimport { isPlainObject } from '@mui/utils/deepmerge';\nimport capitalize from '@mui/utils/capitalize';\nimport getDisplayName from '@mui/utils/getDisplayName';\nimport createTheme from \"../createTheme/index.js\";\nimport styleFunctionSx from \"../styleFunctionSx/index.js\";\nimport preprocessStyles from \"../preprocessStyles.js\";\n\n/* eslint-disable no-underscore-dangle */\n/* eslint-disable no-labels */\n/* eslint-disable no-lone-blocks */\n\nexport const systemDefaultTheme = createTheme();\n\n// Update /system/styled/#api in case if this changes\nexport function shouldForwardProp(prop) {\n  return prop !== 'ownerState' && prop !== 'theme' && prop !== 'sx' && prop !== 'as';\n}\nfunction shallowLayer(serialized, layerName) {\n  if (layerName && serialized && typeof serialized === 'object' && serialized.styles && !serialized.styles.startsWith('@layer') // only add the layer if it is not already there.\n  ) {\n    serialized.styles = `@layer ${layerName}{${String(serialized.styles)}}`;\n  }\n  return serialized;\n}\nfunction defaultOverridesResolver(slot) {\n  if (!slot) {\n    return null;\n  }\n  return (_props, styles) => styles[slot];\n}\nfunction attachTheme(props, themeId, defaultTheme) {\n  props.theme = isObjectEmpty(props.theme) ? defaultTheme : props.theme[themeId] || props.theme;\n}\nfunction processStyle(props, style, layerName) {\n  /*\n   * Style types:\n   *  - null/undefined\n   *  - string\n   *  - CSS style object: { [cssKey]: [cssValue], variants }\n   *  - Processed style object: { style, variants, isProcessed: true }\n   *  - Array of any of the above\n   */\n\n  const resolvedStyle = typeof style === 'function' ? style(props) : style;\n  if (Array.isArray(resolvedStyle)) {\n    return resolvedStyle.flatMap(subStyle => processStyle(props, subStyle, layerName));\n  }\n  if (Array.isArray(resolvedStyle?.variants)) {\n    let rootStyle;\n    if (resolvedStyle.isProcessed) {\n      rootStyle = layerName ? shallowLayer(resolvedStyle.style, layerName) : resolvedStyle.style;\n    } else {\n      const {\n        variants,\n        ...otherStyles\n      } = resolvedStyle;\n      rootStyle = layerName ? shallowLayer(serializeStyles(otherStyles), layerName) : otherStyles;\n    }\n    return processStyleVariants(props, resolvedStyle.variants, [rootStyle], layerName);\n  }\n  if (resolvedStyle?.isProcessed) {\n    return layerName ? shallowLayer(serializeStyles(resolvedStyle.style), layerName) : resolvedStyle.style;\n  }\n  return layerName ? shallowLayer(serializeStyles(resolvedStyle), layerName) : resolvedStyle;\n}\nfunction processStyleVariants(props, variants, results = [], layerName = undefined) {\n  let mergedState; // We might not need it, initialized lazily\n\n  variantLoop: for (let i = 0; i < variants.length; i += 1) {\n    const variant = variants[i];\n    if (typeof variant.props === 'function') {\n      mergedState ??= {\n        ...props,\n        ...props.ownerState,\n        ownerState: props.ownerState\n      };\n      if (!variant.props(mergedState)) {\n        continue;\n      }\n    } else {\n      for (const key in variant.props) {\n        if (props[key] !== variant.props[key] && props.ownerState?.[key] !== variant.props[key]) {\n          continue variantLoop;\n        }\n      }\n    }\n    if (typeof variant.style === 'function') {\n      mergedState ??= {\n        ...props,\n        ...props.ownerState,\n        ownerState: props.ownerState\n      };\n      results.push(layerName ? shallowLayer(serializeStyles(variant.style(mergedState)), layerName) : variant.style(mergedState));\n    } else {\n      results.push(layerName ? shallowLayer(serializeStyles(variant.style), layerName) : variant.style);\n    }\n  }\n  return results;\n}\nexport default function createStyled(input = {}) {\n  const {\n    themeId,\n    defaultTheme = systemDefaultTheme,\n    rootShouldForwardProp = shouldForwardProp,\n    slotShouldForwardProp = shouldForwardProp\n  } = input;\n  function styleAttachTheme(props) {\n    attachTheme(props, themeId, defaultTheme);\n  }\n  const styled = (tag, inputOptions = {}) => {\n    // If `tag` is already a styled component, filter out the `sx` style function\n    // to prevent unnecessary styles generated by the composite components.\n    mutateStyles(tag, styles => styles.filter(style => style !== styleFunctionSx));\n    const {\n      name: componentName,\n      slot: componentSlot,\n      skipVariantsResolver: inputSkipVariantsResolver,\n      skipSx: inputSkipSx,\n      // TODO v6: remove `lowercaseFirstLetter()` in the next major release\n      // For more details: https://github.com/mui/material-ui/pull/37908\n      overridesResolver = defaultOverridesResolver(lowercaseFirstLetter(componentSlot)),\n      ...options\n    } = inputOptions;\n    const layerName = componentName && componentName.startsWith('Mui') || !!componentSlot ? 'components' : 'custom';\n\n    // if skipVariantsResolver option is defined, take the value, otherwise, true for root and false for other slots.\n    const skipVariantsResolver = inputSkipVariantsResolver !== undefined ? inputSkipVariantsResolver :\n    // TODO v6: remove `Root` in the next major release\n    // For more details: https://github.com/mui/material-ui/pull/37908\n    componentSlot && componentSlot !== 'Root' && componentSlot !== 'root' || false;\n    const skipSx = inputSkipSx || false;\n    let shouldForwardPropOption = shouldForwardProp;\n\n    // TODO v6: remove `Root` in the next major release\n    // For more details: https://github.com/mui/material-ui/pull/37908\n    if (componentSlot === 'Root' || componentSlot === 'root') {\n      shouldForwardPropOption = rootShouldForwardProp;\n    } else if (componentSlot) {\n      // any other slot specified\n      shouldForwardPropOption = slotShouldForwardProp;\n    } else if (isStringTag(tag)) {\n      // for string (html) tag, preserve the behavior in emotion & styled-components.\n      shouldForwardPropOption = undefined;\n    }\n    const defaultStyledResolver = styledEngineStyled(tag, {\n      shouldForwardProp: shouldForwardPropOption,\n      label: generateStyledLabel(componentName, componentSlot),\n      ...options\n    });\n    const transformStyle = style => {\n      // - On the server Emotion doesn't use React.forwardRef for creating components, so the created\n      //   component stays as a function. This condition makes sure that we do not interpolate functions\n      //   which are basically components used as a selectors.\n      // - `style` could be a styled component from a babel plugin for component selectors, This condition\n      //   makes sure that we do not interpolate them.\n      if (style.__emotion_real === style) {\n        return style;\n      }\n      if (typeof style === 'function') {\n        return function styleFunctionProcessor(props) {\n          return processStyle(props, style, props.theme.modularCssLayers ? layerName : undefined);\n        };\n      }\n      if (isPlainObject(style)) {\n        const serialized = preprocessStyles(style);\n        return function styleObjectProcessor(props) {\n          if (!serialized.variants) {\n            return props.theme.modularCssLayers ? shallowLayer(serialized.style, layerName) : serialized.style;\n          }\n          return processStyle(props, serialized, props.theme.modularCssLayers ? layerName : undefined);\n        };\n      }\n      return style;\n    };\n    const muiStyledResolver = (...expressionsInput) => {\n      const expressionsHead = [];\n      const expressionsBody = expressionsInput.map(transformStyle);\n      const expressionsTail = [];\n\n      // Preprocess `props` to set the scoped theme value.\n      // This must run before any other expression.\n      expressionsHead.push(styleAttachTheme);\n      if (componentName && overridesResolver) {\n        expressionsTail.push(function styleThemeOverrides(props) {\n          const theme = props.theme;\n          const styleOverrides = theme.components?.[componentName]?.styleOverrides;\n          if (!styleOverrides) {\n            return null;\n          }\n          const resolvedStyleOverrides = {};\n\n          // TODO: v7 remove iteration and use `resolveStyleArg(styleOverrides[slot])` directly\n          // eslint-disable-next-line guard-for-in\n          for (const slotKey in styleOverrides) {\n            resolvedStyleOverrides[slotKey] = processStyle(props, styleOverrides[slotKey], props.theme.modularCssLayers ? 'theme' : undefined);\n          }\n          return overridesResolver(props, resolvedStyleOverrides);\n        });\n      }\n      if (componentName && !skipVariantsResolver) {\n        expressionsTail.push(function styleThemeVariants(props) {\n          const theme = props.theme;\n          const themeVariants = theme?.components?.[componentName]?.variants;\n          if (!themeVariants) {\n            return null;\n          }\n          return processStyleVariants(props, themeVariants, [], props.theme.modularCssLayers ? 'theme' : undefined);\n        });\n      }\n      if (!skipSx) {\n        expressionsTail.push(styleFunctionSx);\n      }\n\n      // This function can be called as a tagged template, so the first argument would contain\n      // CSS `string[]` values.\n      if (Array.isArray(expressionsBody[0])) {\n        const inputStrings = expressionsBody.shift();\n\n        // We need to add placeholders in the tagged template for the custom functions we have\n        // possibly added (attachTheme, overrides, variants, and sx).\n        const placeholdersHead = new Array(expressionsHead.length).fill('');\n        const placeholdersTail = new Array(expressionsTail.length).fill('');\n        let outputStrings;\n        // prettier-ignore\n        {\n          outputStrings = [...placeholdersHead, ...inputStrings, ...placeholdersTail];\n          outputStrings.raw = [...placeholdersHead, ...inputStrings.raw, ...placeholdersTail];\n        }\n\n        // The only case where we put something before `attachTheme`\n        expressionsHead.unshift(outputStrings);\n      }\n      const expressions = [...expressionsHead, ...expressionsBody, ...expressionsTail];\n      const Component = defaultStyledResolver(...expressions);\n      if (tag.muiName) {\n        Component.muiName = tag.muiName;\n      }\n      if (process.env.NODE_ENV !== 'production') {\n        Component.displayName = generateDisplayName(componentName, componentSlot, tag);\n      }\n      return Component;\n    };\n    if (defaultStyledResolver.withConfig) {\n      muiStyledResolver.withConfig = defaultStyledResolver.withConfig;\n    }\n    return muiStyledResolver;\n  };\n  return styled;\n}\nfunction generateDisplayName(componentName, componentSlot, tag) {\n  if (componentName) {\n    return `${componentName}${capitalize(componentSlot || '')}`;\n  }\n  return `Styled(${getDisplayName(tag)})`;\n}\nfunction generateStyledLabel(componentName, componentSlot) {\n  let label;\n  if (process.env.NODE_ENV !== 'production') {\n    if (componentName) {\n      // TODO v6: remove `lowercaseFirstLetter()` in the next major release\n      // For more details: https://github.com/mui/material-ui/pull/37908\n      label = `${componentName}-${lowercaseFirstLetter(componentSlot || 'Root')}`;\n    }\n  }\n  return label;\n}\nfunction isObjectEmpty(object) {\n  // eslint-disable-next-line\n  for (const _ in object) {\n    return false;\n  }\n  return true;\n}\n\n// https://github.com/emotion-js/emotion/blob/26ded6109fcd8ca9875cc2ce4564fee678a3f3c5/packages/styled/src/utils.js#L40\nfunction isStringTag(tag) {\n  return typeof tag === 'string' &&\n  // 96 is one less than the char code\n  // for \"a\" so this is checking that\n  // it's a lowercase character\n  tag.charCodeAt(0) > 96;\n}\nfunction lowercaseFirstLetter(string) {\n  if (!string) {\n    return string;\n  }\n  return string.charAt(0).toLowerCase() + string.slice(1);\n}"], "mappings": ";;;;AAAA,OAAOA,kBAAkB,IAAIC,qBAAqB,IAAIC,YAAY,EAAEC,wBAAwB,IAAIC,eAAe,QAAQ,oBAAoB;AAC3I,SAASC,aAAa,QAAQ,sBAAsB;AACpD,OAAOC,UAAU,MAAM,uBAAuB;AAC9C,OAAOC,cAAc,MAAM,2BAA2B;AACtD,OAAOC,WAAW,MAAM,yBAAyB;AACjD,OAAOC,eAAe,MAAM,6BAA6B;AACzD,OAAOC,gBAAgB,MAAM,wBAAwB;;AAErD;AACA;AACA;;AAEA,OAAO,MAAMC,kBAAkB,GAAGH,WAAW,CAAC,CAAC;;AAE/C;AACA,OAAO,SAASI,iBAAiBA,CAACC,IAAI,EAAE;EACtC,OAAOA,IAAI,KAAK,YAAY,IAAIA,IAAI,KAAK,OAAO,IAAIA,IAAI,KAAK,IAAI,IAAIA,IAAI,KAAK,IAAI;AACpF;AACA,SAASC,YAAYA,CAACC,UAAU,EAAEC,SAAS,EAAE;EAC3C,IAAIA,SAAS,IAAID,UAAU,IAAI,OAAOA,UAAU,KAAK,QAAQ,IAAIA,UAAU,CAACE,MAAM,IAAI,CAACF,UAAU,CAACE,MAAM,CAACC,UAAU,CAAC,QAAQ,CAAC,CAAC;EAAA,EAC5H;IACAH,UAAU,CAACE,MAAM,aAAAE,MAAA,CAAaH,SAAS,OAAAG,MAAA,CAAIC,MAAM,CAACL,UAAU,CAACE,MAAM,CAAC,MAAG;EACzE;EACA,OAAOF,UAAU;AACnB;AACA,SAASM,wBAAwBA,CAACC,IAAI,EAAE;EACtC,IAAI,CAACA,IAAI,EAAE;IACT,OAAO,IAAI;EACb;EACA,OAAO,CAACC,MAAM,EAAEN,MAAM,KAAKA,MAAM,CAACK,IAAI,CAAC;AACzC;AACA,SAASE,WAAWA,CAACC,KAAK,EAAEC,OAAO,EAAEC,YAAY,EAAE;EACjDF,KAAK,CAACG,KAAK,GAAGC,aAAa,CAACJ,KAAK,CAACG,KAAK,CAAC,GAAGD,YAAY,GAAGF,KAAK,CAACG,KAAK,CAACF,OAAO,CAAC,IAAID,KAAK,CAACG,KAAK;AAC/F;AACA,SAASE,YAAYA,CAACL,KAAK,EAAEM,KAAK,EAAEf,SAAS,EAAE;EAC7C;AACF;AACA;AACA;AACA;AACA;AACA;AACA;;EAEE,MAAMgB,aAAa,GAAG,OAAOD,KAAK,KAAK,UAAU,GAAGA,KAAK,CAACN,KAAK,CAAC,GAAGM,KAAK;EACxE,IAAIE,KAAK,CAACC,OAAO,CAACF,aAAa,CAAC,EAAE;IAChC,OAAOA,aAAa,CAACG,OAAO,CAACC,QAAQ,IAAIN,YAAY,CAACL,KAAK,EAAEW,QAAQ,EAAEpB,SAAS,CAAC,CAAC;EACpF;EACA,IAAIiB,KAAK,CAACC,OAAO,CAACF,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEK,QAAQ,CAAC,EAAE;IAC1C,IAAIC,SAAS;IACb,IAAIN,aAAa,CAACO,WAAW,EAAE;MAC7BD,SAAS,GAAGtB,SAAS,GAAGF,YAAY,CAACkB,aAAa,CAACD,KAAK,EAAEf,SAAS,CAAC,GAAGgB,aAAa,CAACD,KAAK;IAC5F,CAAC,MAAM;MACL,MAAM;UACJM;QAEF,CAAC,GAAGL,aAAa;QADZQ,WAAW,GAAAC,wBAAA,CACZT,aAAa,EAAAU,SAAA;MACjBJ,SAAS,GAAGtB,SAAS,GAAGF,YAAY,CAACV,eAAe,CAACoC,WAAW,CAAC,EAAExB,SAAS,CAAC,GAAGwB,WAAW;IAC7F;IACA,OAAOG,oBAAoB,CAAClB,KAAK,EAAEO,aAAa,CAACK,QAAQ,EAAE,CAACC,SAAS,CAAC,EAAEtB,SAAS,CAAC;EACpF;EACA,IAAIgB,aAAa,aAAbA,aAAa,eAAbA,aAAa,CAAEO,WAAW,EAAE;IAC9B,OAAOvB,SAAS,GAAGF,YAAY,CAACV,eAAe,CAAC4B,aAAa,CAACD,KAAK,CAAC,EAAEf,SAAS,CAAC,GAAGgB,aAAa,CAACD,KAAK;EACxG;EACA,OAAOf,SAAS,GAAGF,YAAY,CAACV,eAAe,CAAC4B,aAAa,CAAC,EAAEhB,SAAS,CAAC,GAAGgB,aAAa;AAC5F;AACA,SAASW,oBAAoBA,CAAClB,KAAK,EAAEY,QAAQ,EAAuC;EAAA,IAArCO,OAAO,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,EAAE;EAAA,IAAE7B,SAAS,GAAA6B,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAGE,SAAS;EAChF,IAAIC,WAAW,CAAC,CAAC;;EAEjBC,WAAW,EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGb,QAAQ,CAACS,MAAM,EAAEI,CAAC,IAAI,CAAC,EAAE;IACxD,MAAMC,OAAO,GAAGd,QAAQ,CAACa,CAAC,CAAC;IAC3B,IAAI,OAAOC,OAAO,CAAC1B,KAAK,KAAK,UAAU,EAAE;MACvCuB,WAAW,aAAXA,WAAW,cAAXA,WAAW,GAAXA,WAAW,GAAAI,aAAA,CAAAA,aAAA,CAAAA,aAAA,KACN3B,KAAK,GACLA,KAAK,CAAC4B,UAAU;QACnBA,UAAU,EAAE5B,KAAK,CAAC4B;MAAU;MAE9B,IAAI,CAACF,OAAO,CAAC1B,KAAK,CAACuB,WAAW,CAAC,EAAE;QAC/B;MACF;IACF,CAAC,MAAM;MACL,KAAK,MAAMM,GAAG,IAAIH,OAAO,CAAC1B,KAAK,EAAE;QAAA,IAAA8B,iBAAA;QAC/B,IAAI9B,KAAK,CAAC6B,GAAG,CAAC,KAAKH,OAAO,CAAC1B,KAAK,CAAC6B,GAAG,CAAC,IAAI,EAAAC,iBAAA,GAAA9B,KAAK,CAAC4B,UAAU,cAAAE,iBAAA,uBAAhBA,iBAAA,CAAmBD,GAAG,CAAC,MAAKH,OAAO,CAAC1B,KAAK,CAAC6B,GAAG,CAAC,EAAE;UACvF,SAASL,WAAW;QACtB;MACF;IACF;IACA,IAAI,OAAOE,OAAO,CAACpB,KAAK,KAAK,UAAU,EAAE;MACvCiB,WAAW,aAAXA,WAAW,cAAXA,WAAW,GAAXA,WAAW,GAAAI,aAAA,CAAAA,aAAA,CAAAA,aAAA,KACN3B,KAAK,GACLA,KAAK,CAAC4B,UAAU;QACnBA,UAAU,EAAE5B,KAAK,CAAC4B;MAAU;MAE9BT,OAAO,CAACY,IAAI,CAACxC,SAAS,GAAGF,YAAY,CAACV,eAAe,CAAC+C,OAAO,CAACpB,KAAK,CAACiB,WAAW,CAAC,CAAC,EAAEhC,SAAS,CAAC,GAAGmC,OAAO,CAACpB,KAAK,CAACiB,WAAW,CAAC,CAAC;IAC7H,CAAC,MAAM;MACLJ,OAAO,CAACY,IAAI,CAACxC,SAAS,GAAGF,YAAY,CAACV,eAAe,CAAC+C,OAAO,CAACpB,KAAK,CAAC,EAAEf,SAAS,CAAC,GAAGmC,OAAO,CAACpB,KAAK,CAAC;IACnG;EACF;EACA,OAAOa,OAAO;AAChB;AACA,eAAe,SAASa,YAAYA,CAAA,EAAa;EAAA,IAAZC,KAAK,GAAAb,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;EAC7C,MAAM;IACJnB,OAAO;IACPC,YAAY,GAAGhB,kBAAkB;IACjCgD,qBAAqB,GAAG/C,iBAAiB;IACzCgD,qBAAqB,GAAGhD;EAC1B,CAAC,GAAG8C,KAAK;EACT,SAASG,gBAAgBA,CAACpC,KAAK,EAAE;IAC/BD,WAAW,CAACC,KAAK,EAAEC,OAAO,EAAEC,YAAY,CAAC;EAC3C;EACA,MAAMmC,MAAM,GAAG,SAAAA,CAACC,GAAG,EAAwB;IAAA,IAAtBC,YAAY,GAAAnB,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;IACpC;IACA;IACA3C,YAAY,CAAC6D,GAAG,EAAE9C,MAAM,IAAIA,MAAM,CAACgD,MAAM,CAAClC,KAAK,IAAIA,KAAK,KAAKtB,eAAe,CAAC,CAAC;IAC9E,MAAM;QACJyD,IAAI,EAAEC,aAAa;QACnB7C,IAAI,EAAE8C,aAAa;QACnBC,oBAAoB,EAAEC,yBAAyB;QAC/CC,MAAM,EAAEC,WAAW;QACnB;QACA;QACAC,iBAAiB,GAAGpD,wBAAwB,CAACqD,oBAAoB,CAACN,aAAa,CAAC;MAElF,CAAC,GAAGJ,YAAY;MADXW,OAAO,GAAAlC,wBAAA,CACRuB,YAAY,EAAAY,UAAA;IAChB,MAAM5D,SAAS,GAAGmD,aAAa,IAAIA,aAAa,CAACjD,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,CAACkD,aAAa,GAAG,YAAY,GAAG,QAAQ;;IAE/G;IACA,MAAMC,oBAAoB,GAAGC,yBAAyB,KAAKvB,SAAS,GAAGuB,yBAAyB;IAChG;IACA;IACAF,aAAa,IAAIA,aAAa,KAAK,MAAM,IAAIA,aAAa,KAAK,MAAM,IAAI,KAAK;IAC9E,MAAMG,MAAM,GAAGC,WAAW,IAAI,KAAK;IACnC,IAAIK,uBAAuB,GAAGjE,iBAAiB;;IAE/C;IACA;IACA,IAAIwD,aAAa,KAAK,MAAM,IAAIA,aAAa,KAAK,MAAM,EAAE;MACxDS,uBAAuB,GAAGlB,qBAAqB;IACjD,CAAC,MAAM,IAAIS,aAAa,EAAE;MACxB;MACAS,uBAAuB,GAAGjB,qBAAqB;IACjD,CAAC,MAAM,IAAIkB,WAAW,CAACf,GAAG,CAAC,EAAE;MAC3B;MACAc,uBAAuB,GAAG9B,SAAS;IACrC;IACA,MAAMgC,qBAAqB,GAAG/E,kBAAkB,CAAC+D,GAAG,EAAAX,aAAA;MAClDxC,iBAAiB,EAAEiE,uBAAuB;MAC1CG,KAAK,EAAEC,mBAAmB,CAACd,aAAa,EAAEC,aAAa;IAAC,GACrDO,OAAO,CACX,CAAC;IACF,MAAMO,cAAc,GAAGnD,KAAK,IAAI;MAC9B;MACA;MACA;MACA;MACA;MACA,IAAIA,KAAK,CAACoD,cAAc,KAAKpD,KAAK,EAAE;QAClC,OAAOA,KAAK;MACd;MACA,IAAI,OAAOA,KAAK,KAAK,UAAU,EAAE;QAC/B,OAAO,SAASqD,sBAAsBA,CAAC3D,KAAK,EAAE;UAC5C,OAAOK,YAAY,CAACL,KAAK,EAAEM,KAAK,EAAEN,KAAK,CAACG,KAAK,CAACyD,gBAAgB,GAAGrE,SAAS,GAAG+B,SAAS,CAAC;QACzF,CAAC;MACH;MACA,IAAI1C,aAAa,CAAC0B,KAAK,CAAC,EAAE;QACxB,MAAMhB,UAAU,GAAGL,gBAAgB,CAACqB,KAAK,CAAC;QAC1C,OAAO,SAASuD,oBAAoBA,CAAC7D,KAAK,EAAE;UAC1C,IAAI,CAACV,UAAU,CAACsB,QAAQ,EAAE;YACxB,OAAOZ,KAAK,CAACG,KAAK,CAACyD,gBAAgB,GAAGvE,YAAY,CAACC,UAAU,CAACgB,KAAK,EAAEf,SAAS,CAAC,GAAGD,UAAU,CAACgB,KAAK;UACpG;UACA,OAAOD,YAAY,CAACL,KAAK,EAAEV,UAAU,EAAEU,KAAK,CAACG,KAAK,CAACyD,gBAAgB,GAAGrE,SAAS,GAAG+B,SAAS,CAAC;QAC9F,CAAC;MACH;MACA,OAAOhB,KAAK;IACd,CAAC;IACD,MAAMwD,iBAAiB,GAAG,SAAAA,CAAA,EAAyB;MACjD,MAAMC,eAAe,GAAG,EAAE;MAAC,SAAAC,IAAA,GAAA5C,SAAA,CAAAC,MAAA,EADC4C,gBAAgB,OAAAzD,KAAA,CAAAwD,IAAA,GAAAE,IAAA,MAAAA,IAAA,GAAAF,IAAA,EAAAE,IAAA;QAAhBD,gBAAgB,CAAAC,IAAA,IAAA9C,SAAA,CAAA8C,IAAA;MAAA;MAE5C,MAAMC,eAAe,GAAGF,gBAAgB,CAACG,GAAG,CAACX,cAAc,CAAC;MAC5D,MAAMY,eAAe,GAAG,EAAE;;MAE1B;MACA;MACAN,eAAe,CAAChC,IAAI,CAACK,gBAAgB,CAAC;MACtC,IAAIM,aAAa,IAAIM,iBAAiB,EAAE;QACtCqB,eAAe,CAACtC,IAAI,CAAC,SAASuC,mBAAmBA,CAACtE,KAAK,EAAE;UAAA,IAAAuE,iBAAA;UACvD,MAAMpE,KAAK,GAAGH,KAAK,CAACG,KAAK;UACzB,MAAMqE,cAAc,IAAAD,iBAAA,GAAGpE,KAAK,CAACsE,UAAU,cAAAF,iBAAA,gBAAAA,iBAAA,GAAhBA,iBAAA,CAAmB7B,aAAa,CAAC,cAAA6B,iBAAA,uBAAjCA,iBAAA,CAAmCC,cAAc;UACxE,IAAI,CAACA,cAAc,EAAE;YACnB,OAAO,IAAI;UACb;UACA,MAAME,sBAAsB,GAAG,CAAC,CAAC;;UAEjC;UACA;UACA,KAAK,MAAMC,OAAO,IAAIH,cAAc,EAAE;YACpCE,sBAAsB,CAACC,OAAO,CAAC,GAAGtE,YAAY,CAACL,KAAK,EAAEwE,cAAc,CAACG,OAAO,CAAC,EAAE3E,KAAK,CAACG,KAAK,CAACyD,gBAAgB,GAAG,OAAO,GAAGtC,SAAS,CAAC;UACpI;UACA,OAAO0B,iBAAiB,CAAChD,KAAK,EAAE0E,sBAAsB,CAAC;QACzD,CAAC,CAAC;MACJ;MACA,IAAIhC,aAAa,IAAI,CAACE,oBAAoB,EAAE;QAC1CyB,eAAe,CAACtC,IAAI,CAAC,SAAS6C,kBAAkBA,CAAC5E,KAAK,EAAE;UAAA,IAAA6E,kBAAA;UACtD,MAAM1E,KAAK,GAAGH,KAAK,CAACG,KAAK;UACzB,MAAM2E,aAAa,GAAG3E,KAAK,aAALA,KAAK,gBAAA0E,kBAAA,GAAL1E,KAAK,CAAEsE,UAAU,cAAAI,kBAAA,gBAAAA,kBAAA,GAAjBA,kBAAA,CAAoBnC,aAAa,CAAC,cAAAmC,kBAAA,uBAAlCA,kBAAA,CAAoCjE,QAAQ;UAClE,IAAI,CAACkE,aAAa,EAAE;YAClB,OAAO,IAAI;UACb;UACA,OAAO5D,oBAAoB,CAAClB,KAAK,EAAE8E,aAAa,EAAE,EAAE,EAAE9E,KAAK,CAACG,KAAK,CAACyD,gBAAgB,GAAG,OAAO,GAAGtC,SAAS,CAAC;QAC3G,CAAC,CAAC;MACJ;MACA,IAAI,CAACwB,MAAM,EAAE;QACXuB,eAAe,CAACtC,IAAI,CAAC/C,eAAe,CAAC;MACvC;;MAEA;MACA;MACA,IAAIwB,KAAK,CAACC,OAAO,CAAC0D,eAAe,CAAC,CAAC,CAAC,CAAC,EAAE;QACrC,MAAMY,YAAY,GAAGZ,eAAe,CAACa,KAAK,CAAC,CAAC;;QAE5C;QACA;QACA,MAAMC,gBAAgB,GAAG,IAAIzE,KAAK,CAACuD,eAAe,CAAC1C,MAAM,CAAC,CAAC6D,IAAI,CAAC,EAAE,CAAC;QACnE,MAAMC,gBAAgB,GAAG,IAAI3E,KAAK,CAAC6D,eAAe,CAAChD,MAAM,CAAC,CAAC6D,IAAI,CAAC,EAAE,CAAC;QACnE,IAAIE,aAAa;QACjB;QACA;UACEA,aAAa,GAAG,CAAC,GAAGH,gBAAgB,EAAE,GAAGF,YAAY,EAAE,GAAGI,gBAAgB,CAAC;UAC3EC,aAAa,CAACC,GAAG,GAAG,CAAC,GAAGJ,gBAAgB,EAAE,GAAGF,YAAY,CAACM,GAAG,EAAE,GAAGF,gBAAgB,CAAC;QACrF;;QAEA;QACApB,eAAe,CAACuB,OAAO,CAACF,aAAa,CAAC;MACxC;MACA,MAAMG,WAAW,GAAG,CAAC,GAAGxB,eAAe,EAAE,GAAGI,eAAe,EAAE,GAAGE,eAAe,CAAC;MAChF,MAAMmB,SAAS,GAAGlC,qBAAqB,CAAC,GAAGiC,WAAW,CAAC;MACvD,IAAIjD,GAAG,CAACmD,OAAO,EAAE;QACfD,SAAS,CAACC,OAAO,GAAGnD,GAAG,CAACmD,OAAO;MACjC;MACA,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;QACzCJ,SAAS,CAACK,WAAW,GAAGC,mBAAmB,CAACpD,aAAa,EAAEC,aAAa,EAAEL,GAAG,CAAC;MAChF;MACA,OAAOkD,SAAS;IAClB,CAAC;IACD,IAAIlC,qBAAqB,CAACyC,UAAU,EAAE;MACpCjC,iBAAiB,CAACiC,UAAU,GAAGzC,qBAAqB,CAACyC,UAAU;IACjE;IACA,OAAOjC,iBAAiB;EAC1B,CAAC;EACD,OAAOzB,MAAM;AACf;AACA,SAASyD,mBAAmBA,CAACpD,aAAa,EAAEC,aAAa,EAAEL,GAAG,EAAE;EAC9D,IAAII,aAAa,EAAE;IACjB,UAAAhD,MAAA,CAAUgD,aAAa,EAAAhD,MAAA,CAAGb,UAAU,CAAC8D,aAAa,IAAI,EAAE,CAAC;EAC3D;EACA,iBAAAjD,MAAA,CAAiBZ,cAAc,CAACwD,GAAG,CAAC;AACtC;AACA,SAASkB,mBAAmBA,CAACd,aAAa,EAAEC,aAAa,EAAE;EACzD,IAAIY,KAAK;EACT,IAAImC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzC,IAAIlD,aAAa,EAAE;MACjB;MACA;MACAa,KAAK,MAAA7D,MAAA,CAAMgD,aAAa,OAAAhD,MAAA,CAAIuD,oBAAoB,CAACN,aAAa,IAAI,MAAM,CAAC,CAAE;IAC7E;EACF;EACA,OAAOY,KAAK;AACd;AACA,SAASnD,aAAaA,CAAC4F,MAAM,EAAE;EAC7B;EACA,KAAK,MAAMC,CAAC,IAAID,MAAM,EAAE;IACtB,OAAO,KAAK;EACd;EACA,OAAO,IAAI;AACb;;AAEA;AACA,SAAS3C,WAAWA,CAACf,GAAG,EAAE;EACxB,OAAO,OAAOA,GAAG,KAAK,QAAQ;EAC9B;EACA;EACA;EACAA,GAAG,CAAC4D,UAAU,CAAC,CAAC,CAAC,GAAG,EAAE;AACxB;AACA,SAASjD,oBAAoBA,CAACkD,MAAM,EAAE;EACpC,IAAI,CAACA,MAAM,EAAE;IACX,OAAOA,MAAM;EACf;EACA,OAAOA,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAGF,MAAM,CAACG,KAAK,CAAC,CAAC,CAAC;AACzD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}