{"ast": null, "code": "'use client';\n\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport { GlobalStyles as MuiGlobalStyles, internal_serializeStyles as serializeStyles } from '@mui/styled-engine';\nimport useTheme from \"../useTheme/index.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nfunction wrapGlobalLayer(styles) {\n  const serialized = serializeStyles(styles);\n  if (styles !== serialized && serialized.styles) {\n    if (!serialized.styles.match(/^@layer\\s+[^{]*$/)) {\n      // If the styles are not already wrapped in a layer, wrap them in a global layer.\n      serialized.styles = \"@layer global{\".concat(serialized.styles, \"}\");\n    }\n    return serialized;\n  }\n  return styles;\n}\nfunction GlobalStyles(_ref) {\n  let {\n    styles,\n    themeId,\n    defaultTheme = {}\n  } = _ref;\n  const upperTheme = useTheme(defaultTheme);\n  const resolvedTheme = themeId ? upperTheme[themeId] || upperTheme : upperTheme;\n  let globalStyles = typeof styles === 'function' ? styles(resolvedTheme) : styles;\n  if (resolvedTheme.modularCssLayers) {\n    if (Array.isArray(globalStyles)) {\n      globalStyles = globalStyles.map(styleArg => {\n        if (typeof styleArg === 'function') {\n          return wrapGlobalLayer(styleArg(resolvedTheme));\n        }\n        return wrapGlobalLayer(styleArg);\n      });\n    } else {\n      globalStyles = wrapGlobalLayer(globalStyles);\n    }\n  }\n  return /*#__PURE__*/_jsx(MuiGlobalStyles, {\n    styles: globalStyles\n  });\n}\nprocess.env.NODE_ENV !== \"production\" ? GlobalStyles.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │ To update them, edit the TypeScript types and run `pnpm proptypes`. │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * @ignore\n   */\n  defaultTheme: PropTypes.object,\n  /**\n   * @ignore\n   */\n  styles: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.array, PropTypes.func, PropTypes.number, PropTypes.object, PropTypes.string, PropTypes.bool]),\n  /**\n   * @ignore\n   */\n  themeId: PropTypes.string\n} : void 0;\nexport default GlobalStyles;", "map": {"version": 3, "names": ["React", "PropTypes", "GlobalStyles", "MuiGlobalStyles", "internal_serializeStyles", "serializeStyles", "useTheme", "jsx", "_jsx", "wrapGlobalLayer", "styles", "serialized", "match", "concat", "_ref", "themeId", "defaultTheme", "upperTheme", "resolvedTheme", "globalStyles", "modularCssLayers", "Array", "isArray", "map", "styleArg", "process", "env", "NODE_ENV", "propTypes", "object", "oneOfType", "array", "func", "number", "string", "bool"], "sources": ["C:/Users/<USER>/OneDrive/ドキュメント/Projects/StudyHub/studyhub-app/node_modules/@mui/system/esm/GlobalStyles/GlobalStyles.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport { GlobalStyles as MuiGlobalStyles, internal_serializeStyles as serializeStyles } from '@mui/styled-engine';\nimport useTheme from \"../useTheme/index.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nfunction wrapGlobalLayer(styles) {\n  const serialized = serializeStyles(styles);\n  if (styles !== serialized && serialized.styles) {\n    if (!serialized.styles.match(/^@layer\\s+[^{]*$/)) {\n      // If the styles are not already wrapped in a layer, wrap them in a global layer.\n      serialized.styles = `@layer global{${serialized.styles}}`;\n    }\n    return serialized;\n  }\n  return styles;\n}\nfunction GlobalStyles({\n  styles,\n  themeId,\n  defaultTheme = {}\n}) {\n  const upperTheme = useTheme(defaultTheme);\n  const resolvedTheme = themeId ? upperTheme[themeId] || upperTheme : upperTheme;\n  let globalStyles = typeof styles === 'function' ? styles(resolvedTheme) : styles;\n  if (resolvedTheme.modularCssLayers) {\n    if (Array.isArray(globalStyles)) {\n      globalStyles = globalStyles.map(styleArg => {\n        if (typeof styleArg === 'function') {\n          return wrapGlobalLayer(styleArg(resolvedTheme));\n        }\n        return wrapGlobalLayer(styleArg);\n      });\n    } else {\n      globalStyles = wrapGlobalLayer(globalStyles);\n    }\n  }\n  return /*#__PURE__*/_jsx(MuiGlobalStyles, {\n    styles: globalStyles\n  });\n}\nprocess.env.NODE_ENV !== \"production\" ? GlobalStyles.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │ To update them, edit the TypeScript types and run `pnpm proptypes`. │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * @ignore\n   */\n  defaultTheme: PropTypes.object,\n  /**\n   * @ignore\n   */\n  styles: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.array, PropTypes.func, PropTypes.number, PropTypes.object, PropTypes.string, PropTypes.bool]),\n  /**\n   * @ignore\n   */\n  themeId: PropTypes.string\n} : void 0;\nexport default GlobalStyles;"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,SAASC,YAAY,IAAIC,eAAe,EAAEC,wBAAwB,IAAIC,eAAe,QAAQ,oBAAoB;AACjH,OAAOC,QAAQ,MAAM,sBAAsB;AAC3C,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,SAASC,eAAeA,CAACC,MAAM,EAAE;EAC/B,MAAMC,UAAU,GAAGN,eAAe,CAACK,MAAM,CAAC;EAC1C,IAAIA,MAAM,KAAKC,UAAU,IAAIA,UAAU,CAACD,MAAM,EAAE;IAC9C,IAAI,CAACC,UAAU,CAACD,MAAM,CAACE,KAAK,CAAC,kBAAkB,CAAC,EAAE;MAChD;MACAD,UAAU,CAACD,MAAM,oBAAAG,MAAA,CAAoBF,UAAU,CAACD,MAAM,MAAG;IAC3D;IACA,OAAOC,UAAU;EACnB;EACA,OAAOD,MAAM;AACf;AACA,SAASR,YAAYA,CAAAY,IAAA,EAIlB;EAAA,IAJmB;IACpBJ,MAAM;IACNK,OAAO;IACPC,YAAY,GAAG,CAAC;EAClB,CAAC,GAAAF,IAAA;EACC,MAAMG,UAAU,GAAGX,QAAQ,CAACU,YAAY,CAAC;EACzC,MAAME,aAAa,GAAGH,OAAO,GAAGE,UAAU,CAACF,OAAO,CAAC,IAAIE,UAAU,GAAGA,UAAU;EAC9E,IAAIE,YAAY,GAAG,OAAOT,MAAM,KAAK,UAAU,GAAGA,MAAM,CAACQ,aAAa,CAAC,GAAGR,MAAM;EAChF,IAAIQ,aAAa,CAACE,gBAAgB,EAAE;IAClC,IAAIC,KAAK,CAACC,OAAO,CAACH,YAAY,CAAC,EAAE;MAC/BA,YAAY,GAAGA,YAAY,CAACI,GAAG,CAACC,QAAQ,IAAI;QAC1C,IAAI,OAAOA,QAAQ,KAAK,UAAU,EAAE;UAClC,OAAOf,eAAe,CAACe,QAAQ,CAACN,aAAa,CAAC,CAAC;QACjD;QACA,OAAOT,eAAe,CAACe,QAAQ,CAAC;MAClC,CAAC,CAAC;IACJ,CAAC,MAAM;MACLL,YAAY,GAAGV,eAAe,CAACU,YAAY,CAAC;IAC9C;EACF;EACA,OAAO,aAAaX,IAAI,CAACL,eAAe,EAAE;IACxCO,MAAM,EAAES;EACV,CAAC,CAAC;AACJ;AACAM,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGzB,YAAY,CAAC0B,SAAS,CAAC,yBAAyB;EACtF;EACA;EACA;EACA;EACA;AACF;AACA;EACEZ,YAAY,EAAEf,SAAS,CAAC4B,MAAM;EAC9B;AACF;AACA;EACEnB,MAAM,EAAET,SAAS,CAAC,sCAAsC6B,SAAS,CAAC,CAAC7B,SAAS,CAAC8B,KAAK,EAAE9B,SAAS,CAAC+B,IAAI,EAAE/B,SAAS,CAACgC,MAAM,EAAEhC,SAAS,CAAC4B,MAAM,EAAE5B,SAAS,CAACiC,MAAM,EAAEjC,SAAS,CAACkC,IAAI,CAAC,CAAC;EAC1K;AACF;AACA;EACEpB,OAAO,EAAEd,SAAS,CAACiC;AACrB,CAAC,GAAG,KAAK,CAAC;AACV,eAAehC,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}