{"ast": null, "code": "import _objectSpread from\"C:/Users/<USER>/OneDrive/\\u30C9\\u30AD\\u30E5\\u30E1\\u30F3\\u30C8/Projects/StudyHub/studyhub-app/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import React,{useState}from'react';import{Container,Typography,Card,CardContent,Button,Box,Avatar,TextField,Chip,Paper,List,ListItem,ListItemText,ListItemIcon,Divider,LinearProgress,IconButton,Alert,CircularProgress,FormControl,InputLabel,Select,MenuItem}from'@mui/material';import{Grid}from'@mui/material';import{Edit,Save,Cancel,School,Group,LibraryBooks,Star,TrendingUp,PhotoCamera,CalendarToday}from'@mui/icons-material';import{useAuth}from'../contexts/AuthContext';import{ref,uploadBytes,getDownloadURL}from'firebase/storage';import{storage}from'../firebase/config';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const Profile=()=>{var _userProfile$createdA,_userProfile$createdA2,_userProfile$createdA3;const{currentUser,userProfile,updateUserProfile}=useAuth();const[isEditing,setIsEditing]=useState(false);const[loading,setLoading]=useState(false);const[error,setError]=useState('');const[success,setSuccess]=useState('');const[profileData,setProfileData]=useState({firstName:'',lastName:'',bio:'',university:'',major:'',year:''});const[profilePictureFile,setProfilePictureFile]=useState(null);const[profilePicturePreview,setProfilePicturePreview]=useState('');// Initialize profile data when userProfile changes\nReact.useEffect(()=>{if(userProfile){setProfileData({firstName:userProfile.firstName||'',lastName:userProfile.lastName||'',bio:userProfile.bio||'',university:userProfile.university||'',major:userProfile.major||'',year:userProfile.year||''});setProfilePicturePreview(userProfile.profilePicture||'');}},[userProfile]);const handleInputChange=(field,value)=>{setProfileData(prev=>_objectSpread(_objectSpread({},prev),{},{[field]:value}));};const handleProfilePictureChange=event=>{var _event$target$files;const file=(_event$target$files=event.target.files)===null||_event$target$files===void 0?void 0:_event$target$files[0];if(file){setProfilePictureFile(file);const reader=new FileReader();reader.onload=e=>{var _e$target;setProfilePicturePreview((_e$target=e.target)===null||_e$target===void 0?void 0:_e$target.result);};reader.readAsDataURL(file);}};const uploadProfilePicture=async file=>{if(!currentUser)throw new Error('No user logged in');const storageRef=ref(storage,\"profile-pictures/\".concat(currentUser.uid));const snapshot=await uploadBytes(storageRef,file);return await getDownloadURL(snapshot.ref);};const handleSave=async()=>{if(!currentUser)return;setLoading(true);setError('');setSuccess('');try{let profilePictureUrl=(userProfile===null||userProfile===void 0?void 0:userProfile.profilePicture)||'';// Upload new profile picture if selected\nif(profilePictureFile){profilePictureUrl=await uploadProfilePicture(profilePictureFile);}// Update profile\nawait updateUserProfile(_objectSpread(_objectSpread({},profileData),{},{profilePicture:profilePictureUrl}));setSuccess('Profile updated successfully!');setIsEditing(false);setProfilePictureFile(null);}catch(error){setError(error.message||'Failed to update profile');}finally{setLoading(false);}};const handleCancel=()=>{if(userProfile){setProfileData({firstName:userProfile.firstName||'',lastName:userProfile.lastName||'',bio:userProfile.bio||'',university:userProfile.university||'',major:userProfile.major||'',year:userProfile.year||''});setProfilePicturePreview(userProfile.profilePicture||'');}setProfilePictureFile(null);setIsEditing(false);setError('');setSuccess('');};// Mock data - replace with actual data from Firebase\nconst stats=[{label:'Study Groups Joined',value:5,icon:/*#__PURE__*/_jsx(Group,{color:\"primary\"})},{label:'Courses Completed',value:12,icon:/*#__PURE__*/_jsx(School,{color:\"primary\"})},{label:'Resources Shared',value:8,icon:/*#__PURE__*/_jsx(LibraryBooks,{color:\"primary\"})},{label:'Total Study Hours',value:156,icon:/*#__PURE__*/_jsx(TrendingUp,{color:\"primary\"})}];const achievements=[{title:'Early Bird',description:'Completed 5 courses',icon:'🏆'},{title:'Team Player',description:'Joined 3 study groups',icon:'🤝'},{title:'Knowledge Sharer',description:'Uploaded 5 resources',icon:'📚'},{title:'Consistent Learner',description:'30-day study streak',icon:'🔥'}];const recentActivity=[{action:'Completed \"Advanced React\" course',date:'2 days ago'},{action:'Joined \"Machine Learning\" study group',date:'1 week ago'},{action:'Uploaded \"JavaScript Notes\"',date:'1 week ago'},{action:'Started \"Data Structures\" course',date:'2 weeks ago'}];const currentCourses=[{name:'Advanced React Development',progress:75},{name:'Machine Learning Fundamentals',progress:45},{name:'Database Design',progress:30}];if(!currentUser||!userProfile){return/*#__PURE__*/_jsx(Container,{maxWidth:\"lg\",children:/*#__PURE__*/_jsx(Box,{display:\"flex\",justifyContent:\"center\",alignItems:\"center\",minHeight:\"400px\",children:/*#__PURE__*/_jsx(CircularProgress,{})})});}return/*#__PURE__*/_jsx(Container,{maxWidth:\"lg\",children:/*#__PURE__*/_jsxs(Grid,{container:true,spacing:3,children:[/*#__PURE__*/_jsxs(Grid,{size:{xs:12,md:4},children:[/*#__PURE__*/_jsx(Card,{children:/*#__PURE__*/_jsxs(CardContent,{sx:{textAlign:'center'},children:[/*#__PURE__*/_jsxs(Box,{sx:{position:'relative',display:'inline-block',mb:2},children:[/*#__PURE__*/_jsxs(Avatar,{src:profilePicturePreview,sx:{width:120,height:120,mx:'auto',fontSize:'3rem'},children:[profileData.firstName[0],profileData.lastName[0]]}),isEditing&&/*#__PURE__*/_jsxs(IconButton,{component:\"label\",sx:{position:'absolute',bottom:0,right:0,backgroundColor:'primary.main',color:'white','&:hover':{backgroundColor:'primary.dark'}},children:[/*#__PURE__*/_jsx(PhotoCamera,{}),/*#__PURE__*/_jsx(\"input\",{type:\"file\",hidden:true,accept:\"image/*\",onChange:handleProfilePictureChange})]})]}),error&&/*#__PURE__*/_jsx(Alert,{severity:\"error\",sx:{mb:2},children:error}),success&&/*#__PURE__*/_jsx(Alert,{severity:\"success\",sx:{mb:2},children:success}),isEditing?/*#__PURE__*/_jsxs(Box,{sx:{mb:2},children:[/*#__PURE__*/_jsx(TextField,{fullWidth:true,label:\"First Name\",value:profileData.firstName,onChange:e=>handleInputChange('firstName',e.target.value),sx:{mb:1}}),/*#__PURE__*/_jsx(TextField,{fullWidth:true,label:\"Last Name\",value:profileData.lastName,onChange:e=>handleInputChange('lastName',e.target.value),sx:{mb:1}}),/*#__PURE__*/_jsx(TextField,{fullWidth:true,label:\"Email\",value:userProfile.email,disabled:true,sx:{mb:1}}),/*#__PURE__*/_jsx(TextField,{fullWidth:true,label:\"University\",value:profileData.university,onChange:e=>handleInputChange('university',e.target.value),sx:{mb:1}}),/*#__PURE__*/_jsx(TextField,{fullWidth:true,label:\"Major\",value:profileData.major,onChange:e=>handleInputChange('major',e.target.value),sx:{mb:1}}),/*#__PURE__*/_jsxs(FormControl,{fullWidth:true,sx:{mb:1},children:[/*#__PURE__*/_jsx(InputLabel,{children:\"Year\"}),/*#__PURE__*/_jsxs(Select,{value:profileData.year,label:\"Year\",onChange:e=>handleInputChange('year',e.target.value),children:[/*#__PURE__*/_jsx(MenuItem,{value:\"Freshman\",children:\"Freshman\"}),/*#__PURE__*/_jsx(MenuItem,{value:\"Sophomore\",children:\"Sophomore\"}),/*#__PURE__*/_jsx(MenuItem,{value:\"Junior\",children:\"Junior\"}),/*#__PURE__*/_jsx(MenuItem,{value:\"Senior\",children:\"Senior\"}),/*#__PURE__*/_jsx(MenuItem,{value:\"Graduate\",children:\"Graduate\"}),/*#__PURE__*/_jsx(MenuItem,{value:\"PhD\",children:\"PhD\"})]})]}),/*#__PURE__*/_jsx(TextField,{fullWidth:true,label:\"Bio\",multiline:true,rows:3,value:profileData.bio,onChange:e=>handleInputChange('bio',e.target.value)})]}):/*#__PURE__*/_jsxs(Box,{sx:{mb:2},children:[/*#__PURE__*/_jsx(Typography,{variant:\"h5\",gutterBottom:true,children:userProfile.displayName}),/*#__PURE__*/_jsx(Typography,{variant:\"body2\",color:\"text.secondary\",gutterBottom:true,children:userProfile.email}),/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',justifyContent:'center',gap:1,mb:2},children:[userProfile.university&&/*#__PURE__*/_jsx(Chip,{label:userProfile.university,size:\"small\"}),userProfile.major&&/*#__PURE__*/_jsx(Chip,{label:userProfile.major,size:\"small\"}),userProfile.year&&/*#__PURE__*/_jsx(Chip,{label:userProfile.year,size:\"small\"})]}),/*#__PURE__*/_jsx(Typography,{variant:\"body2\",color:\"text.secondary\",children:userProfile.bio||'No bio available'}),/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center',justifyContent:'center',gap:1,mt:2},children:[/*#__PURE__*/_jsx(CalendarToday,{fontSize:\"small\",color:\"action\"}),/*#__PURE__*/_jsxs(Typography,{variant:\"caption\",color:\"text.secondary\",children:[\"Joined \",((_userProfile$createdA=userProfile.createdAt)===null||_userProfile$createdA===void 0?void 0:(_userProfile$createdA2=_userProfile$createdA.toDate)===null||_userProfile$createdA2===void 0?void 0:(_userProfile$createdA3=_userProfile$createdA2.call(_userProfile$createdA))===null||_userProfile$createdA3===void 0?void 0:_userProfile$createdA3.toLocaleDateString())||'Recently']})]})]}),isEditing?/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',gap:1,justifyContent:'center'},children:[/*#__PURE__*/_jsx(Button,{variant:\"contained\",startIcon:loading?/*#__PURE__*/_jsx(CircularProgress,{size:20}):/*#__PURE__*/_jsx(Save,{}),onClick:handleSave,disabled:loading,children:loading?'Saving...':'Save'}),/*#__PURE__*/_jsx(Button,{variant:\"outlined\",startIcon:/*#__PURE__*/_jsx(Cancel,{}),onClick:handleCancel,disabled:loading,children:\"Cancel\"})]}):/*#__PURE__*/_jsx(Button,{variant:\"contained\",startIcon:/*#__PURE__*/_jsx(Edit,{}),onClick:()=>setIsEditing(true),children:\"Edit Profile\"})]})}),/*#__PURE__*/_jsx(Card,{sx:{mt:3},children:/*#__PURE__*/_jsxs(CardContent,{children:[/*#__PURE__*/_jsx(Typography,{variant:\"h6\",gutterBottom:true,children:\"Statistics\"}),stats.map((stat,index)=>/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center',mb:2},children:[stat.icon,/*#__PURE__*/_jsxs(Box,{sx:{ml:2,flexGrow:1},children:[/*#__PURE__*/_jsx(Typography,{variant:\"body2\",color:\"text.secondary\",children:stat.label}),/*#__PURE__*/_jsx(Typography,{variant:\"h6\",children:stat.value})]})]},index))]})})]}),/*#__PURE__*/_jsxs(Grid,{size:{xs:12,md:8},children:[/*#__PURE__*/_jsx(Card,{sx:{mb:3},children:/*#__PURE__*/_jsxs(CardContent,{children:[/*#__PURE__*/_jsx(Typography,{variant:\"h6\",gutterBottom:true,children:\"Current Courses\"}),currentCourses.map((course,index)=>/*#__PURE__*/_jsxs(Box,{sx:{mb:2},children:[/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',justifyContent:'space-between',mb:1},children:[/*#__PURE__*/_jsx(Typography,{variant:\"body1\",children:course.name}),/*#__PURE__*/_jsxs(Typography,{variant:\"body2\",color:\"text.secondary\",children:[course.progress,\"%\"]})]}),/*#__PURE__*/_jsx(LinearProgress,{variant:\"determinate\",value:course.progress})]},index))]})}),/*#__PURE__*/_jsx(Card,{sx:{mb:3},children:/*#__PURE__*/_jsxs(CardContent,{children:[/*#__PURE__*/_jsx(Typography,{variant:\"h6\",gutterBottom:true,children:\"Achievements\"}),/*#__PURE__*/_jsx(Grid,{container:true,spacing:2,children:achievements.map((achievement,index)=>/*#__PURE__*/_jsx(Grid,{size:{xs:12,sm:6},children:/*#__PURE__*/_jsxs(Paper,{sx:{p:2,display:'flex',alignItems:'center',backgroundColor:'primary.light',color:'primary.contrastText'},children:[/*#__PURE__*/_jsx(Typography,{variant:\"h4\",sx:{mr:2},children:achievement.icon}),/*#__PURE__*/_jsxs(Box,{children:[/*#__PURE__*/_jsx(Typography,{variant:\"subtitle1\",fontWeight:\"bold\",children:achievement.title}),/*#__PURE__*/_jsx(Typography,{variant:\"body2\",children:achievement.description})]})]})},index))})]})}),/*#__PURE__*/_jsx(Card,{children:/*#__PURE__*/_jsxs(CardContent,{children:[/*#__PURE__*/_jsx(Typography,{variant:\"h6\",gutterBottom:true,children:\"Recent Activity\"}),/*#__PURE__*/_jsx(List,{children:recentActivity.map((activity,index)=>/*#__PURE__*/_jsxs(React.Fragment,{children:[/*#__PURE__*/_jsxs(ListItem,{children:[/*#__PURE__*/_jsx(ListItemIcon,{children:/*#__PURE__*/_jsx(Star,{color:\"primary\"})}),/*#__PURE__*/_jsx(ListItemText,{primary:activity.action,secondary:activity.date})]}),index<recentActivity.length-1&&/*#__PURE__*/_jsx(Divider,{})]},index))})]})})]})]})});};export default Profile;", "map": {"version": 3, "names": ["React", "useState", "Container", "Typography", "Card", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Box", "Avatar", "TextField", "Chip", "Paper", "List", "ListItem", "ListItemText", "ListItemIcon", "Divider", "LinearProgress", "IconButton", "<PERSON><PERSON>", "CircularProgress", "FormControl", "InputLabel", "Select", "MenuItem", "Grid", "Edit", "Save", "Cancel", "School", "Group", "LibraryBooks", "Star", "TrendingUp", "PhotoCamera", "CalendarToday", "useAuth", "ref", "uploadBytes", "getDownloadURL", "storage", "jsx", "_jsx", "jsxs", "_jsxs", "Profile", "_userProfile$createdA", "_userProfile$createdA2", "_userProfile$createdA3", "currentUser", "userProfile", "updateUserProfile", "isEditing", "setIsEditing", "loading", "setLoading", "error", "setError", "success", "setSuccess", "profileData", "setProfileData", "firstName", "lastName", "bio", "university", "major", "year", "profilePictureFile", "setProfilePictureFile", "profilePicturePreview", "setProfilePicturePreview", "useEffect", "profilePicture", "handleInputChange", "field", "value", "prev", "_objectSpread", "handleProfilePictureChange", "event", "_event$target$files", "file", "target", "files", "reader", "FileReader", "onload", "e", "_e$target", "result", "readAsDataURL", "uploadProfilePicture", "Error", "storageRef", "concat", "uid", "snapshot", "handleSave", "profilePictureUrl", "message", "handleCancel", "stats", "label", "icon", "color", "achievements", "title", "description", "recentActivity", "action", "date", "currentCourses", "name", "progress", "max<PERSON><PERSON><PERSON>", "children", "display", "justifyContent", "alignItems", "minHeight", "container", "spacing", "size", "xs", "md", "sx", "textAlign", "position", "mb", "src", "width", "height", "mx", "fontSize", "component", "bottom", "right", "backgroundColor", "type", "hidden", "accept", "onChange", "severity", "fullWidth", "email", "disabled", "multiline", "rows", "variant", "gutterBottom", "displayName", "gap", "mt", "createdAt", "toDate", "call", "toLocaleDateString", "startIcon", "onClick", "map", "stat", "index", "ml", "flexGrow", "course", "achievement", "sm", "p", "mr", "fontWeight", "activity", "Fragment", "primary", "secondary", "length"], "sources": ["C:/Users/<USER>/OneDrive/ドキュメント/Projects/StudyHub/studyhub-app/src/pages/Profile.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport {\n  Container,\n  <PERSON><PERSON><PERSON>,\n  Card,\n  CardContent,\n  Button,\n  Box,\n  Avatar,\n  TextField,\n  Chip,\n  Paper,\n  List,\n  ListItem,\n  ListItemText,\n  ListItemIcon,\n  Divider,\n  LinearProgress,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  IconButton,\n  Alert,\n  CircularProgress,\n  FormControl,\n  InputLabel,\n  Select,\n  MenuItem,\n} from '@mui/material';\nimport { Grid } from '@mui/material';\nimport {\n  Edit,\n  Save,\n  Cancel,\n  School,\n  Group,\n  LibraryBooks,\n  Star,\n  TrendingUp,\n  PhotoCamera,\n  CalendarToday,\n} from '@mui/icons-material';\nimport { useAuth } from '../contexts/AuthContext';\nimport { ref, uploadBytes, getDownloadURL } from 'firebase/storage';\nimport { storage } from '../firebase/config';\n\nconst Profile: React.FC = () => {\n  const { currentUser, userProfile, updateUserProfile } = useAuth();\n  const [isEditing, setIsEditing] = useState(false);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n  const [success, setSuccess] = useState('');\n  const [profileData, setProfileData] = useState({\n    firstName: '',\n    lastName: '',\n    bio: '',\n    university: '',\n    major: '',\n    year: '',\n  });\n  const [profilePictureFile, setProfilePictureFile] = useState<File | null>(null);\n  const [profilePicturePreview, setProfilePicturePreview] = useState<string>('');\n\n  // Initialize profile data when userProfile changes\n  React.useEffect(() => {\n    if (userProfile) {\n      setProfileData({\n        firstName: userProfile.firstName || '',\n        lastName: userProfile.lastName || '',\n        bio: userProfile.bio || '',\n        university: userProfile.university || '',\n        major: userProfile.major || '',\n        year: userProfile.year || '',\n      });\n      setProfilePicturePreview(userProfile.profilePicture || '');\n    }\n  }, [userProfile]);\n\n  const handleInputChange = (field: string, value: string) => {\n    setProfileData(prev => ({ ...prev, [field]: value }));\n  };\n\n  const handleProfilePictureChange = (event: React.ChangeEvent<HTMLInputElement>) => {\n    const file = event.target.files?.[0];\n    if (file) {\n      setProfilePictureFile(file);\n      const reader = new FileReader();\n      reader.onload = (e) => {\n        setProfilePicturePreview(e.target?.result as string);\n      };\n      reader.readAsDataURL(file);\n    }\n  };\n\n  const uploadProfilePicture = async (file: File): Promise<string> => {\n    if (!currentUser) throw new Error('No user logged in');\n\n    const storageRef = ref(storage, `profile-pictures/${currentUser.uid}`);\n    const snapshot = await uploadBytes(storageRef, file);\n    return await getDownloadURL(snapshot.ref);\n  };\n\n  const handleSave = async () => {\n    if (!currentUser) return;\n\n    setLoading(true);\n    setError('');\n    setSuccess('');\n\n    try {\n      let profilePictureUrl = userProfile?.profilePicture || '';\n\n      // Upload new profile picture if selected\n      if (profilePictureFile) {\n        profilePictureUrl = await uploadProfilePicture(profilePictureFile);\n      }\n\n      // Update profile\n      await updateUserProfile({\n        ...profileData,\n        profilePicture: profilePictureUrl,\n      });\n\n      setSuccess('Profile updated successfully!');\n      setIsEditing(false);\n      setProfilePictureFile(null);\n    } catch (error: any) {\n      setError(error.message || 'Failed to update profile');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleCancel = () => {\n    if (userProfile) {\n      setProfileData({\n        firstName: userProfile.firstName || '',\n        lastName: userProfile.lastName || '',\n        bio: userProfile.bio || '',\n        university: userProfile.university || '',\n        major: userProfile.major || '',\n        year: userProfile.year || '',\n      });\n      setProfilePicturePreview(userProfile.profilePicture || '');\n    }\n    setProfilePictureFile(null);\n    setIsEditing(false);\n    setError('');\n    setSuccess('');\n  };\n\n  // Mock data - replace with actual data from Firebase\n  const stats = [\n    { label: 'Study Groups Joined', value: 5, icon: <Group color=\"primary\" /> },\n    { label: 'Courses Completed', value: 12, icon: <School color=\"primary\" /> },\n    { label: 'Resources Shared', value: 8, icon: <LibraryBooks color=\"primary\" /> },\n    { label: 'Total Study Hours', value: 156, icon: <TrendingUp color=\"primary\" /> },\n  ];\n\n  const achievements = [\n    { title: 'Early Bird', description: 'Completed 5 courses', icon: '🏆' },\n    { title: 'Team Player', description: 'Joined 3 study groups', icon: '🤝' },\n    { title: 'Knowledge Sharer', description: 'Uploaded 5 resources', icon: '📚' },\n    { title: 'Consistent Learner', description: '30-day study streak', icon: '🔥' },\n  ];\n\n  const recentActivity = [\n    { action: 'Completed \"Advanced React\" course', date: '2 days ago' },\n    { action: 'Joined \"Machine Learning\" study group', date: '1 week ago' },\n    { action: 'Uploaded \"JavaScript Notes\"', date: '1 week ago' },\n    { action: 'Started \"Data Structures\" course', date: '2 weeks ago' },\n  ];\n\n  const currentCourses = [\n    { name: 'Advanced React Development', progress: 75 },\n    { name: 'Machine Learning Fundamentals', progress: 45 },\n    { name: 'Database Design', progress: 30 },\n  ];\n\n  if (!currentUser || !userProfile) {\n    return (\n      <Container maxWidth=\"lg\">\n        <Box display=\"flex\" justifyContent=\"center\" alignItems=\"center\" minHeight=\"400px\">\n          <CircularProgress />\n        </Box>\n      </Container>\n    );\n  }\n\n  return (\n    <Container maxWidth=\"lg\">\n      <Grid container spacing={3}>\n        {/* Profile Information */}\n        <Grid size={{ xs: 12, md: 4 }}>\n          <Card>\n            <CardContent sx={{ textAlign: 'center' }}>\n              <Box sx={{ position: 'relative', display: 'inline-block', mb: 2 }}>\n                <Avatar\n                  src={profilePicturePreview}\n                  sx={{\n                    width: 120,\n                    height: 120,\n                    mx: 'auto',\n                    fontSize: '3rem',\n                  }}\n                >\n                  {profileData.firstName[0]}{profileData.lastName[0]}\n                </Avatar>\n                {isEditing && (\n                  <IconButton\n                    component=\"label\"\n                    sx={{\n                      position: 'absolute',\n                      bottom: 0,\n                      right: 0,\n                      backgroundColor: 'primary.main',\n                      color: 'white',\n                      '&:hover': { backgroundColor: 'primary.dark' },\n                    }}\n                  >\n                    <PhotoCamera />\n                    <input\n                      type=\"file\"\n                      hidden\n                      accept=\"image/*\"\n                      onChange={handleProfilePictureChange}\n                    />\n                  </IconButton>\n                )}\n              </Box>\n\n              {error && (\n                <Alert severity=\"error\" sx={{ mb: 2 }}>\n                  {error}\n                </Alert>\n              )}\n\n              {success && (\n                <Alert severity=\"success\" sx={{ mb: 2 }}>\n                  {success}\n                </Alert>\n              )}\n\n              {isEditing ? (\n                <Box sx={{ mb: 2 }}>\n                  <TextField\n                    fullWidth\n                    label=\"First Name\"\n                    value={profileData.firstName}\n                    onChange={(e) => handleInputChange('firstName', e.target.value)}\n                    sx={{ mb: 1 }}\n                  />\n                  <TextField\n                    fullWidth\n                    label=\"Last Name\"\n                    value={profileData.lastName}\n                    onChange={(e) => handleInputChange('lastName', e.target.value)}\n                    sx={{ mb: 1 }}\n                  />\n                  <TextField\n                    fullWidth\n                    label=\"Email\"\n                    value={userProfile.email}\n                    disabled\n                    sx={{ mb: 1 }}\n                  />\n                  <TextField\n                    fullWidth\n                    label=\"University\"\n                    value={profileData.university}\n                    onChange={(e) => handleInputChange('university', e.target.value)}\n                    sx={{ mb: 1 }}\n                  />\n                  <TextField\n                    fullWidth\n                    label=\"Major\"\n                    value={profileData.major}\n                    onChange={(e) => handleInputChange('major', e.target.value)}\n                    sx={{ mb: 1 }}\n                  />\n                  <FormControl fullWidth sx={{ mb: 1 }}>\n                    <InputLabel>Year</InputLabel>\n                    <Select\n                      value={profileData.year}\n                      label=\"Year\"\n                      onChange={(e) => handleInputChange('year', e.target.value)}\n                    >\n                      <MenuItem value=\"Freshman\">Freshman</MenuItem>\n                      <MenuItem value=\"Sophomore\">Sophomore</MenuItem>\n                      <MenuItem value=\"Junior\">Junior</MenuItem>\n                      <MenuItem value=\"Senior\">Senior</MenuItem>\n                      <MenuItem value=\"Graduate\">Graduate</MenuItem>\n                      <MenuItem value=\"PhD\">PhD</MenuItem>\n                    </Select>\n                  </FormControl>\n                  <TextField\n                    fullWidth\n                    label=\"Bio\"\n                    multiline\n                    rows={3}\n                    value={profileData.bio}\n                    onChange={(e) => handleInputChange('bio', e.target.value)}\n                  />\n                </Box>\n              ) : (\n                <Box sx={{ mb: 2 }}>\n                  <Typography variant=\"h5\" gutterBottom>\n                    {userProfile.displayName}\n                  </Typography>\n                  <Typography variant=\"body2\" color=\"text.secondary\" gutterBottom>\n                    {userProfile.email}\n                  </Typography>\n                  <Box sx={{ display: 'flex', justifyContent: 'center', gap: 1, mb: 2 }}>\n                    {userProfile.university && <Chip label={userProfile.university} size=\"small\" />}\n                    {userProfile.major && <Chip label={userProfile.major} size=\"small\" />}\n                    {userProfile.year && <Chip label={userProfile.year} size=\"small\" />}\n                  </Box>\n                  <Typography variant=\"body2\" color=\"text.secondary\">\n                    {userProfile.bio || 'No bio available'}\n                  </Typography>\n                  <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', gap: 1, mt: 2 }}>\n                    <CalendarToday fontSize=\"small\" color=\"action\" />\n                    <Typography variant=\"caption\" color=\"text.secondary\">\n                      Joined {userProfile.createdAt?.toDate?.()?.toLocaleDateString() || 'Recently'}\n                    </Typography>\n                  </Box>\n                </Box>\n              )}\n\n              {isEditing ? (\n                <Box sx={{ display: 'flex', gap: 1, justifyContent: 'center' }}>\n                  <Button\n                    variant=\"contained\"\n                    startIcon={loading ? <CircularProgress size={20} /> : <Save />}\n                    onClick={handleSave}\n                    disabled={loading}\n                  >\n                    {loading ? 'Saving...' : 'Save'}\n                  </Button>\n                  <Button\n                    variant=\"outlined\"\n                    startIcon={<Cancel />}\n                    onClick={handleCancel}\n                    disabled={loading}\n                  >\n                    Cancel\n                  </Button>\n                </Box>\n              ) : (\n                <Button\n                  variant=\"contained\"\n                  startIcon={<Edit />}\n                  onClick={() => setIsEditing(true)}\n                >\n                  Edit Profile\n                </Button>\n              )}\n            </CardContent>\n          </Card>\n\n          {/* Stats */}\n          <Card sx={{ mt: 3 }}>\n            <CardContent>\n              <Typography variant=\"h6\" gutterBottom>\n                Statistics\n              </Typography>\n              {stats.map((stat, index) => (\n                <Box key={index} sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>\n                  {stat.icon}\n                  <Box sx={{ ml: 2, flexGrow: 1 }}>\n                    <Typography variant=\"body2\" color=\"text.secondary\">\n                      {stat.label}\n                    </Typography>\n                    <Typography variant=\"h6\">\n                      {stat.value}\n                    </Typography>\n                  </Box>\n                </Box>\n              ))}\n            </CardContent>\n          </Card>\n        </Grid>\n\n        {/* Main Content */}\n        <Grid size={{ xs: 12, md: 8 }}>\n          {/* Current Courses */}\n          <Card sx={{ mb: 3 }}>\n            <CardContent>\n              <Typography variant=\"h6\" gutterBottom>\n                Current Courses\n              </Typography>\n              {currentCourses.map((course, index) => (\n                <Box key={index} sx={{ mb: 2 }}>\n                  <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>\n                    <Typography variant=\"body1\">{course.name}</Typography>\n                    <Typography variant=\"body2\" color=\"text.secondary\">\n                      {course.progress}%\n                    </Typography>\n                  </Box>\n                  <LinearProgress variant=\"determinate\" value={course.progress} />\n                </Box>\n              ))}\n            </CardContent>\n          </Card>\n\n          {/* Achievements */}\n          <Card sx={{ mb: 3 }}>\n            <CardContent>\n              <Typography variant=\"h6\" gutterBottom>\n                Achievements\n              </Typography>\n              <Grid container spacing={2}>\n                {achievements.map((achievement, index) => (\n                  <Grid size={{ xs: 12, sm: 6 }} key={index}>\n                    <Paper\n                      sx={{\n                        p: 2,\n                        display: 'flex',\n                        alignItems: 'center',\n                        backgroundColor: 'primary.light',\n                        color: 'primary.contrastText',\n                      }}\n                    >\n                      <Typography variant=\"h4\" sx={{ mr: 2 }}>\n                        {achievement.icon}\n                      </Typography>\n                      <Box>\n                        <Typography variant=\"subtitle1\" fontWeight=\"bold\">\n                          {achievement.title}\n                        </Typography>\n                        <Typography variant=\"body2\">\n                          {achievement.description}\n                        </Typography>\n                      </Box>\n                    </Paper>\n                  </Grid>\n                ))}\n              </Grid>\n            </CardContent>\n          </Card>\n\n          {/* Recent Activity */}\n          <Card>\n            <CardContent>\n              <Typography variant=\"h6\" gutterBottom>\n                Recent Activity\n              </Typography>\n              <List>\n                {recentActivity.map((activity, index) => (\n                  <React.Fragment key={index}>\n                    <ListItem>\n                      <ListItemIcon>\n                        <Star color=\"primary\" />\n                      </ListItemIcon>\n                      <ListItemText\n                        primary={activity.action}\n                        secondary={activity.date}\n                      />\n                    </ListItem>\n                    {index < recentActivity.length - 1 && <Divider />}\n                  </React.Fragment>\n                ))}\n              </List>\n            </CardContent>\n          </Card>\n        </Grid>\n      </Grid>\n    </Container>\n  );\n};\n\nexport default Profile;\n"], "mappings": "gLAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,KAAQ,OAAO,CACvC,OACEC,SAAS,CACTC,UAAU,CACVC,IAAI,CACJC,WAAW,CACXC,MAAM,CACNC,GAAG,CACHC,MAAM,CACNC,SAAS,CACTC,IAAI,CACJC,KAAK,CACLC,IAAI,CACJC,QAAQ,CACRC,YAAY,CACZC,YAAY,CACZ<PERSON>,OAAO,CACPC,cAAc,CAKdC,UAAU,CACVC,KAAK,CACLC,gBAAgB,CAChBC,WAAW,CACXC,UAAU,CACVC,MAAM,CACNC,QAAQ,KACH,eAAe,CACtB,OAASC,IAAI,KAAQ,eAAe,CACpC,OACEC,IAAI,CACJC,IAAI,CACJC,MAAM,CACNC,MAAM,CACNC,KAAK,CACLC,YAAY,CACZC,IAAI,CACJC,UAAU,CACVC,WAAW,CACXC,aAAa,KACR,qBAAqB,CAC5B,OAASC,OAAO,KAAQ,yBAAyB,CACjD,OAASC,GAAG,CAAEC,WAAW,CAAEC,cAAc,KAAQ,kBAAkB,CACnE,OAASC,OAAO,KAAQ,oBAAoB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAE7C,KAAM,CAAAC,OAAiB,CAAGA,CAAA,GAAM,KAAAC,qBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAC9B,KAAM,CAAEC,WAAW,CAAEC,WAAW,CAAEC,iBAAkB,CAAC,CAAGf,OAAO,CAAC,CAAC,CACjE,KAAM,CAACgB,SAAS,CAAEC,YAAY,CAAC,CAAGpD,QAAQ,CAAC,KAAK,CAAC,CACjD,KAAM,CAACqD,OAAO,CAAEC,UAAU,CAAC,CAAGtD,QAAQ,CAAC,KAAK,CAAC,CAC7C,KAAM,CAACuD,KAAK,CAAEC,QAAQ,CAAC,CAAGxD,QAAQ,CAAC,EAAE,CAAC,CACtC,KAAM,CAACyD,OAAO,CAAEC,UAAU,CAAC,CAAG1D,QAAQ,CAAC,EAAE,CAAC,CAC1C,KAAM,CAAC2D,WAAW,CAAEC,cAAc,CAAC,CAAG5D,QAAQ,CAAC,CAC7C6D,SAAS,CAAE,EAAE,CACbC,QAAQ,CAAE,EAAE,CACZC,GAAG,CAAE,EAAE,CACPC,UAAU,CAAE,EAAE,CACdC,KAAK,CAAE,EAAE,CACTC,IAAI,CAAE,EACR,CAAC,CAAC,CACF,KAAM,CAACC,kBAAkB,CAAEC,qBAAqB,CAAC,CAAGpE,QAAQ,CAAc,IAAI,CAAC,CAC/E,KAAM,CAACqE,qBAAqB,CAAEC,wBAAwB,CAAC,CAAGtE,QAAQ,CAAS,EAAE,CAAC,CAE9E;AACAD,KAAK,CAACwE,SAAS,CAAC,IAAM,CACpB,GAAItB,WAAW,CAAE,CACfW,cAAc,CAAC,CACbC,SAAS,CAAEZ,WAAW,CAACY,SAAS,EAAI,EAAE,CACtCC,QAAQ,CAAEb,WAAW,CAACa,QAAQ,EAAI,EAAE,CACpCC,GAAG,CAAEd,WAAW,CAACc,GAAG,EAAI,EAAE,CAC1BC,UAAU,CAAEf,WAAW,CAACe,UAAU,EAAI,EAAE,CACxCC,KAAK,CAAEhB,WAAW,CAACgB,KAAK,EAAI,EAAE,CAC9BC,IAAI,CAAEjB,WAAW,CAACiB,IAAI,EAAI,EAC5B,CAAC,CAAC,CACFI,wBAAwB,CAACrB,WAAW,CAACuB,cAAc,EAAI,EAAE,CAAC,CAC5D,CACF,CAAC,CAAE,CAACvB,WAAW,CAAC,CAAC,CAEjB,KAAM,CAAAwB,iBAAiB,CAAGA,CAACC,KAAa,CAAEC,KAAa,GAAK,CAC1Df,cAAc,CAACgB,IAAI,EAAAC,aAAA,CAAAA,aAAA,IAAUD,IAAI,MAAE,CAACF,KAAK,EAAGC,KAAK,EAAG,CAAC,CACvD,CAAC,CAED,KAAM,CAAAG,0BAA0B,CAAIC,KAA0C,EAAK,KAAAC,mBAAA,CACjF,KAAM,CAAAC,IAAI,EAAAD,mBAAA,CAAGD,KAAK,CAACG,MAAM,CAACC,KAAK,UAAAH,mBAAA,iBAAlBA,mBAAA,CAAqB,CAAC,CAAC,CACpC,GAAIC,IAAI,CAAE,CACRb,qBAAqB,CAACa,IAAI,CAAC,CAC3B,KAAM,CAAAG,MAAM,CAAG,GAAI,CAAAC,UAAU,CAAC,CAAC,CAC/BD,MAAM,CAACE,MAAM,CAAIC,CAAC,EAAK,KAAAC,SAAA,CACrBlB,wBAAwB,EAAAkB,SAAA,CAACD,CAAC,CAACL,MAAM,UAAAM,SAAA,iBAARA,SAAA,CAAUC,MAAgB,CAAC,CACtD,CAAC,CACDL,MAAM,CAACM,aAAa,CAACT,IAAI,CAAC,CAC5B,CACF,CAAC,CAED,KAAM,CAAAU,oBAAoB,CAAG,KAAO,CAAAV,IAAU,EAAsB,CAClE,GAAI,CAACjC,WAAW,CAAE,KAAM,IAAI,CAAA4C,KAAK,CAAC,mBAAmB,CAAC,CAEtD,KAAM,CAAAC,UAAU,CAAGzD,GAAG,CAACG,OAAO,qBAAAuD,MAAA,CAAsB9C,WAAW,CAAC+C,GAAG,CAAE,CAAC,CACtE,KAAM,CAAAC,QAAQ,CAAG,KAAM,CAAA3D,WAAW,CAACwD,UAAU,CAAEZ,IAAI,CAAC,CACpD,MAAO,MAAM,CAAA3C,cAAc,CAAC0D,QAAQ,CAAC5D,GAAG,CAAC,CAC3C,CAAC,CAED,KAAM,CAAA6D,UAAU,CAAG,KAAAA,CAAA,GAAY,CAC7B,GAAI,CAACjD,WAAW,CAAE,OAElBM,UAAU,CAAC,IAAI,CAAC,CAChBE,QAAQ,CAAC,EAAE,CAAC,CACZE,UAAU,CAAC,EAAE,CAAC,CAEd,GAAI,CACF,GAAI,CAAAwC,iBAAiB,CAAG,CAAAjD,WAAW,SAAXA,WAAW,iBAAXA,WAAW,CAAEuB,cAAc,GAAI,EAAE,CAEzD;AACA,GAAIL,kBAAkB,CAAE,CACtB+B,iBAAiB,CAAG,KAAM,CAAAP,oBAAoB,CAACxB,kBAAkB,CAAC,CACpE,CAEA;AACA,KAAM,CAAAjB,iBAAiB,CAAA2B,aAAA,CAAAA,aAAA,IAClBlB,WAAW,MACda,cAAc,CAAE0B,iBAAiB,EAClC,CAAC,CAEFxC,UAAU,CAAC,+BAA+B,CAAC,CAC3CN,YAAY,CAAC,KAAK,CAAC,CACnBgB,qBAAqB,CAAC,IAAI,CAAC,CAC7B,CAAE,MAAOb,KAAU,CAAE,CACnBC,QAAQ,CAACD,KAAK,CAAC4C,OAAO,EAAI,0BAA0B,CAAC,CACvD,CAAC,OAAS,CACR7C,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED,KAAM,CAAA8C,YAAY,CAAGA,CAAA,GAAM,CACzB,GAAInD,WAAW,CAAE,CACfW,cAAc,CAAC,CACbC,SAAS,CAAEZ,WAAW,CAACY,SAAS,EAAI,EAAE,CACtCC,QAAQ,CAAEb,WAAW,CAACa,QAAQ,EAAI,EAAE,CACpCC,GAAG,CAAEd,WAAW,CAACc,GAAG,EAAI,EAAE,CAC1BC,UAAU,CAAEf,WAAW,CAACe,UAAU,EAAI,EAAE,CACxCC,KAAK,CAAEhB,WAAW,CAACgB,KAAK,EAAI,EAAE,CAC9BC,IAAI,CAAEjB,WAAW,CAACiB,IAAI,EAAI,EAC5B,CAAC,CAAC,CACFI,wBAAwB,CAACrB,WAAW,CAACuB,cAAc,EAAI,EAAE,CAAC,CAC5D,CACAJ,qBAAqB,CAAC,IAAI,CAAC,CAC3BhB,YAAY,CAAC,KAAK,CAAC,CACnBI,QAAQ,CAAC,EAAE,CAAC,CACZE,UAAU,CAAC,EAAE,CAAC,CAChB,CAAC,CAED;AACA,KAAM,CAAA2C,KAAK,CAAG,CACZ,CAAEC,KAAK,CAAE,qBAAqB,CAAE3B,KAAK,CAAE,CAAC,CAAE4B,IAAI,cAAE9D,IAAA,CAACZ,KAAK,EAAC2E,KAAK,CAAC,SAAS,CAAE,CAAE,CAAC,CAC3E,CAAEF,KAAK,CAAE,mBAAmB,CAAE3B,KAAK,CAAE,EAAE,CAAE4B,IAAI,cAAE9D,IAAA,CAACb,MAAM,EAAC4E,KAAK,CAAC,SAAS,CAAE,CAAE,CAAC,CAC3E,CAAEF,KAAK,CAAE,kBAAkB,CAAE3B,KAAK,CAAE,CAAC,CAAE4B,IAAI,cAAE9D,IAAA,CAACX,YAAY,EAAC0E,KAAK,CAAC,SAAS,CAAE,CAAE,CAAC,CAC/E,CAAEF,KAAK,CAAE,mBAAmB,CAAE3B,KAAK,CAAE,GAAG,CAAE4B,IAAI,cAAE9D,IAAA,CAACT,UAAU,EAACwE,KAAK,CAAC,SAAS,CAAE,CAAE,CAAC,CACjF,CAED,KAAM,CAAAC,YAAY,CAAG,CACnB,CAAEC,KAAK,CAAE,YAAY,CAAEC,WAAW,CAAE,qBAAqB,CAAEJ,IAAI,CAAE,IAAK,CAAC,CACvE,CAAEG,KAAK,CAAE,aAAa,CAAEC,WAAW,CAAE,uBAAuB,CAAEJ,IAAI,CAAE,IAAK,CAAC,CAC1E,CAAEG,KAAK,CAAE,kBAAkB,CAAEC,WAAW,CAAE,sBAAsB,CAAEJ,IAAI,CAAE,IAAK,CAAC,CAC9E,CAAEG,KAAK,CAAE,oBAAoB,CAAEC,WAAW,CAAE,qBAAqB,CAAEJ,IAAI,CAAE,IAAK,CAAC,CAChF,CAED,KAAM,CAAAK,cAAc,CAAG,CACrB,CAAEC,MAAM,CAAE,mCAAmC,CAAEC,IAAI,CAAE,YAAa,CAAC,CACnE,CAAED,MAAM,CAAE,uCAAuC,CAAEC,IAAI,CAAE,YAAa,CAAC,CACvE,CAAED,MAAM,CAAE,6BAA6B,CAAEC,IAAI,CAAE,YAAa,CAAC,CAC7D,CAAED,MAAM,CAAE,kCAAkC,CAAEC,IAAI,CAAE,aAAc,CAAC,CACpE,CAED,KAAM,CAAAC,cAAc,CAAG,CACrB,CAAEC,IAAI,CAAE,4BAA4B,CAAEC,QAAQ,CAAE,EAAG,CAAC,CACpD,CAAED,IAAI,CAAE,+BAA+B,CAAEC,QAAQ,CAAE,EAAG,CAAC,CACvD,CAAED,IAAI,CAAE,iBAAiB,CAAEC,QAAQ,CAAE,EAAG,CAAC,CAC1C,CAED,GAAI,CAACjE,WAAW,EAAI,CAACC,WAAW,CAAE,CAChC,mBACER,IAAA,CAACxC,SAAS,EAACiH,QAAQ,CAAC,IAAI,CAAAC,QAAA,cACtB1E,IAAA,CAACnC,GAAG,EAAC8G,OAAO,CAAC,MAAM,CAACC,cAAc,CAAC,QAAQ,CAACC,UAAU,CAAC,QAAQ,CAACC,SAAS,CAAC,OAAO,CAAAJ,QAAA,cAC/E1E,IAAA,CAACtB,gBAAgB,GAAE,CAAC,CACjB,CAAC,CACG,CAAC,CAEhB,CAEA,mBACEsB,IAAA,CAACxC,SAAS,EAACiH,QAAQ,CAAC,IAAI,CAAAC,QAAA,cACtBxE,KAAA,CAACnB,IAAI,EAACgG,SAAS,MAACC,OAAO,CAAE,CAAE,CAAAN,QAAA,eAEzBxE,KAAA,CAACnB,IAAI,EAACkG,IAAI,CAAE,CAAEC,EAAE,CAAE,EAAE,CAAEC,EAAE,CAAE,CAAE,CAAE,CAAAT,QAAA,eAC5B1E,IAAA,CAACtC,IAAI,EAAAgH,QAAA,cACHxE,KAAA,CAACvC,WAAW,EAACyH,EAAE,CAAE,CAAEC,SAAS,CAAE,QAAS,CAAE,CAAAX,QAAA,eACvCxE,KAAA,CAACrC,GAAG,EAACuH,EAAE,CAAE,CAAEE,QAAQ,CAAE,UAAU,CAAEX,OAAO,CAAE,cAAc,CAAEY,EAAE,CAAE,CAAE,CAAE,CAAAb,QAAA,eAChExE,KAAA,CAACpC,MAAM,EACL0H,GAAG,CAAE5D,qBAAsB,CAC3BwD,EAAE,CAAE,CACFK,KAAK,CAAE,GAAG,CACVC,MAAM,CAAE,GAAG,CACXC,EAAE,CAAE,MAAM,CACVC,QAAQ,CAAE,MACZ,CAAE,CAAAlB,QAAA,EAEDxD,WAAW,CAACE,SAAS,CAAC,CAAC,CAAC,CAAEF,WAAW,CAACG,QAAQ,CAAC,CAAC,CAAC,EAC5C,CAAC,CACRX,SAAS,eACRR,KAAA,CAAC1B,UAAU,EACTqH,SAAS,CAAC,OAAO,CACjBT,EAAE,CAAE,CACFE,QAAQ,CAAE,UAAU,CACpBQ,MAAM,CAAE,CAAC,CACTC,KAAK,CAAE,CAAC,CACRC,eAAe,CAAE,cAAc,CAC/BjC,KAAK,CAAE,OAAO,CACd,SAAS,CAAE,CAAEiC,eAAe,CAAE,cAAe,CAC/C,CAAE,CAAAtB,QAAA,eAEF1E,IAAA,CAACR,WAAW,GAAE,CAAC,cACfQ,IAAA,UACEiG,IAAI,CAAC,MAAM,CACXC,MAAM,MACNC,MAAM,CAAC,SAAS,CAChBC,QAAQ,CAAE/D,0BAA2B,CACtC,CAAC,EACQ,CACb,EACE,CAAC,CAELvB,KAAK,eACJd,IAAA,CAACvB,KAAK,EAAC4H,QAAQ,CAAC,OAAO,CAACjB,EAAE,CAAE,CAAEG,EAAE,CAAE,CAAE,CAAE,CAAAb,QAAA,CACnC5D,KAAK,CACD,CACR,CAEAE,OAAO,eACNhB,IAAA,CAACvB,KAAK,EAAC4H,QAAQ,CAAC,SAAS,CAACjB,EAAE,CAAE,CAAEG,EAAE,CAAE,CAAE,CAAE,CAAAb,QAAA,CACrC1D,OAAO,CACH,CACR,CAEAN,SAAS,cACRR,KAAA,CAACrC,GAAG,EAACuH,EAAE,CAAE,CAAEG,EAAE,CAAE,CAAE,CAAE,CAAAb,QAAA,eACjB1E,IAAA,CAACjC,SAAS,EACRuI,SAAS,MACTzC,KAAK,CAAC,YAAY,CAClB3B,KAAK,CAAEhB,WAAW,CAACE,SAAU,CAC7BgF,QAAQ,CAAGtD,CAAC,EAAKd,iBAAiB,CAAC,WAAW,CAAEc,CAAC,CAACL,MAAM,CAACP,KAAK,CAAE,CAChEkD,EAAE,CAAE,CAAEG,EAAE,CAAE,CAAE,CAAE,CACf,CAAC,cACFvF,IAAA,CAACjC,SAAS,EACRuI,SAAS,MACTzC,KAAK,CAAC,WAAW,CACjB3B,KAAK,CAAEhB,WAAW,CAACG,QAAS,CAC5B+E,QAAQ,CAAGtD,CAAC,EAAKd,iBAAiB,CAAC,UAAU,CAAEc,CAAC,CAACL,MAAM,CAACP,KAAK,CAAE,CAC/DkD,EAAE,CAAE,CAAEG,EAAE,CAAE,CAAE,CAAE,CACf,CAAC,cACFvF,IAAA,CAACjC,SAAS,EACRuI,SAAS,MACTzC,KAAK,CAAC,OAAO,CACb3B,KAAK,CAAE1B,WAAW,CAAC+F,KAAM,CACzBC,QAAQ,MACRpB,EAAE,CAAE,CAAEG,EAAE,CAAE,CAAE,CAAE,CACf,CAAC,cACFvF,IAAA,CAACjC,SAAS,EACRuI,SAAS,MACTzC,KAAK,CAAC,YAAY,CAClB3B,KAAK,CAAEhB,WAAW,CAACK,UAAW,CAC9B6E,QAAQ,CAAGtD,CAAC,EAAKd,iBAAiB,CAAC,YAAY,CAAEc,CAAC,CAACL,MAAM,CAACP,KAAK,CAAE,CACjEkD,EAAE,CAAE,CAAEG,EAAE,CAAE,CAAE,CAAE,CACf,CAAC,cACFvF,IAAA,CAACjC,SAAS,EACRuI,SAAS,MACTzC,KAAK,CAAC,OAAO,CACb3B,KAAK,CAAEhB,WAAW,CAACM,KAAM,CACzB4E,QAAQ,CAAGtD,CAAC,EAAKd,iBAAiB,CAAC,OAAO,CAAEc,CAAC,CAACL,MAAM,CAACP,KAAK,CAAE,CAC5DkD,EAAE,CAAE,CAAEG,EAAE,CAAE,CAAE,CAAE,CACf,CAAC,cACFrF,KAAA,CAACvB,WAAW,EAAC2H,SAAS,MAAClB,EAAE,CAAE,CAAEG,EAAE,CAAE,CAAE,CAAE,CAAAb,QAAA,eACnC1E,IAAA,CAACpB,UAAU,EAAA8F,QAAA,CAAC,MAAI,CAAY,CAAC,cAC7BxE,KAAA,CAACrB,MAAM,EACLqD,KAAK,CAAEhB,WAAW,CAACO,IAAK,CACxBoC,KAAK,CAAC,MAAM,CACZuC,QAAQ,CAAGtD,CAAC,EAAKd,iBAAiB,CAAC,MAAM,CAAEc,CAAC,CAACL,MAAM,CAACP,KAAK,CAAE,CAAAwC,QAAA,eAE3D1E,IAAA,CAAClB,QAAQ,EAACoD,KAAK,CAAC,UAAU,CAAAwC,QAAA,CAAC,UAAQ,CAAU,CAAC,cAC9C1E,IAAA,CAAClB,QAAQ,EAACoD,KAAK,CAAC,WAAW,CAAAwC,QAAA,CAAC,WAAS,CAAU,CAAC,cAChD1E,IAAA,CAAClB,QAAQ,EAACoD,KAAK,CAAC,QAAQ,CAAAwC,QAAA,CAAC,QAAM,CAAU,CAAC,cAC1C1E,IAAA,CAAClB,QAAQ,EAACoD,KAAK,CAAC,QAAQ,CAAAwC,QAAA,CAAC,QAAM,CAAU,CAAC,cAC1C1E,IAAA,CAAClB,QAAQ,EAACoD,KAAK,CAAC,UAAU,CAAAwC,QAAA,CAAC,UAAQ,CAAU,CAAC,cAC9C1E,IAAA,CAAClB,QAAQ,EAACoD,KAAK,CAAC,KAAK,CAAAwC,QAAA,CAAC,KAAG,CAAU,CAAC,EAC9B,CAAC,EACE,CAAC,cACd1E,IAAA,CAACjC,SAAS,EACRuI,SAAS,MACTzC,KAAK,CAAC,KAAK,CACX4C,SAAS,MACTC,IAAI,CAAE,CAAE,CACRxE,KAAK,CAAEhB,WAAW,CAACI,GAAI,CACvB8E,QAAQ,CAAGtD,CAAC,EAAKd,iBAAiB,CAAC,KAAK,CAAEc,CAAC,CAACL,MAAM,CAACP,KAAK,CAAE,CAC3D,CAAC,EACC,CAAC,cAENhC,KAAA,CAACrC,GAAG,EAACuH,EAAE,CAAE,CAAEG,EAAE,CAAE,CAAE,CAAE,CAAAb,QAAA,eACjB1E,IAAA,CAACvC,UAAU,EAACkJ,OAAO,CAAC,IAAI,CAACC,YAAY,MAAAlC,QAAA,CAClClE,WAAW,CAACqG,WAAW,CACd,CAAC,cACb7G,IAAA,CAACvC,UAAU,EAACkJ,OAAO,CAAC,OAAO,CAAC5C,KAAK,CAAC,gBAAgB,CAAC6C,YAAY,MAAAlC,QAAA,CAC5DlE,WAAW,CAAC+F,KAAK,CACR,CAAC,cACbrG,KAAA,CAACrC,GAAG,EAACuH,EAAE,CAAE,CAAET,OAAO,CAAE,MAAM,CAAEC,cAAc,CAAE,QAAQ,CAAEkC,GAAG,CAAE,CAAC,CAAEvB,EAAE,CAAE,CAAE,CAAE,CAAAb,QAAA,EACnElE,WAAW,CAACe,UAAU,eAAIvB,IAAA,CAAChC,IAAI,EAAC6F,KAAK,CAAErD,WAAW,CAACe,UAAW,CAAC0D,IAAI,CAAC,OAAO,CAAE,CAAC,CAC9EzE,WAAW,CAACgB,KAAK,eAAIxB,IAAA,CAAChC,IAAI,EAAC6F,KAAK,CAAErD,WAAW,CAACgB,KAAM,CAACyD,IAAI,CAAC,OAAO,CAAE,CAAC,CACpEzE,WAAW,CAACiB,IAAI,eAAIzB,IAAA,CAAChC,IAAI,EAAC6F,KAAK,CAAErD,WAAW,CAACiB,IAAK,CAACwD,IAAI,CAAC,OAAO,CAAE,CAAC,EAChE,CAAC,cACNjF,IAAA,CAACvC,UAAU,EAACkJ,OAAO,CAAC,OAAO,CAAC5C,KAAK,CAAC,gBAAgB,CAAAW,QAAA,CAC/ClE,WAAW,CAACc,GAAG,EAAI,kBAAkB,CAC5B,CAAC,cACbpB,KAAA,CAACrC,GAAG,EAACuH,EAAE,CAAE,CAAET,OAAO,CAAE,MAAM,CAAEE,UAAU,CAAE,QAAQ,CAAED,cAAc,CAAE,QAAQ,CAAEkC,GAAG,CAAE,CAAC,CAAEC,EAAE,CAAE,CAAE,CAAE,CAAArC,QAAA,eAC1F1E,IAAA,CAACP,aAAa,EAACmG,QAAQ,CAAC,OAAO,CAAC7B,KAAK,CAAC,QAAQ,CAAE,CAAC,cACjD7D,KAAA,CAACzC,UAAU,EAACkJ,OAAO,CAAC,SAAS,CAAC5C,KAAK,CAAC,gBAAgB,CAAAW,QAAA,EAAC,SAC5C,CAAC,EAAAtE,qBAAA,CAAAI,WAAW,CAACwG,SAAS,UAAA5G,qBAAA,kBAAAC,sBAAA,CAArBD,qBAAA,CAAuB6G,MAAM,UAAA5G,sBAAA,kBAAAC,sBAAA,CAA7BD,sBAAA,CAAA6G,IAAA,CAAA9G,qBAAgC,CAAC,UAAAE,sBAAA,iBAAjCA,sBAAA,CAAmC6G,kBAAkB,CAAC,CAAC,GAAI,UAAU,EACnE,CAAC,EACV,CAAC,EACH,CACN,CAEAzG,SAAS,cACRR,KAAA,CAACrC,GAAG,EAACuH,EAAE,CAAE,CAAET,OAAO,CAAE,MAAM,CAAEmC,GAAG,CAAE,CAAC,CAAElC,cAAc,CAAE,QAAS,CAAE,CAAAF,QAAA,eAC7D1E,IAAA,CAACpC,MAAM,EACL+I,OAAO,CAAC,WAAW,CACnBS,SAAS,CAAExG,OAAO,cAAGZ,IAAA,CAACtB,gBAAgB,EAACuG,IAAI,CAAE,EAAG,CAAE,CAAC,cAAGjF,IAAA,CAACf,IAAI,GAAE,CAAE,CAC/DoI,OAAO,CAAE7D,UAAW,CACpBgD,QAAQ,CAAE5F,OAAQ,CAAA8D,QAAA,CAEjB9D,OAAO,CAAG,WAAW,CAAG,MAAM,CACzB,CAAC,cACTZ,IAAA,CAACpC,MAAM,EACL+I,OAAO,CAAC,UAAU,CAClBS,SAAS,cAAEpH,IAAA,CAACd,MAAM,GAAE,CAAE,CACtBmI,OAAO,CAAE1D,YAAa,CACtB6C,QAAQ,CAAE5F,OAAQ,CAAA8D,QAAA,CACnB,QAED,CAAQ,CAAC,EACN,CAAC,cAEN1E,IAAA,CAACpC,MAAM,EACL+I,OAAO,CAAC,WAAW,CACnBS,SAAS,cAAEpH,IAAA,CAAChB,IAAI,GAAE,CAAE,CACpBqI,OAAO,CAAEA,CAAA,GAAM1G,YAAY,CAAC,IAAI,CAAE,CAAA+D,QAAA,CACnC,cAED,CAAQ,CACT,EACU,CAAC,CACV,CAAC,cAGP1E,IAAA,CAACtC,IAAI,EAAC0H,EAAE,CAAE,CAAE2B,EAAE,CAAE,CAAE,CAAE,CAAArC,QAAA,cAClBxE,KAAA,CAACvC,WAAW,EAAA+G,QAAA,eACV1E,IAAA,CAACvC,UAAU,EAACkJ,OAAO,CAAC,IAAI,CAACC,YAAY,MAAAlC,QAAA,CAAC,YAEtC,CAAY,CAAC,CACZd,KAAK,CAAC0D,GAAG,CAAC,CAACC,IAAI,CAAEC,KAAK,gBACrBtH,KAAA,CAACrC,GAAG,EAAauH,EAAE,CAAE,CAAET,OAAO,CAAE,MAAM,CAAEE,UAAU,CAAE,QAAQ,CAAEU,EAAE,CAAE,CAAE,CAAE,CAAAb,QAAA,EACnE6C,IAAI,CAACzD,IAAI,cACV5D,KAAA,CAACrC,GAAG,EAACuH,EAAE,CAAE,CAAEqC,EAAE,CAAE,CAAC,CAAEC,QAAQ,CAAE,CAAE,CAAE,CAAAhD,QAAA,eAC9B1E,IAAA,CAACvC,UAAU,EAACkJ,OAAO,CAAC,OAAO,CAAC5C,KAAK,CAAC,gBAAgB,CAAAW,QAAA,CAC/C6C,IAAI,CAAC1D,KAAK,CACD,CAAC,cACb7D,IAAA,CAACvC,UAAU,EAACkJ,OAAO,CAAC,IAAI,CAAAjC,QAAA,CACrB6C,IAAI,CAACrF,KAAK,CACD,CAAC,EACV,CAAC,GATEsF,KAUL,CACN,CAAC,EACS,CAAC,CACV,CAAC,EACH,CAAC,cAGPtH,KAAA,CAACnB,IAAI,EAACkG,IAAI,CAAE,CAAEC,EAAE,CAAE,EAAE,CAAEC,EAAE,CAAE,CAAE,CAAE,CAAAT,QAAA,eAE5B1E,IAAA,CAACtC,IAAI,EAAC0H,EAAE,CAAE,CAAEG,EAAE,CAAE,CAAE,CAAE,CAAAb,QAAA,cAClBxE,KAAA,CAACvC,WAAW,EAAA+G,QAAA,eACV1E,IAAA,CAACvC,UAAU,EAACkJ,OAAO,CAAC,IAAI,CAACC,YAAY,MAAAlC,QAAA,CAAC,iBAEtC,CAAY,CAAC,CACZJ,cAAc,CAACgD,GAAG,CAAC,CAACK,MAAM,CAAEH,KAAK,gBAChCtH,KAAA,CAACrC,GAAG,EAAauH,EAAE,CAAE,CAAEG,EAAE,CAAE,CAAE,CAAE,CAAAb,QAAA,eAC7BxE,KAAA,CAACrC,GAAG,EAACuH,EAAE,CAAE,CAAET,OAAO,CAAE,MAAM,CAAEC,cAAc,CAAE,eAAe,CAAEW,EAAE,CAAE,CAAE,CAAE,CAAAb,QAAA,eACnE1E,IAAA,CAACvC,UAAU,EAACkJ,OAAO,CAAC,OAAO,CAAAjC,QAAA,CAAEiD,MAAM,CAACpD,IAAI,CAAa,CAAC,cACtDrE,KAAA,CAACzC,UAAU,EAACkJ,OAAO,CAAC,OAAO,CAAC5C,KAAK,CAAC,gBAAgB,CAAAW,QAAA,EAC/CiD,MAAM,CAACnD,QAAQ,CAAC,GACnB,EAAY,CAAC,EACV,CAAC,cACNxE,IAAA,CAACzB,cAAc,EAACoI,OAAO,CAAC,aAAa,CAACzE,KAAK,CAAEyF,MAAM,CAACnD,QAAS,CAAE,CAAC,GAPxDgD,KAQL,CACN,CAAC,EACS,CAAC,CACV,CAAC,cAGPxH,IAAA,CAACtC,IAAI,EAAC0H,EAAE,CAAE,CAAEG,EAAE,CAAE,CAAE,CAAE,CAAAb,QAAA,cAClBxE,KAAA,CAACvC,WAAW,EAAA+G,QAAA,eACV1E,IAAA,CAACvC,UAAU,EAACkJ,OAAO,CAAC,IAAI,CAACC,YAAY,MAAAlC,QAAA,CAAC,cAEtC,CAAY,CAAC,cACb1E,IAAA,CAACjB,IAAI,EAACgG,SAAS,MAACC,OAAO,CAAE,CAAE,CAAAN,QAAA,CACxBV,YAAY,CAACsD,GAAG,CAAC,CAACM,WAAW,CAAEJ,KAAK,gBACnCxH,IAAA,CAACjB,IAAI,EAACkG,IAAI,CAAE,CAAEC,EAAE,CAAE,EAAE,CAAE2C,EAAE,CAAE,CAAE,CAAE,CAAAnD,QAAA,cAC5BxE,KAAA,CAACjC,KAAK,EACJmH,EAAE,CAAE,CACF0C,CAAC,CAAE,CAAC,CACJnD,OAAO,CAAE,MAAM,CACfE,UAAU,CAAE,QAAQ,CACpBmB,eAAe,CAAE,eAAe,CAChCjC,KAAK,CAAE,sBACT,CAAE,CAAAW,QAAA,eAEF1E,IAAA,CAACvC,UAAU,EAACkJ,OAAO,CAAC,IAAI,CAACvB,EAAE,CAAE,CAAE2C,EAAE,CAAE,CAAE,CAAE,CAAArD,QAAA,CACpCkD,WAAW,CAAC9D,IAAI,CACP,CAAC,cACb5D,KAAA,CAACrC,GAAG,EAAA6G,QAAA,eACF1E,IAAA,CAACvC,UAAU,EAACkJ,OAAO,CAAC,WAAW,CAACqB,UAAU,CAAC,MAAM,CAAAtD,QAAA,CAC9CkD,WAAW,CAAC3D,KAAK,CACR,CAAC,cACbjE,IAAA,CAACvC,UAAU,EAACkJ,OAAO,CAAC,OAAO,CAAAjC,QAAA,CACxBkD,WAAW,CAAC1D,WAAW,CACd,CAAC,EACV,CAAC,EACD,CAAC,EArB0BsD,KAsB9B,CACP,CAAC,CACE,CAAC,EACI,CAAC,CACV,CAAC,cAGPxH,IAAA,CAACtC,IAAI,EAAAgH,QAAA,cACHxE,KAAA,CAACvC,WAAW,EAAA+G,QAAA,eACV1E,IAAA,CAACvC,UAAU,EAACkJ,OAAO,CAAC,IAAI,CAACC,YAAY,MAAAlC,QAAA,CAAC,iBAEtC,CAAY,CAAC,cACb1E,IAAA,CAAC9B,IAAI,EAAAwG,QAAA,CACFP,cAAc,CAACmD,GAAG,CAAC,CAACW,QAAQ,CAAET,KAAK,gBAClCtH,KAAA,CAAC5C,KAAK,CAAC4K,QAAQ,EAAAxD,QAAA,eACbxE,KAAA,CAAC/B,QAAQ,EAAAuG,QAAA,eACP1E,IAAA,CAAC3B,YAAY,EAAAqG,QAAA,cACX1E,IAAA,CAACV,IAAI,EAACyE,KAAK,CAAC,SAAS,CAAE,CAAC,CACZ,CAAC,cACf/D,IAAA,CAAC5B,YAAY,EACX+J,OAAO,CAAEF,QAAQ,CAAC7D,MAAO,CACzBgE,SAAS,CAAEH,QAAQ,CAAC5D,IAAK,CAC1B,CAAC,EACM,CAAC,CACVmD,KAAK,CAAGrD,cAAc,CAACkE,MAAM,CAAG,CAAC,eAAIrI,IAAA,CAAC1B,OAAO,GAAE,CAAC,GAV9BkJ,KAWL,CACjB,CAAC,CACE,CAAC,EACI,CAAC,CACV,CAAC,EACH,CAAC,EACH,CAAC,CACE,CAAC,CAEhB,CAAC,CAED,cAAe,CAAArH,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}