import React, { useState, useEffect } from 'react';
import {
  Container,
  Grid,
  Paper,
  List,
  ListItem,
  ListItemAvatar,
  ListItemText,
  Avatar,
  Typography,
  Box,
  Badge,
  IconButton,
  TextField,
  InputAdornment,
  Fab,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  CircularProgress,
} from '@mui/material';
import {
  Search,
  Add,
  Message,
  Group,
  Person,
  Close,
} from '@mui/icons-material';
import { formatDistanceToNow } from 'date-fns';
import { ChatService } from '../services/chatService';
import { StudyGroupService } from '../services/studyGroupService';
import { Chat as ChatType, CreatePrivateChatData } from '../types/chat';
import { StudyGroup } from '../types/studyGroup';
import { useAuth } from '../contexts/AuthContext';
import GroupChat from '../components/Chat/GroupChat';

const Chat: React.FC = () => {
  const { user } = useAuth();
  const [chats, setChats] = useState<ChatType[]>([]);
  const [selectedChat, setSelectedChat] = useState<ChatType | null>(null);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [newChatOpen, setNewChatOpen] = useState(false);
  const [chatType, setChatType] = useState<'private' | 'group'>('private');
  const [studyGroups, setStudyGroups] = useState<StudyGroup[]>([]);
  const [selectedUsers, setSelectedUsers] = useState<string[]>([]);
  const [newChatData, setNewChatData] = useState({
    name: '',
    description: '',
    participantEmail: '',
  });

  useEffect(() => {
    if (!user) return;

    // Subscribe to user's chats
    const unsubscribe = ChatService.subscribeToUserChats(user.uid, (userChats) => {
      setChats(userChats);
      setLoading(false);
    });

    // Load user's study groups for group chat creation
    loadStudyGroups();

    return unsubscribe;
  }, [user]);

  const loadStudyGroups = async () => {
    if (!user) return;

    try {
      const groups = await StudyGroupService.getUserStudyGroups(user.uid);
      setStudyGroups(groups);
    } catch (error) {
      console.error('Error loading study groups:', error);
    }
  };

  const filteredChats = chats.filter(chat => {
    if (!searchTerm) return true;
    
    const searchLower = searchTerm.toLowerCase();
    
    if (chat.type === 'group') {
      return chat.name?.toLowerCase().includes(searchLower);
    } else {
      // For private chats, search by participant names
      return chat.participantDetails?.some(p => 
        p.displayName.toLowerCase().includes(searchLower) ||
        p.email.toLowerCase().includes(searchLower)
      );
    }
  });

  const handleCreatePrivateChat = async () => {
    if (!user || !newChatData.participantEmail.trim()) return;

    try {
      setLoading(true);
      
      // TODO: Find user by email and get their profile
      // For now, we'll create a mock profile
      const otherUserProfile = {
        displayName: newChatData.participantEmail.split('@')[0],
        email: newChatData.participantEmail,
        profilePicture: '',
      };

      const currentUserProfile = {
        displayName: user.displayName || user.email?.split('@')[0] || 'User',
        email: user.email || '',
        profilePicture: user.photoURL || '',
      };

      const chatData: CreatePrivateChatData = {
        participantId: 'temp-user-id', // TODO: Get actual user ID
        initialMessage: `Hi! I'd like to connect with you on StudyHub.`,
      };

      const chatId = await ChatService.createPrivateChat(
        user.uid,
        chatData,
        currentUserProfile,
        otherUserProfile
      );

      setNewChatOpen(false);
      setNewChatData({ name: '', description: '', participantEmail: '' });
    } catch (error) {
      console.error('Error creating private chat:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleCreateGroupChat = async () => {
    if (!user || !newChatData.name.trim()) return;

    try {
      setLoading(true);
      
      // TODO: Implement group chat creation
      console.log('Creating group chat:', newChatData);
      
      setNewChatOpen(false);
      setNewChatData({ name: '', description: '', participantEmail: '' });
    } catch (error) {
      console.error('Error creating group chat:', error);
    } finally {
      setLoading(false);
    }
  };

  const getChatDisplayName = (chat: ChatType) => {
    if (chat.type === 'group') {
      return chat.name || 'Group Chat';
    } else {
      // For private chats, show the other participant's name
      const otherParticipant = chat.participantDetails?.find(p => p.uid !== user?.uid);
      return otherParticipant?.displayName || 'Private Chat';
    }
  };

  const getChatAvatar = (chat: ChatType) => {
    if (chat.type === 'group') {
      return chat.name?.[0] || 'G';
    } else {
      const otherParticipant = chat.participantDetails?.find(p => p.uid !== user?.uid);
      return otherParticipant?.profilePicture || otherParticipant?.displayName?.[0] || 'U';
    }
  };

  const getLastMessagePreview = (chat: ChatType) => {
    if (!chat.lastMessage) return 'No messages yet';
    
    const content = chat.lastMessage.content;
    const isOwnMessage = chat.lastMessage.senderId === user?.uid;
    const prefix = isOwnMessage ? 'You: ' : '';
    
    return `${prefix}${content.length > 50 ? content.substring(0, 50) + '...' : content}`;
  };

  const formatLastMessageTime = (chat: ChatType) => {
    if (!chat.lastMessageAt) return '';
    
    const date = chat.lastMessageAt.toDate ? chat.lastMessageAt.toDate() : new Date(chat.lastMessageAt);
    return formatDistanceToNow(date, { addSuffix: true });
  };

  const getUnreadCount = (chat: ChatType) => {
    return chat.unreadCount?.[user?.uid || ''] || 0;
  };

  if (loading) {
    return (
      <Container maxWidth="lg">
        <Box display="flex" justifyContent="center" alignItems="center" height="400px">
          <CircularProgress />
        </Box>
      </Container>
    );
  }

  return (
    <Container maxWidth="lg" sx={{ py: 3 }}>
      <Typography variant="h4" component="h1" gutterBottom>
        Messages
      </Typography>

      <Grid container spacing={3} sx={{ height: 'calc(100vh - 200px)' }}>
        {/* Chat List */}
        <Grid xs={12} md={4}>
          <Paper sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
            {/* Search */}
            <Box sx={{ p: 2, borderBottom: 1, borderColor: 'divider' }}>
              <TextField
                fullWidth
                placeholder="Search conversations..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <Search />
                    </InputAdornment>
                  ),
                }}
              />
            </Box>

            {/* Chat List */}
            <List sx={{ flexGrow: 1, overflow: 'auto', py: 0 }}>
              {filteredChats.length === 0 ? (
                <Box sx={{ p: 3, textAlign: 'center' }}>
                  <Typography variant="body2" color="text.secondary">
                    No conversations yet
                  </Typography>
                  <Typography variant="caption" color="text.secondary">
                    Start a new conversation to get started
                  </Typography>
                </Box>
              ) : (
                filteredChats.map((chat) => (
                  <ListItem
                    key={chat.id}
                    onClick={() => setSelectedChat(chat)}
                    sx={{
                      cursor: 'pointer',
                      borderBottom: 1,
                      borderColor: 'divider',
                      bgcolor: selectedChat?.id === chat.id ? 'action.selected' : 'transparent',
                      '&:hover': {
                        bgcolor: 'action.hover',
                      },
                    }}
                  >
                    <ListItemAvatar>
                      <Badge
                        badgeContent={getUnreadCount(chat)}
                        color="primary"
                        invisible={getUnreadCount(chat) === 0}
                      >
                        <Avatar src={typeof getChatAvatar(chat) === 'string' && getChatAvatar(chat).startsWith('http') ? getChatAvatar(chat) : undefined}>
                          {typeof getChatAvatar(chat) === 'string' && !getChatAvatar(chat).startsWith('http') ? getChatAvatar(chat) : ''}
                        </Avatar>
                      </Badge>
                    </ListItemAvatar>
                    <ListItemText
                      primary={
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          <Typography variant="subtitle2" noWrap>
                            {getChatDisplayName(chat)}
                          </Typography>
                          {chat.type === 'group' && (
                            <Group fontSize="small" color="action" />
                          )}
                        </Box>
                      }
                      secondary={
                        <Box>
                          <Typography variant="body2" color="text.secondary" noWrap>
                            {getLastMessagePreview(chat)}
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            {formatLastMessageTime(chat)}
                          </Typography>
                        </Box>
                      }
                    />
                  </ListItem>
                ))
              )}
            </List>
          </Paper>
        </Grid>

        {/* Chat Area */}
        <Grid xs={12} md={8}>
          <Paper sx={{ height: '100%' }}>
            {selectedChat ? (
              <GroupChat
                chatId={selectedChat.id}
                chat={selectedChat}
                onClose={() => setSelectedChat(null)}
              />
            ) : (
              <Box
                sx={{
                  height: '100%',
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'center',
                  justifyContent: 'center',
                  gap: 2,
                }}
              >
                <Message sx={{ fontSize: 64, color: 'text.secondary' }} />
                <Typography variant="h6" color="text.secondary">
                  Select a conversation to start messaging
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Choose from your existing conversations or start a new one
                </Typography>
              </Box>
            )}
          </Paper>
        </Grid>
      </Grid>

      {/* Floating Action Button */}
      <Fab
        color="primary"
        aria-label="new chat"
        sx={{ position: 'fixed', bottom: 16, right: 16 }}
        onClick={() => setNewChatOpen(true)}
      >
        <Add />
      </Fab>

      {/* New Chat Dialog */}
      <Dialog open={newChatOpen} onClose={() => setNewChatOpen(false)} maxWidth="sm" fullWidth>
        <DialogTitle>Start New Conversation</DialogTitle>
        <DialogContent>
          <Box sx={{ pt: 1 }}>
            <FormControl fullWidth sx={{ mb: 2 }}>
              <InputLabel>Chat Type</InputLabel>
              <Select
                value={chatType}
                label="Chat Type"
                onChange={(e) => setChatType(e.target.value as 'private' | 'group')}
              >
                <MenuItem value="private">
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <Person />
                    Private Chat
                  </Box>
                </MenuItem>
                <MenuItem value="group">
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <Group />
                    Group Chat
                  </Box>
                </MenuItem>
              </Select>
            </FormControl>

            {chatType === 'private' ? (
              <TextField
                fullWidth
                label="User Email"
                value={newChatData.participantEmail}
                onChange={(e) => setNewChatData({ ...newChatData, participantEmail: e.target.value })}
                placeholder="Enter email address of the person you want to chat with"
              />
            ) : (
              <>
                <TextField
                  fullWidth
                  label="Group Name"
                  value={newChatData.name}
                  onChange={(e) => setNewChatData({ ...newChatData, name: e.target.value })}
                  sx={{ mb: 2 }}
                />
                <TextField
                  fullWidth
                  label="Description (optional)"
                  multiline
                  rows={2}
                  value={newChatData.description}
                  onChange={(e) => setNewChatData({ ...newChatData, description: e.target.value })}
                />
              </>
            )}
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setNewChatOpen(false)}>Cancel</Button>
          <Button
            onClick={chatType === 'private' ? handleCreatePrivateChat : handleCreateGroupChat}
            variant="contained"
            disabled={
              loading ||
              (chatType === 'private' && !newChatData.participantEmail.trim()) ||
              (chatType === 'group' && !newChatData.name.trim())
            }
          >
            {loading ? <CircularProgress size={20} /> : 'Start Chat'}
          </Button>
        </DialogActions>
      </Dialog>
    </Container>
  );
};

export default Chat;
