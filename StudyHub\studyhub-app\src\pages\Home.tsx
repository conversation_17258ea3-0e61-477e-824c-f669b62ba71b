import React from 'react';
import {
  Container,
  Typography,
  Box,
  <PERSON><PERSON>,
  Card,
  CardContent,
  CardActions,
  Paper,
} from '@mui/material';
import Grid from '@mui/material/Grid2';
import {
  Group,
  LibraryBooks,
  Quiz,
  Schedule,
  TrendingUp,
  People,
} from '@mui/icons-material';
import { Link } from 'react-router-dom';

const Home: React.FC = () => {
  const features = [
    {
      icon: <Group fontSize="large" color="primary" />,
      title: 'Study Groups',
      description: 'Join or create study groups with fellow students. Collaborate and learn together.',
    },
    {
      icon: <LibraryBooks fontSize="large" color="primary" />,
      title: 'Resource Library',
      description: 'Access a vast collection of study materials, notes, and educational resources.',
    },
    {
      icon: <Quiz fontSize="large" color="primary" />,
      title: 'Practice Tests',
      description: 'Test your knowledge with interactive quizzes and practice exams.',
    },
    {
      icon: <Schedule fontSize="large" color="primary" />,
      title: 'Study Scheduler',
      description: 'Plan your study sessions and track your progress with our smart scheduler.',
    },
    {
      icon: <TrendingUp fontSize="large" color="primary" />,
      title: 'Progress Tracking',
      description: 'Monitor your learning progress and identify areas for improvement.',
    },
    {
      icon: <People fontSize="large" color="primary" />,
      title: 'Community',
      description: 'Connect with a community of learners and share knowledge.',
    },
  ];

  return (
    <Container maxWidth="lg">
      {/* Hero Section */}
      <Box
        sx={{
          textAlign: 'center',
          py: 8,
          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
          borderRadius: 2,
          color: 'white',
          mb: 6,
        }}
      >
        <Typography variant="h1" component="h1" gutterBottom>
          Welcome to StudyHub
        </Typography>
        <Typography variant="h5" component="p" sx={{ mb: 4, opacity: 0.9 }}>
          Your ultimate platform for collaborative learning and academic success
        </Typography>
        <Box sx={{ display: 'flex', gap: 2, justifyContent: 'center' }}>
          <Button
            variant="contained"
            size="large"
            component={Link}
            to="/register"
            sx={{
              backgroundColor: 'white',
              color: 'primary.main',
              '&:hover': {
                backgroundColor: 'grey.100',
              },
            }}
          >
            Get Started
          </Button>
          <Button
            variant="outlined"
            size="large"
            component={Link}
            to="/login"
            sx={{
              borderColor: 'white',
              color: 'white',
              '&:hover': {
                borderColor: 'white',
                backgroundColor: 'rgba(255, 255, 255, 0.1)',
              },
            }}
          >
            Sign In
          </Button>
        </Box>
      </Box>

      {/* Features Section */}
      <Box sx={{ mb: 6 }}>
        <Typography variant="h2" component="h2" textAlign="center" gutterBottom>
          Why Choose StudyHub?
        </Typography>
        <Typography
          variant="h6"
          component="p"
          textAlign="center"
          color="text.secondary"
          sx={{ mb: 4 }}
        >
          Discover the tools and features that make learning more effective and enjoyable
        </Typography>

        <Grid container spacing={3}>
          {features.map((feature, index) => (
            <Grid item xs={12} md={6} lg={4} key={index}>
              <Card
                sx={{
                  height: '100%',
                  display: 'flex',
                  flexDirection: 'column',
                  transition: 'transform 0.2s',
                  '&:hover': {
                    transform: 'translateY(-4px)',
                    boxShadow: 3,
                  },
                }}
              >
                <CardContent sx={{ flexGrow: 1, textAlign: 'center' }}>
                  <Box sx={{ mb: 2 }}>
                    {feature.icon}
                  </Box>
                  <Typography variant="h6" component="h3" gutterBottom>
                    {feature.title}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    {feature.description}
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
          ))}
        </Grid>
      </Box>

      {/* Call to Action */}
      <Paper
        sx={{
          p: 4,
          textAlign: 'center',
          backgroundColor: 'primary.main',
          color: 'white',
          mb: 4,
        }}
      >
        <Typography variant="h4" component="h2" gutterBottom>
          Ready to Start Learning?
        </Typography>
        <Typography variant="body1" sx={{ mb: 3 }}>
          Join thousands of students who are already using StudyHub to achieve their academic goals.
        </Typography>
        <Button
          variant="contained"
          size="large"
          component={Link}
          to="/register"
          sx={{
            backgroundColor: 'white',
            color: 'primary.main',
            '&:hover': {
              backgroundColor: 'grey.100',
            },
          }}
        >
          Create Your Account
        </Button>
      </Paper>
    </Container>
  );
};

export default Home;
