import React from 'react';
import {
  Container,
  Typography,
  Box,
  Button,
  Card,
  CardContent,
  Paper,
} from '@mui/material';
import { Grid } from '@mui/material';
import {
  Forum,
  Explore,
  TrendingUp,
  People,
  Chat,
  Public,
} from '@mui/icons-material';
import { Link } from 'react-router-dom';

const Home: React.FC = () => {
  const features = [
    {
      icon: <Forum fontSize="large" color="primary" />,
      title: 'Opinion Communities',
      description: 'Join communities based on your interests and share your thoughts with like-minded people.',
    },
    {
      icon: <Chat fontSize="large" color="primary" />,
      title: 'Real-time Discussions',
      description: 'Engage in live conversations and debates on topics that matter to you.',
    },
    {
      icon: <Explore fontSize="large" color="primary" />,
      title: 'Discover Content',
      description: 'Explore trending topics, popular posts, and discover new perspectives.',
    },
    {
      icon: <TrendingUp fontSize="large" color="primary" />,
      title: 'Trending Topics',
      description: 'Stay updated with what\'s hot and trending across all communities.',
    },
    {
      icon: <People fontSize="large" color="primary" />,
      title: 'Connect & Follow',
      description: 'Build your network by following interesting people and growing your audience.',
    },
    {
      icon: <Public fontSize="large" color="primary" />,
      title: 'Share Your Voice',
      description: 'Express your opinions and engage in meaningful conversations that matter.',
    },
  ];

  return (
    <Container maxWidth="lg">
      {/* Hero Section */}
      <Box
        sx={{
          textAlign: 'center',
          py: 8,
          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
          borderRadius: 2,
          color: 'white',
          mb: 6,
        }}
      >
        <Typography variant="h1" component="h1" gutterBottom>
          Welcome to ChatRoom
        </Typography>
        <Typography variant="h5" component="p" sx={{ mb: 4, opacity: 0.9 }}>
          Share your voice, connect with communities, and engage in meaningful conversations
        </Typography>
        <Box sx={{ display: 'flex', gap: 2, justifyContent: 'center' }}>
          <Button
            variant="contained"
            size="large"
            component={Link}
            to="/register"
            sx={{
              backgroundColor: 'white',
              color: 'primary.main',
              '&:hover': {
                backgroundColor: 'grey.100',
              },
            }}
          >
            Get Started
          </Button>
          <Button
            variant="outlined"
            size="large"
            component={Link}
            to="/login"
            sx={{
              borderColor: 'white',
              color: 'white',
              '&:hover': {
                borderColor: 'white',
                backgroundColor: 'rgba(255, 255, 255, 0.1)',
              },
            }}
          >
            Sign In
          </Button>
        </Box>
      </Box>

      {/* Features Section */}
      <Box sx={{ mb: 6 }}>
        <Typography variant="h2" component="h2" textAlign="center" gutterBottom>
          Why Choose ChatRoom?
        </Typography>
        <Typography
          variant="h6"
          component="p"
          textAlign="center"
          color="text.secondary"
          sx={{ mb: 4 }}
        >
          Discover the features that make sharing opinions and connecting with others seamless and engaging
        </Typography>

        <Grid container spacing={3}>
          {features.map((feature, index) => (
            <Grid item xs={12} md={6} lg={4} key={index}>
              <Card
                sx={{
                  height: '100%',
                  display: 'flex',
                  flexDirection: 'column',
                  transition: 'transform 0.2s',
                  '&:hover': {
                    transform: 'translateY(-4px)',
                    boxShadow: 3,
                  },
                }}
              >
                <CardContent sx={{ flexGrow: 1, textAlign: 'center' }}>
                  <Box sx={{ mb: 2 }}>
                    {feature.icon}
                  </Box>
                  <Typography variant="h6" component="h3" gutterBottom>
                    {feature.title}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    {feature.description}
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
          ))}
        </Grid>
      </Box>

      {/* Call to Action */}
      <Paper
        sx={{
          p: 4,
          textAlign: 'center',
          backgroundColor: 'primary.main',
          color: 'white',
          mb: 4,
        }}
      >
        <Typography variant="h4" component="h2" gutterBottom>
          Ready to Share Your Voice?
        </Typography>
        <Typography variant="body1" sx={{ mb: 3 }}>
          Join thousands of people who are already using ChatRoom to connect and share their opinions.
        </Typography>
        <Button
          variant="contained"
          size="large"
          component={Link}
          to="/register"
          sx={{
            backgroundColor: 'white',
            color: 'primary.main',
            '&:hover': {
              backgroundColor: 'grey.100',
            },
          }}
        >
          Create Your Account
        </Button>
      </Paper>
    </Container>
  );
};

export default Home;
