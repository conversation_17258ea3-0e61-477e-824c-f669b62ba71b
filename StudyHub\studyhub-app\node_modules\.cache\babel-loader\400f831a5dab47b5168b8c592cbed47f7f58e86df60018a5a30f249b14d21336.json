{"ast": null, "code": "import { constructFrom } from \"../constructFrom.js\";\nexport function normalizeDates(context) {\n  for (var _len = arguments.length, dates = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n    dates[_key - 1] = arguments[_key];\n  }\n  const normalize = constructFrom.bind(null, context || dates.find(date => typeof date === \"object\"));\n  return dates.map(normalize);\n}", "map": {"version": 3, "names": ["constructFrom", "normalizeDates", "context", "_len", "arguments", "length", "dates", "Array", "_key", "normalize", "bind", "find", "date", "map"], "sources": ["C:/Users/<USER>/OneDrive/ドキュメント/Projects/StudyHub/studyhub-app/node_modules/date-fns/_lib/normalizeDates.js"], "sourcesContent": ["import { constructFrom } from \"../constructFrom.js\";\n\nexport function normalizeDates(context, ...dates) {\n  const normalize = constructFrom.bind(\n    null,\n    context || dates.find((date) => typeof date === \"object\"),\n  );\n  return dates.map(normalize);\n}\n"], "mappings": "AAAA,SAASA,aAAa,QAAQ,qBAAqB;AAEnD,OAAO,SAASC,cAAcA,CAACC,OAAO,EAAY;EAAA,SAAAC,IAAA,GAAAC,SAAA,CAAAC,MAAA,EAAPC,KAAK,OAAAC,KAAA,CAAAJ,IAAA,OAAAA,IAAA,WAAAK,IAAA,MAAAA,IAAA,GAAAL,IAAA,EAAAK,IAAA;IAALF,KAAK,CAAAE,IAAA,QAAAJ,SAAA,CAAAI,IAAA;EAAA;EAC9C,MAAMC,SAAS,GAAGT,aAAa,CAACU,IAAI,CAClC,IAAI,EACJR,OAAO,IAAII,KAAK,CAACK,IAAI,CAAEC,IAAI,IAAK,OAAOA,IAAI,KAAK,QAAQ,CAC1D,CAAC;EACD,OAAON,KAAK,CAACO,GAAG,CAACJ,SAAS,CAAC;AAC7B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}