{"ast": null, "code": "import React from'react';import{Container,Typography,<PERSON>,<PERSON><PERSON>,Card,CardContent,Paper}from'@mui/material';import{Grid}from'@mui/material';import{Group,LibraryBooks,Quiz,Schedule,TrendingUp,People}from'@mui/icons-material';import{Link}from'react-router-dom';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const Home=()=>{const features=[{icon:/*#__PURE__*/_jsx(Group,{fontSize:\"large\",color:\"primary\"}),title:'Study Groups',description:'Join or create study groups with fellow students. Collaborate and learn together.'},{icon:/*#__PURE__*/_jsx(LibraryBooks,{fontSize:\"large\",color:\"primary\"}),title:'Resource Library',description:'Access a vast collection of study materials, notes, and educational resources.'},{icon:/*#__PURE__*/_jsx(Quiz,{fontSize:\"large\",color:\"primary\"}),title:'Practice Tests',description:'Test your knowledge with interactive quizzes and practice exams.'},{icon:/*#__PURE__*/_jsx(Schedule,{fontSize:\"large\",color:\"primary\"}),title:'Study Scheduler',description:'Plan your study sessions and track your progress with our smart scheduler.'},{icon:/*#__PURE__*/_jsx(TrendingUp,{fontSize:\"large\",color:\"primary\"}),title:'Progress Tracking',description:'Monitor your learning progress and identify areas for improvement.'},{icon:/*#__PURE__*/_jsx(People,{fontSize:\"large\",color:\"primary\"}),title:'Community',description:'Connect with a community of learners and share knowledge.'}];return/*#__PURE__*/_jsxs(Container,{maxWidth:\"lg\",children:[/*#__PURE__*/_jsxs(Box,{sx:{textAlign:'center',py:8,background:'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',borderRadius:2,color:'white',mb:6},children:[/*#__PURE__*/_jsx(Typography,{variant:\"h1\",component:\"h1\",gutterBottom:true,children:\"Welcome to StudyHub\"}),/*#__PURE__*/_jsx(Typography,{variant:\"h5\",component:\"p\",sx:{mb:4,opacity:0.9},children:\"Your ultimate platform for collaborative learning and academic success\"}),/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',gap:2,justifyContent:'center'},children:[/*#__PURE__*/_jsx(Button,{variant:\"contained\",size:\"large\",component:Link,to:\"/register\",sx:{backgroundColor:'white',color:'primary.main','&:hover':{backgroundColor:'grey.100'}},children:\"Get Started\"}),/*#__PURE__*/_jsx(Button,{variant:\"outlined\",size:\"large\",component:Link,to:\"/login\",sx:{borderColor:'white',color:'white','&:hover':{borderColor:'white',backgroundColor:'rgba(255, 255, 255, 0.1)'}},children:\"Sign In\"})]})]}),/*#__PURE__*/_jsxs(Box,{sx:{mb:6},children:[/*#__PURE__*/_jsx(Typography,{variant:\"h2\",component:\"h2\",textAlign:\"center\",gutterBottom:true,children:\"Why Choose StudyHub?\"}),/*#__PURE__*/_jsx(Typography,{variant:\"h6\",component:\"p\",textAlign:\"center\",color:\"text.secondary\",sx:{mb:4},children:\"Discover the tools and features that make learning more effective and enjoyable\"}),/*#__PURE__*/_jsx(Grid,{container:true,spacing:3,children:features.map((feature,index)=>/*#__PURE__*/_jsx(Grid,{size:{xs:12,md:6,lg:4},children:/*#__PURE__*/_jsx(Card,{sx:{height:'100%',display:'flex',flexDirection:'column',transition:'transform 0.2s','&:hover':{transform:'translateY(-4px)',boxShadow:3}},children:/*#__PURE__*/_jsxs(CardContent,{sx:{flexGrow:1,textAlign:'center'},children:[/*#__PURE__*/_jsx(Box,{sx:{mb:2},children:feature.icon}),/*#__PURE__*/_jsx(Typography,{variant:\"h6\",component:\"h3\",gutterBottom:true,children:feature.title}),/*#__PURE__*/_jsx(Typography,{variant:\"body2\",color:\"text.secondary\",children:feature.description})]})})},index))})]}),/*#__PURE__*/_jsxs(Paper,{sx:{p:4,textAlign:'center',backgroundColor:'primary.main',color:'white',mb:4},children:[/*#__PURE__*/_jsx(Typography,{variant:\"h4\",component:\"h2\",gutterBottom:true,children:\"Ready to Start Learning?\"}),/*#__PURE__*/_jsx(Typography,{variant:\"body1\",sx:{mb:3},children:\"Join thousands of students who are already using StudyHub to achieve their academic goals.\"}),/*#__PURE__*/_jsx(Button,{variant:\"contained\",size:\"large\",component:Link,to:\"/register\",sx:{backgroundColor:'white',color:'primary.main','&:hover':{backgroundColor:'grey.100'}},children:\"Create Your Account\"})]})]});};export default Home;", "map": {"version": 3, "names": ["React", "Container", "Typography", "Box", "<PERSON><PERSON>", "Card", "<PERSON><PERSON><PERSON><PERSON>", "Paper", "Grid", "Group", "LibraryBooks", "Quiz", "Schedule", "TrendingUp", "People", "Link", "jsx", "_jsx", "jsxs", "_jsxs", "Home", "features", "icon", "fontSize", "color", "title", "description", "max<PERSON><PERSON><PERSON>", "children", "sx", "textAlign", "py", "background", "borderRadius", "mb", "variant", "component", "gutterBottom", "opacity", "display", "gap", "justifyContent", "size", "to", "backgroundColor", "borderColor", "container", "spacing", "map", "feature", "index", "xs", "md", "lg", "height", "flexDirection", "transition", "transform", "boxShadow", "flexGrow", "p"], "sources": ["C:/Users/<USER>/OneDrive/ドキュメント/Projects/StudyHub/studyhub-app/src/pages/Home.tsx"], "sourcesContent": ["import React from 'react';\nimport {\n  Container,\n  Typo<PERSON>,\n  Box,\n  <PERSON><PERSON>,\n  Card,\n  CardContent,\n  CardActions,\n  Paper,\n} from '@mui/material';\nimport { Grid } from '@mui/material';\nimport {\n  Group,\n  LibraryBooks,\n  Quiz,\n  Schedule,\n  TrendingUp,\n  People,\n} from '@mui/icons-material';\nimport { Link } from 'react-router-dom';\n\nconst Home: React.FC = () => {\n  const features = [\n    {\n      icon: <Group fontSize=\"large\" color=\"primary\" />,\n      title: 'Study Groups',\n      description: 'Join or create study groups with fellow students. Collaborate and learn together.',\n    },\n    {\n      icon: <LibraryBooks fontSize=\"large\" color=\"primary\" />,\n      title: 'Resource Library',\n      description: 'Access a vast collection of study materials, notes, and educational resources.',\n    },\n    {\n      icon: <Quiz fontSize=\"large\" color=\"primary\" />,\n      title: 'Practice Tests',\n      description: 'Test your knowledge with interactive quizzes and practice exams.',\n    },\n    {\n      icon: <Schedule fontSize=\"large\" color=\"primary\" />,\n      title: 'Study Scheduler',\n      description: 'Plan your study sessions and track your progress with our smart scheduler.',\n    },\n    {\n      icon: <TrendingUp fontSize=\"large\" color=\"primary\" />,\n      title: 'Progress Tracking',\n      description: 'Monitor your learning progress and identify areas for improvement.',\n    },\n    {\n      icon: <People fontSize=\"large\" color=\"primary\" />,\n      title: 'Community',\n      description: 'Connect with a community of learners and share knowledge.',\n    },\n  ];\n\n  return (\n    <Container maxWidth=\"lg\">\n      {/* Hero Section */}\n      <Box\n        sx={{\n          textAlign: 'center',\n          py: 8,\n          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n          borderRadius: 2,\n          color: 'white',\n          mb: 6,\n        }}\n      >\n        <Typography variant=\"h1\" component=\"h1\" gutterBottom>\n          Welcome to StudyHub\n        </Typography>\n        <Typography variant=\"h5\" component=\"p\" sx={{ mb: 4, opacity: 0.9 }}>\n          Your ultimate platform for collaborative learning and academic success\n        </Typography>\n        <Box sx={{ display: 'flex', gap: 2, justifyContent: 'center' }}>\n          <Button\n            variant=\"contained\"\n            size=\"large\"\n            component={Link}\n            to=\"/register\"\n            sx={{\n              backgroundColor: 'white',\n              color: 'primary.main',\n              '&:hover': {\n                backgroundColor: 'grey.100',\n              },\n            }}\n          >\n            Get Started\n          </Button>\n          <Button\n            variant=\"outlined\"\n            size=\"large\"\n            component={Link}\n            to=\"/login\"\n            sx={{\n              borderColor: 'white',\n              color: 'white',\n              '&:hover': {\n                borderColor: 'white',\n                backgroundColor: 'rgba(255, 255, 255, 0.1)',\n              },\n            }}\n          >\n            Sign In\n          </Button>\n        </Box>\n      </Box>\n\n      {/* Features Section */}\n      <Box sx={{ mb: 6 }}>\n        <Typography variant=\"h2\" component=\"h2\" textAlign=\"center\" gutterBottom>\n          Why Choose StudyHub?\n        </Typography>\n        <Typography\n          variant=\"h6\"\n          component=\"p\"\n          textAlign=\"center\"\n          color=\"text.secondary\"\n          sx={{ mb: 4 }}\n        >\n          Discover the tools and features that make learning more effective and enjoyable\n        </Typography>\n\n        <Grid container spacing={3}>\n          {features.map((feature, index) => (\n            <Grid size={{ xs: 12, md: 6, lg: 4 }} key={index}>\n              <Card\n                sx={{\n                  height: '100%',\n                  display: 'flex',\n                  flexDirection: 'column',\n                  transition: 'transform 0.2s',\n                  '&:hover': {\n                    transform: 'translateY(-4px)',\n                    boxShadow: 3,\n                  },\n                }}\n              >\n                <CardContent sx={{ flexGrow: 1, textAlign: 'center' }}>\n                  <Box sx={{ mb: 2 }}>\n                    {feature.icon}\n                  </Box>\n                  <Typography variant=\"h6\" component=\"h3\" gutterBottom>\n                    {feature.title}\n                  </Typography>\n                  <Typography variant=\"body2\" color=\"text.secondary\">\n                    {feature.description}\n                  </Typography>\n                </CardContent>\n              </Card>\n            </Grid>\n          ))}\n        </Grid>\n      </Box>\n\n      {/* Call to Action */}\n      <Paper\n        sx={{\n          p: 4,\n          textAlign: 'center',\n          backgroundColor: 'primary.main',\n          color: 'white',\n          mb: 4,\n        }}\n      >\n        <Typography variant=\"h4\" component=\"h2\" gutterBottom>\n          Ready to Start Learning?\n        </Typography>\n        <Typography variant=\"body1\" sx={{ mb: 3 }}>\n          Join thousands of students who are already using StudyHub to achieve their academic goals.\n        </Typography>\n        <Button\n          variant=\"contained\"\n          size=\"large\"\n          component={Link}\n          to=\"/register\"\n          sx={{\n            backgroundColor: 'white',\n            color: 'primary.main',\n            '&:hover': {\n              backgroundColor: 'grey.100',\n            },\n          }}\n        >\n          Create Your Account\n        </Button>\n      </Paper>\n    </Container>\n  );\n};\n\nexport default Home;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,OACEC,SAAS,CACTC,UAAU,CACVC,GAAG,CACHC,MAAM,CACNC,IAAI,CACJC,WAAW,CAEXC,KAAK,KACA,eAAe,CACtB,OAASC,IAAI,KAAQ,eAAe,CACpC,OACEC,KAAK,CACLC,YAAY,CACZC,IAAI,CACJC,QAAQ,CACRC,UAAU,CACVC,MAAM,KACD,qBAAqB,CAC5B,OAASC,IAAI,KAAQ,kBAAkB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAExC,KAAM,CAAAC,IAAc,CAAGA,CAAA,GAAM,CAC3B,KAAM,CAAAC,QAAQ,CAAG,CACf,CACEC,IAAI,cAAEL,IAAA,CAACR,KAAK,EAACc,QAAQ,CAAC,OAAO,CAACC,KAAK,CAAC,SAAS,CAAE,CAAC,CAChDC,KAAK,CAAE,cAAc,CACrBC,WAAW,CAAE,mFACf,CAAC,CACD,CACEJ,IAAI,cAAEL,IAAA,CAACP,YAAY,EAACa,QAAQ,CAAC,OAAO,CAACC,KAAK,CAAC,SAAS,CAAE,CAAC,CACvDC,KAAK,CAAE,kBAAkB,CACzBC,WAAW,CAAE,gFACf,CAAC,CACD,CACEJ,IAAI,cAAEL,IAAA,CAACN,IAAI,EAACY,QAAQ,CAAC,OAAO,CAACC,KAAK,CAAC,SAAS,CAAE,CAAC,CAC/CC,KAAK,CAAE,gBAAgB,CACvBC,WAAW,CAAE,kEACf,CAAC,CACD,CACEJ,IAAI,cAAEL,IAAA,CAACL,QAAQ,EAACW,QAAQ,CAAC,OAAO,CAACC,KAAK,CAAC,SAAS,CAAE,CAAC,CACnDC,KAAK,CAAE,iBAAiB,CACxBC,WAAW,CAAE,4EACf,CAAC,CACD,CACEJ,IAAI,cAAEL,IAAA,CAACJ,UAAU,EAACU,QAAQ,CAAC,OAAO,CAACC,KAAK,CAAC,SAAS,CAAE,CAAC,CACrDC,KAAK,CAAE,mBAAmB,CAC1BC,WAAW,CAAE,oEACf,CAAC,CACD,CACEJ,IAAI,cAAEL,IAAA,CAACH,MAAM,EAACS,QAAQ,CAAC,OAAO,CAACC,KAAK,CAAC,SAAS,CAAE,CAAC,CACjDC,KAAK,CAAE,WAAW,CAClBC,WAAW,CAAE,2DACf,CAAC,CACF,CAED,mBACEP,KAAA,CAAClB,SAAS,EAAC0B,QAAQ,CAAC,IAAI,CAAAC,QAAA,eAEtBT,KAAA,CAAChB,GAAG,EACF0B,EAAE,CAAE,CACFC,SAAS,CAAE,QAAQ,CACnBC,EAAE,CAAE,CAAC,CACLC,UAAU,CAAE,mDAAmD,CAC/DC,YAAY,CAAE,CAAC,CACfT,KAAK,CAAE,OAAO,CACdU,EAAE,CAAE,CACN,CAAE,CAAAN,QAAA,eAEFX,IAAA,CAACf,UAAU,EAACiC,OAAO,CAAC,IAAI,CAACC,SAAS,CAAC,IAAI,CAACC,YAAY,MAAAT,QAAA,CAAC,qBAErD,CAAY,CAAC,cACbX,IAAA,CAACf,UAAU,EAACiC,OAAO,CAAC,IAAI,CAACC,SAAS,CAAC,GAAG,CAACP,EAAE,CAAE,CAAEK,EAAE,CAAE,CAAC,CAAEI,OAAO,CAAE,GAAI,CAAE,CAAAV,QAAA,CAAC,wEAEpE,CAAY,CAAC,cACbT,KAAA,CAAChB,GAAG,EAAC0B,EAAE,CAAE,CAAEU,OAAO,CAAE,MAAM,CAAEC,GAAG,CAAE,CAAC,CAAEC,cAAc,CAAE,QAAS,CAAE,CAAAb,QAAA,eAC7DX,IAAA,CAACb,MAAM,EACL+B,OAAO,CAAC,WAAW,CACnBO,IAAI,CAAC,OAAO,CACZN,SAAS,CAAErB,IAAK,CAChB4B,EAAE,CAAC,WAAW,CACdd,EAAE,CAAE,CACFe,eAAe,CAAE,OAAO,CACxBpB,KAAK,CAAE,cAAc,CACrB,SAAS,CAAE,CACToB,eAAe,CAAE,UACnB,CACF,CAAE,CAAAhB,QAAA,CACH,aAED,CAAQ,CAAC,cACTX,IAAA,CAACb,MAAM,EACL+B,OAAO,CAAC,UAAU,CAClBO,IAAI,CAAC,OAAO,CACZN,SAAS,CAAErB,IAAK,CAChB4B,EAAE,CAAC,QAAQ,CACXd,EAAE,CAAE,CACFgB,WAAW,CAAE,OAAO,CACpBrB,KAAK,CAAE,OAAO,CACd,SAAS,CAAE,CACTqB,WAAW,CAAE,OAAO,CACpBD,eAAe,CAAE,0BACnB,CACF,CAAE,CAAAhB,QAAA,CACH,SAED,CAAQ,CAAC,EACN,CAAC,EACH,CAAC,cAGNT,KAAA,CAAChB,GAAG,EAAC0B,EAAE,CAAE,CAAEK,EAAE,CAAE,CAAE,CAAE,CAAAN,QAAA,eACjBX,IAAA,CAACf,UAAU,EAACiC,OAAO,CAAC,IAAI,CAACC,SAAS,CAAC,IAAI,CAACN,SAAS,CAAC,QAAQ,CAACO,YAAY,MAAAT,QAAA,CAAC,sBAExE,CAAY,CAAC,cACbX,IAAA,CAACf,UAAU,EACTiC,OAAO,CAAC,IAAI,CACZC,SAAS,CAAC,GAAG,CACbN,SAAS,CAAC,QAAQ,CAClBN,KAAK,CAAC,gBAAgB,CACtBK,EAAE,CAAE,CAAEK,EAAE,CAAE,CAAE,CAAE,CAAAN,QAAA,CACf,iFAED,CAAY,CAAC,cAEbX,IAAA,CAACT,IAAI,EAACsC,SAAS,MAACC,OAAO,CAAE,CAAE,CAAAnB,QAAA,CACxBP,QAAQ,CAAC2B,GAAG,CAAC,CAACC,OAAO,CAAEC,KAAK,gBAC3BjC,IAAA,CAACT,IAAI,EAACkC,IAAI,CAAE,CAAES,EAAE,CAAE,EAAE,CAAEC,EAAE,CAAE,CAAC,CAAEC,EAAE,CAAE,CAAE,CAAE,CAAAzB,QAAA,cACnCX,IAAA,CAACZ,IAAI,EACHwB,EAAE,CAAE,CACFyB,MAAM,CAAE,MAAM,CACdf,OAAO,CAAE,MAAM,CACfgB,aAAa,CAAE,QAAQ,CACvBC,UAAU,CAAE,gBAAgB,CAC5B,SAAS,CAAE,CACTC,SAAS,CAAE,kBAAkB,CAC7BC,SAAS,CAAE,CACb,CACF,CAAE,CAAA9B,QAAA,cAEFT,KAAA,CAACb,WAAW,EAACuB,EAAE,CAAE,CAAE8B,QAAQ,CAAE,CAAC,CAAE7B,SAAS,CAAE,QAAS,CAAE,CAAAF,QAAA,eACpDX,IAAA,CAACd,GAAG,EAAC0B,EAAE,CAAE,CAAEK,EAAE,CAAE,CAAE,CAAE,CAAAN,QAAA,CAChBqB,OAAO,CAAC3B,IAAI,CACV,CAAC,cACNL,IAAA,CAACf,UAAU,EAACiC,OAAO,CAAC,IAAI,CAACC,SAAS,CAAC,IAAI,CAACC,YAAY,MAAAT,QAAA,CACjDqB,OAAO,CAACxB,KAAK,CACJ,CAAC,cACbR,IAAA,CAACf,UAAU,EAACiC,OAAO,CAAC,OAAO,CAACX,KAAK,CAAC,gBAAgB,CAAAI,QAAA,CAC/CqB,OAAO,CAACvB,WAAW,CACV,CAAC,EACF,CAAC,CACV,CAAC,EAxBkCwB,KAyBrC,CACP,CAAC,CACE,CAAC,EACJ,CAAC,cAGN/B,KAAA,CAACZ,KAAK,EACJsB,EAAE,CAAE,CACF+B,CAAC,CAAE,CAAC,CACJ9B,SAAS,CAAE,QAAQ,CACnBc,eAAe,CAAE,cAAc,CAC/BpB,KAAK,CAAE,OAAO,CACdU,EAAE,CAAE,CACN,CAAE,CAAAN,QAAA,eAEFX,IAAA,CAACf,UAAU,EAACiC,OAAO,CAAC,IAAI,CAACC,SAAS,CAAC,IAAI,CAACC,YAAY,MAAAT,QAAA,CAAC,0BAErD,CAAY,CAAC,cACbX,IAAA,CAACf,UAAU,EAACiC,OAAO,CAAC,OAAO,CAACN,EAAE,CAAE,CAAEK,EAAE,CAAE,CAAE,CAAE,CAAAN,QAAA,CAAC,4FAE3C,CAAY,CAAC,cACbX,IAAA,CAACb,MAAM,EACL+B,OAAO,CAAC,WAAW,CACnBO,IAAI,CAAC,OAAO,CACZN,SAAS,CAAErB,IAAK,CAChB4B,EAAE,CAAC,WAAW,CACdd,EAAE,CAAE,CACFe,eAAe,CAAE,OAAO,CACxBpB,KAAK,CAAE,cAAc,CACrB,SAAS,CAAE,CACToB,eAAe,CAAE,UACnB,CACF,CAAE,CAAAhB,QAAA,CACH,qBAED,CAAQ,CAAC,EACJ,CAAC,EACC,CAAC,CAEhB,CAAC,CAED,cAAe,CAAAR,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}