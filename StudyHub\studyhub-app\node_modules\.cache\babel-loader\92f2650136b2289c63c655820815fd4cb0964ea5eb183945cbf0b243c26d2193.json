{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\\\u30C9\\u30AD\\u30E5\\u30E1\\u30F3\\u30C8\\\\Projects\\\\StudyHub\\\\studyhub-app\\\\src\\\\pages\\\\Communities.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Container, Grid, Card, CardContent, CardActions, Typography, Button, TextField, Box, Avatar, Chip, Dialog, DialogTitle, DialogContent, DialogActions, FormControl, InputLabel, Select, MenuItem, Paper, Divider, AvatarGroup } from '@mui/material';\nimport { Add, People, Public, Lock, Search } from '@mui/icons-material';\nimport { useAuth } from '../contexts/AuthContext';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Communities = () => {\n  _s();\n  const {\n    currentUser\n  } = useAuth();\n  const [communities, setCommunities] = useState([]);\n  const [searchQuery, setSearchQuery] = useState('');\n  const [selectedCategory, setSelectedCategory] = useState('all');\n  const [createDialogOpen, setCreateDialogOpen] = useState(false);\n  const [newCommunity, setNewCommunity] = useState({\n    name: '',\n    description: '',\n    category: '',\n    isPublic: true,\n    tags: []\n  });\n  const categories = ['Technology', 'Environment', 'Lifestyle', 'Wellness', 'Finance', 'Education', 'Arts', 'Sports', 'Politics', 'Science'];\n  useEffect(() => {\n    // Sample communities data\n    const sampleCommunities = [{\n      id: '1',\n      name: 'Climate Action Network',\n      description: 'Discussing climate change solutions and environmental activism',\n      category: 'Environment',\n      memberCount: 1247,\n      postCount: 89,\n      isPublic: true,\n      isMember: false,\n      recentMembers: ['user1', 'user2', 'user3'],\n      tags: ['climate', 'environment', 'activism'],\n      createdAt: new Date('2024-01-15')\n    }, {\n      id: '2',\n      name: 'AI & Future Tech',\n      description: 'Exploring artificial intelligence and emerging technologies',\n      category: 'Technology',\n      memberCount: 892,\n      postCount: 156,\n      isPublic: true,\n      isMember: true,\n      recentMembers: ['user4', 'user5', 'user6'],\n      tags: ['AI', 'technology', 'innovation'],\n      createdAt: new Date('2024-02-01')\n    }, {\n      id: '3',\n      name: 'Mental Wellness Hub',\n      description: 'Supporting mental health and sharing wellness strategies',\n      category: 'Wellness',\n      memberCount: 634,\n      postCount: 78,\n      isPublic: true,\n      isMember: false,\n      recentMembers: ['user7', 'user8', 'user9'],\n      tags: ['mentalhealth', 'wellness', 'support'],\n      createdAt: new Date('2024-01-20')\n    }, {\n      id: '4',\n      name: 'Crypto Discussions',\n      description: 'Analyzing cryptocurrency trends and blockchain technology',\n      category: 'Finance',\n      memberCount: 445,\n      postCount: 203,\n      isPublic: true,\n      isMember: true,\n      recentMembers: ['user10', 'user11', 'user12'],\n      tags: ['crypto', 'blockchain', 'finance'],\n      createdAt: new Date('2024-02-10')\n    }, {\n      id: '5',\n      name: 'Sustainable Living',\n      description: 'Tips and discussions about eco-friendly lifestyle choices',\n      category: 'Lifestyle',\n      memberCount: 389,\n      postCount: 67,\n      isPublic: true,\n      isMember: false,\n      recentMembers: ['user13', 'user14', 'user15'],\n      tags: ['sustainability', 'lifestyle', 'eco'],\n      createdAt: new Date('2024-01-25')\n    }];\n    setCommunities(sampleCommunities);\n  }, []);\n  const filteredCommunities = communities.filter(community => {\n    const matchesSearch = community.name.toLowerCase().includes(searchQuery.toLowerCase()) || community.description.toLowerCase().includes(searchQuery.toLowerCase()) || community.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()));\n    const matchesCategory = selectedCategory === 'all' || community.category === selectedCategory;\n    return matchesSearch && matchesCategory;\n  });\n  const handleJoinCommunity = communityId => {\n    setCommunities(communities.map(community => community.id === communityId ? {\n      ...community,\n      isMember: !community.isMember,\n      memberCount: community.isMember ? community.memberCount - 1 : community.memberCount + 1\n    } : community));\n  };\n  const handleCreateCommunity = () => {\n    if (!newCommunity.name.trim() || !newCommunity.description.trim()) return;\n    const community = {\n      id: Date.now().toString(),\n      name: newCommunity.name,\n      description: newCommunity.description,\n      category: newCommunity.category,\n      memberCount: 1,\n      postCount: 0,\n      isPublic: newCommunity.isPublic,\n      isMember: true,\n      recentMembers: [(currentUser === null || currentUser === void 0 ? void 0 : currentUser.uid) || ''],\n      tags: newCommunity.tags,\n      createdAt: new Date()\n    };\n    setCommunities([community, ...communities]);\n    setNewCommunity({\n      name: '',\n      description: '',\n      category: '',\n      isPublic: true,\n      tags: []\n    });\n    setCreateDialogOpen(false);\n  };\n  const formatNumber = num => {\n    if (num >= 1000) {\n      return (num / 1000).toFixed(1) + 'K';\n    }\n    return num.toString();\n  };\n  return /*#__PURE__*/_jsxDEV(Container, {\n    maxWidth: \"lg\",\n    children: /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        py: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h4\",\n        gutterBottom: true,\n        sx: {\n          display: 'flex',\n          alignItems: 'center',\n          gap: 1\n        },\n        children: [/*#__PURE__*/_jsxDEV(People, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 208,\n          columnNumber: 11\n        }, this), \"Communities\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 207,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"body1\",\n        color: \"text.secondary\",\n        sx: {\n          mb: 4\n        },\n        children: \"Join communities to share opinions and connect with like-minded people\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 211,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: 3,\n          mb: 4\n        },\n        children: /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 2,\n          alignItems: \"center\",\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              placeholder: \"Search communities...\",\n              value: searchQuery,\n              onChange: e => setSearchQuery(e.target.value),\n              InputProps: {\n                startAdornment: /*#__PURE__*/_jsxDEV(Search, {\n                  sx: {\n                    mr: 1,\n                    color: 'text.secondary'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 225,\n                  columnNumber: 35\n                }, this)\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 219,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 218,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 4,\n            children: /*#__PURE__*/_jsxDEV(FormControl, {\n              fullWidth: true,\n              children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                children: \"Category\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 231,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Select, {\n                value: selectedCategory,\n                label: \"Category\",\n                onChange: e => setSelectedCategory(e.target.value),\n                children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"all\",\n                  children: \"All Categories\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 237,\n                  columnNumber: 19\n                }, this), categories.map(category => /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: category,\n                  children: category\n                }, category, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 239,\n                  columnNumber: 21\n                }, this))]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 232,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 230,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 229,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 2,\n            children: /*#__PURE__*/_jsxDEV(Button, {\n              fullWidth: true,\n              variant: \"contained\",\n              startIcon: /*#__PURE__*/_jsxDEV(Add, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 250,\n                columnNumber: 28\n              }, this),\n              onClick: () => setCreateDialogOpen(true),\n              disabled: !currentUser,\n              children: \"Create\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 247,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 246,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 217,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 216,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: 3,\n        children: filteredCommunities.map(community => /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          md: 6,\n          lg: 4,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            sx: {\n              height: '100%',\n              display: 'flex',\n              flexDirection: 'column'\n            },\n            children: [/*#__PURE__*/_jsxDEV(CardContent, {\n              sx: {\n                flexGrow: 1\n              },\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  alignItems: 'center',\n                  mb: 2\n                },\n                children: [/*#__PURE__*/_jsxDEV(Avatar, {\n                  sx: {\n                    mr: 2,\n                    bgcolor: 'primary.main'\n                  },\n                  children: community.name[0]\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 267,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    flexGrow: 1\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"h6\",\n                    component: \"h2\",\n                    children: community.name\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 271,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      display: 'flex',\n                      alignItems: 'center',\n                      gap: 1\n                    },\n                    children: [community.isPublic ? /*#__PURE__*/_jsxDEV(Public, {\n                      fontSize: \"small\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 275,\n                      columnNumber: 47\n                    }, this) : /*#__PURE__*/_jsxDEV(Lock, {\n                      fontSize: \"small\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 275,\n                      columnNumber: 77\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"caption\",\n                      color: \"text.secondary\",\n                      children: community.isPublic ? 'Public' : 'Private'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 276,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 274,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 270,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 266,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                color: \"text.secondary\",\n                sx: {\n                  mb: 2\n                },\n                children: community.description\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 283,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  mb: 2\n                },\n                children: community.tags.map(tag => /*#__PURE__*/_jsxDEV(Chip, {\n                  label: `#${tag}`,\n                  size: \"small\",\n                  sx: {\n                    mr: 1,\n                    mb: 1\n                  },\n                  color: \"primary\",\n                  variant: \"outlined\"\n                }, tag, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 289,\n                  columnNumber: 23\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 287,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Divider, {\n                sx: {\n                  my: 2\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 300,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  justifyContent: 'space-between',\n                  alignItems: 'center',\n                  mb: 2\n                },\n                children: [/*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    display: 'flex',\n                    gap: 2\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      textAlign: 'center'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"h6\",\n                      color: \"primary\",\n                      children: formatNumber(community.memberCount)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 305,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"caption\",\n                      color: \"text.secondary\",\n                      children: \"Members\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 308,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 304,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      textAlign: 'center'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"h6\",\n                      color: \"primary\",\n                      children: community.postCount\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 313,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"caption\",\n                      color: \"text.secondary\",\n                      children: \"Posts\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 316,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 312,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 303,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(AvatarGroup, {\n                  max: 3,\n                  sx: {\n                    '& .MuiAvatar-root': {\n                      width: 24,\n                      height: 24\n                    }\n                  },\n                  children: community.recentMembers.map((member, index) => /*#__PURE__*/_jsxDEV(Avatar, {\n                    sx: {\n                      fontSize: '0.75rem'\n                    },\n                    children: String.fromCharCode(65 + index)\n                  }, member, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 323,\n                    columnNumber: 25\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 321,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 302,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 265,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(CardActions, {\n              sx: {\n                p: 2,\n                pt: 0\n              },\n              children: /*#__PURE__*/_jsxDEV(Button, {\n                fullWidth: true,\n                variant: community.isMember ? \"outlined\" : \"contained\",\n                onClick: () => handleJoinCommunity(community.id),\n                disabled: !currentUser,\n                children: community.isMember ? 'Leave' : 'Join'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 332,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 331,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 264,\n            columnNumber: 15\n          }, this)\n        }, community.id, false, {\n          fileName: _jsxFileName,\n          lineNumber: 263,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 261,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n        open: createDialogOpen,\n        onClose: () => setCreateDialogOpen(false),\n        maxWidth: \"sm\",\n        fullWidth: true,\n        children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n          children: \"Create New Community\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 348,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n          children: [/*#__PURE__*/_jsxDEV(TextField, {\n            autoFocus: true,\n            margin: \"dense\",\n            label: \"Community Name\",\n            fullWidth: true,\n            variant: \"outlined\",\n            value: newCommunity.name,\n            onChange: e => setNewCommunity({\n              ...newCommunity,\n              name: e.target.value\n            }),\n            sx: {\n              mb: 2\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 350,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            margin: \"dense\",\n            label: \"Description\",\n            fullWidth: true,\n            multiline: true,\n            rows: 3,\n            variant: \"outlined\",\n            value: newCommunity.description,\n            onChange: e => setNewCommunity({\n              ...newCommunity,\n              description: e.target.value\n            }),\n            sx: {\n              mb: 2\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 361,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(FormControl, {\n            fullWidth: true,\n            sx: {\n              mb: 2\n            },\n            children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n              children: \"Category\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 374,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Select, {\n              value: newCommunity.category,\n              label: \"Category\",\n              onChange: e => setNewCommunity({\n                ...newCommunity,\n                category: e.target.value\n              }),\n              children: categories.map(category => /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: category,\n                children: category\n              }, category, false, {\n                fileName: _jsxFileName,\n                lineNumber: 381,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 375,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 373,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(FormControl, {\n            fullWidth: true,\n            children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n              children: \"Privacy\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 389,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Select, {\n              value: newCommunity.isPublic ? 'public' : 'private',\n              label: \"Privacy\",\n              onChange: e => setNewCommunity({\n                ...newCommunity,\n                isPublic: e.target.value === 'public'\n              }),\n              children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"public\",\n                children: \"Public - Anyone can join\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 395,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"private\",\n                children: \"Private - Invite only\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 396,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 390,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 388,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 349,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            onClick: () => setCreateDialogOpen(false),\n            children: \"Cancel\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 401,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleCreateCommunity,\n            variant: \"contained\",\n            disabled: !newCommunity.name.trim() || !newCommunity.description.trim() || !newCommunity.category,\n            children: \"Create Community\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 402,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 400,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 347,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 206,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 205,\n    columnNumber: 5\n  }, this);\n};\n_s(Communities, \"boEaRwntSsIdEHs8ZSkt9PFS4x0=\", false, function () {\n  return [useAuth];\n});\n_c = Communities;\nexport default Communities;\nvar _c;\n$RefreshReg$(_c, \"Communities\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Container", "Grid", "Card", "<PERSON><PERSON><PERSON><PERSON>", "CardActions", "Typography", "<PERSON><PERSON>", "TextField", "Box", "Avatar", "Chip", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "FormControl", "InputLabel", "Select", "MenuItem", "Paper", "Divider", "AvatarGroup", "Add", "People", "Public", "Lock", "Search", "useAuth", "jsxDEV", "_jsxDEV", "Communities", "_s", "currentUser", "communities", "setCommunities", "searchQuery", "setSearch<PERSON>uery", "selectedCate<PERSON><PERSON>", "setSelectedCategory", "createDialogOpen", "setCreateDialogOpen", "newCommunity", "setNewCommunity", "name", "description", "category", "isPublic", "tags", "categories", "sampleCommunities", "id", "memberCount", "postCount", "isMember", "recentMembers", "createdAt", "Date", "filteredCommunities", "filter", "community", "matchesSearch", "toLowerCase", "includes", "some", "tag", "matchesCategory", "handleJoinCommunity", "communityId", "map", "handleCreateCommunity", "trim", "now", "toString", "uid", "formatNumber", "num", "toFixed", "max<PERSON><PERSON><PERSON>", "children", "sx", "py", "variant", "gutterBottom", "display", "alignItems", "gap", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "color", "mb", "p", "container", "spacing", "item", "xs", "md", "fullWidth", "placeholder", "value", "onChange", "e", "target", "InputProps", "startAdornment", "mr", "label", "startIcon", "onClick", "disabled", "lg", "height", "flexDirection", "flexGrow", "bgcolor", "component", "fontSize", "size", "my", "justifyContent", "textAlign", "max", "width", "member", "index", "String", "fromCharCode", "pt", "open", "onClose", "autoFocus", "margin", "multiline", "rows", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/ドキュメント/Projects/StudyHub/studyhub-app/src/pages/Communities.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  <PERSON><PERSON><PERSON>,\n  <PERSON>rid,\n  Card,\n  CardContent,\n  CardActions,\n  Typography,\n  Button,\n  TextField,\n  Box,\n  Avatar,\n  Chip,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  FormControl,\n  InputLabel,\n  Select,\n  MenuItem,\n  Paper,\n  Divider,\n  AvatarGroup,\n} from '@mui/material';\nimport {\n  Add,\n  People,\n  Public,\n  Lock,\n  Search,\n} from '@mui/icons-material';\nimport { useAuth } from '../contexts/AuthContext';\n\ninterface Community {\n  id: string;\n  name: string;\n  description: string;\n  category: string;\n  memberCount: number;\n  postCount: number;\n  isPublic: boolean;\n  isMember: boolean;\n  avatar?: string;\n  recentMembers: string[];\n  tags: string[];\n  createdAt: Date;\n}\n\nconst Communities: React.FC = () => {\n  const { currentUser } = useAuth();\n  const [communities, setCommunities] = useState<Community[]>([]);\n  const [searchQuery, setSearchQuery] = useState('');\n  const [selectedCategory, setSelectedCategory] = useState('all');\n  const [createDialogOpen, setCreateDialogOpen] = useState(false);\n  const [newCommunity, setNewCommunity] = useState({\n    name: '',\n    description: '',\n    category: '',\n    isPublic: true,\n    tags: [] as string[],\n  });\n\n  const categories = [\n    'Technology',\n    'Environment',\n    'Lifestyle',\n    'Wellness',\n    'Finance',\n    'Education',\n    'Arts',\n    'Sports',\n    'Politics',\n    'Science',\n  ];\n\n  useEffect(() => {\n    // Sample communities data\n    const sampleCommunities: Community[] = [\n      {\n        id: '1',\n        name: 'Climate Action Network',\n        description: 'Discussing climate change solutions and environmental activism',\n        category: 'Environment',\n        memberCount: 1247,\n        postCount: 89,\n        isPublic: true,\n        isMember: false,\n        recentMembers: ['user1', 'user2', 'user3'],\n        tags: ['climate', 'environment', 'activism'],\n        createdAt: new Date('2024-01-15'),\n      },\n      {\n        id: '2',\n        name: 'AI & Future Tech',\n        description: 'Exploring artificial intelligence and emerging technologies',\n        category: 'Technology',\n        memberCount: 892,\n        postCount: 156,\n        isPublic: true,\n        isMember: true,\n        recentMembers: ['user4', 'user5', 'user6'],\n        tags: ['AI', 'technology', 'innovation'],\n        createdAt: new Date('2024-02-01'),\n      },\n      {\n        id: '3',\n        name: 'Mental Wellness Hub',\n        description: 'Supporting mental health and sharing wellness strategies',\n        category: 'Wellness',\n        memberCount: 634,\n        postCount: 78,\n        isPublic: true,\n        isMember: false,\n        recentMembers: ['user7', 'user8', 'user9'],\n        tags: ['mentalhealth', 'wellness', 'support'],\n        createdAt: new Date('2024-01-20'),\n      },\n      {\n        id: '4',\n        name: 'Crypto Discussions',\n        description: 'Analyzing cryptocurrency trends and blockchain technology',\n        category: 'Finance',\n        memberCount: 445,\n        postCount: 203,\n        isPublic: true,\n        isMember: true,\n        recentMembers: ['user10', 'user11', 'user12'],\n        tags: ['crypto', 'blockchain', 'finance'],\n        createdAt: new Date('2024-02-10'),\n      },\n      {\n        id: '5',\n        name: 'Sustainable Living',\n        description: 'Tips and discussions about eco-friendly lifestyle choices',\n        category: 'Lifestyle',\n        memberCount: 389,\n        postCount: 67,\n        isPublic: true,\n        isMember: false,\n        recentMembers: ['user13', 'user14', 'user15'],\n        tags: ['sustainability', 'lifestyle', 'eco'],\n        createdAt: new Date('2024-01-25'),\n      },\n    ];\n    setCommunities(sampleCommunities);\n  }, []);\n\n  const filteredCommunities = communities.filter(community => {\n    const matchesSearch = community.name.toLowerCase().includes(searchQuery.toLowerCase()) ||\n                         community.description.toLowerCase().includes(searchQuery.toLowerCase()) ||\n                         community.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()));\n    const matchesCategory = selectedCategory === 'all' || community.category === selectedCategory;\n    return matchesSearch && matchesCategory;\n  });\n\n  const handleJoinCommunity = (communityId: string) => {\n    setCommunities(communities.map(community =>\n      community.id === communityId\n        ? { \n            ...community, \n            isMember: !community.isMember,\n            memberCount: community.isMember ? community.memberCount - 1 : community.memberCount + 1\n          }\n        : community\n    ));\n  };\n\n  const handleCreateCommunity = () => {\n    if (!newCommunity.name.trim() || !newCommunity.description.trim()) return;\n\n    const community: Community = {\n      id: Date.now().toString(),\n      name: newCommunity.name,\n      description: newCommunity.description,\n      category: newCommunity.category,\n      memberCount: 1,\n      postCount: 0,\n      isPublic: newCommunity.isPublic,\n      isMember: true,\n      recentMembers: [currentUser?.uid || ''],\n      tags: newCommunity.tags,\n      createdAt: new Date(),\n    };\n\n    setCommunities([community, ...communities]);\n    setNewCommunity({\n      name: '',\n      description: '',\n      category: '',\n      isPublic: true,\n      tags: [],\n    });\n    setCreateDialogOpen(false);\n  };\n\n  const formatNumber = (num: number) => {\n    if (num >= 1000) {\n      return (num / 1000).toFixed(1) + 'K';\n    }\n    return num.toString();\n  };\n\n  return (\n    <Container maxWidth=\"lg\">\n      <Box sx={{ py: 3 }}>\n        <Typography variant=\"h4\" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\n          <People />\n          Communities\n        </Typography>\n        <Typography variant=\"body1\" color=\"text.secondary\" sx={{ mb: 4 }}>\n          Join communities to share opinions and connect with like-minded people\n        </Typography>\n\n        {/* Search and Filter */}\n        <Paper sx={{ p: 3, mb: 4 }}>\n          <Grid container spacing={2} alignItems=\"center\">\n            <Grid item xs={12} md={6}>\n              <TextField\n                fullWidth\n                placeholder=\"Search communities...\"\n                value={searchQuery}\n                onChange={(e) => setSearchQuery(e.target.value)}\n                InputProps={{\n                  startAdornment: <Search sx={{ mr: 1, color: 'text.secondary' }} />,\n                }}\n              />\n            </Grid>\n            <Grid item xs={12} md={4}>\n              <FormControl fullWidth>\n                <InputLabel>Category</InputLabel>\n                <Select\n                  value={selectedCategory}\n                  label=\"Category\"\n                  onChange={(e) => setSelectedCategory(e.target.value)}\n                >\n                  <MenuItem value=\"all\">All Categories</MenuItem>\n                  {categories.map((category) => (\n                    <MenuItem key={category} value={category}>\n                      {category}\n                    </MenuItem>\n                  ))}\n                </Select>\n              </FormControl>\n            </Grid>\n            <Grid item xs={12} md={2}>\n              <Button\n                fullWidth\n                variant=\"contained\"\n                startIcon={<Add />}\n                onClick={() => setCreateDialogOpen(true)}\n                disabled={!currentUser}\n              >\n                Create\n              </Button>\n            </Grid>\n          </Grid>\n        </Paper>\n\n        {/* Communities Grid */}\n        <Grid container spacing={3}>\n          {filteredCommunities.map((community) => (\n            <Grid item xs={12} md={6} lg={4} key={community.id}>\n              <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>\n                <CardContent sx={{ flexGrow: 1 }}>\n                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>\n                    <Avatar sx={{ mr: 2, bgcolor: 'primary.main' }}>\n                      {community.name[0]}\n                    </Avatar>\n                    <Box sx={{ flexGrow: 1 }}>\n                      <Typography variant=\"h6\" component=\"h2\">\n                        {community.name}\n                      </Typography>\n                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\n                        {community.isPublic ? <Public fontSize=\"small\" /> : <Lock fontSize=\"small\" />}\n                        <Typography variant=\"caption\" color=\"text.secondary\">\n                          {community.isPublic ? 'Public' : 'Private'}\n                        </Typography>\n                      </Box>\n                    </Box>\n                  </Box>\n\n                  <Typography variant=\"body2\" color=\"text.secondary\" sx={{ mb: 2 }}>\n                    {community.description}\n                  </Typography>\n\n                  <Box sx={{ mb: 2 }}>\n                    {community.tags.map((tag) => (\n                      <Chip\n                        key={tag}\n                        label={`#${tag}`}\n                        size=\"small\"\n                        sx={{ mr: 1, mb: 1 }}\n                        color=\"primary\"\n                        variant=\"outlined\"\n                      />\n                    ))}\n                  </Box>\n\n                  <Divider sx={{ my: 2 }} />\n\n                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>\n                    <Box sx={{ display: 'flex', gap: 2 }}>\n                      <Box sx={{ textAlign: 'center' }}>\n                        <Typography variant=\"h6\" color=\"primary\">\n                          {formatNumber(community.memberCount)}\n                        </Typography>\n                        <Typography variant=\"caption\" color=\"text.secondary\">\n                          Members\n                        </Typography>\n                      </Box>\n                      <Box sx={{ textAlign: 'center' }}>\n                        <Typography variant=\"h6\" color=\"primary\">\n                          {community.postCount}\n                        </Typography>\n                        <Typography variant=\"caption\" color=\"text.secondary\">\n                          Posts\n                        </Typography>\n                      </Box>\n                    </Box>\n                    <AvatarGroup max={3} sx={{ '& .MuiAvatar-root': { width: 24, height: 24 } }}>\n                      {community.recentMembers.map((member, index) => (\n                        <Avatar key={member} sx={{ fontSize: '0.75rem' }}>\n                          {String.fromCharCode(65 + index)}\n                        </Avatar>\n                      ))}\n                    </AvatarGroup>\n                  </Box>\n                </CardContent>\n\n                <CardActions sx={{ p: 2, pt: 0 }}>\n                  <Button\n                    fullWidth\n                    variant={community.isMember ? \"outlined\" : \"contained\"}\n                    onClick={() => handleJoinCommunity(community.id)}\n                    disabled={!currentUser}\n                  >\n                    {community.isMember ? 'Leave' : 'Join'}\n                  </Button>\n                </CardActions>\n              </Card>\n            </Grid>\n          ))}\n        </Grid>\n\n        {/* Create Community Dialog */}\n        <Dialog open={createDialogOpen} onClose={() => setCreateDialogOpen(false)} maxWidth=\"sm\" fullWidth>\n          <DialogTitle>Create New Community</DialogTitle>\n          <DialogContent>\n            <TextField\n              autoFocus\n              margin=\"dense\"\n              label=\"Community Name\"\n              fullWidth\n              variant=\"outlined\"\n              value={newCommunity.name}\n              onChange={(e) => setNewCommunity({ ...newCommunity, name: e.target.value })}\n              sx={{ mb: 2 }}\n            />\n            \n            <TextField\n              margin=\"dense\"\n              label=\"Description\"\n              fullWidth\n              multiline\n              rows={3}\n              variant=\"outlined\"\n              value={newCommunity.description}\n              onChange={(e) => setNewCommunity({ ...newCommunity, description: e.target.value })}\n              sx={{ mb: 2 }}\n            />\n\n            <FormControl fullWidth sx={{ mb: 2 }}>\n              <InputLabel>Category</InputLabel>\n              <Select\n                value={newCommunity.category}\n                label=\"Category\"\n                onChange={(e) => setNewCommunity({ ...newCommunity, category: e.target.value })}\n              >\n                {categories.map((category) => (\n                  <MenuItem key={category} value={category}>\n                    {category}\n                  </MenuItem>\n                ))}\n              </Select>\n            </FormControl>\n\n            <FormControl fullWidth>\n              <InputLabel>Privacy</InputLabel>\n              <Select\n                value={newCommunity.isPublic ? 'public' : 'private'}\n                label=\"Privacy\"\n                onChange={(e) => setNewCommunity({ ...newCommunity, isPublic: e.target.value === 'public' })}\n              >\n                <MenuItem value=\"public\">Public - Anyone can join</MenuItem>\n                <MenuItem value=\"private\">Private - Invite only</MenuItem>\n              </Select>\n            </FormControl>\n          </DialogContent>\n          <DialogActions>\n            <Button onClick={() => setCreateDialogOpen(false)}>Cancel</Button>\n            <Button \n              onClick={handleCreateCommunity} \n              variant=\"contained\"\n              disabled={!newCommunity.name.trim() || !newCommunity.description.trim() || !newCommunity.category}\n            >\n              Create Community\n            </Button>\n          </DialogActions>\n        </Dialog>\n      </Box>\n    </Container>\n  );\n};\n\nexport default Communities;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,SAAS,EACTC,IAAI,EACJC,IAAI,EACJC,WAAW,EACXC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,SAAS,EACTC,GAAG,EACHC,MAAM,EACNC,IAAI,EACJC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,QAAQ,EACRC,KAAK,EACLC,OAAO,EACPC,WAAW,QACN,eAAe;AACtB,SACEC,GAAG,EACHC,MAAM,EACNC,MAAM,EACNC,IAAI,EACJC,MAAM,QACD,qBAAqB;AAC5B,SAASC,OAAO,QAAQ,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAiBlD,MAAMC,WAAqB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAClC,MAAM;IAAEC;EAAY,CAAC,GAAGL,OAAO,CAAC,CAAC;EACjC,MAAM,CAACM,WAAW,EAAEC,cAAc,CAAC,GAAGpC,QAAQ,CAAc,EAAE,CAAC;EAC/D,MAAM,CAACqC,WAAW,EAAEC,cAAc,CAAC,GAAGtC,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACuC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGxC,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAACyC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG1C,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAAC2C,YAAY,EAAEC,eAAe,CAAC,GAAG5C,QAAQ,CAAC;IAC/C6C,IAAI,EAAE,EAAE;IACRC,WAAW,EAAE,EAAE;IACfC,QAAQ,EAAE,EAAE;IACZC,QAAQ,EAAE,IAAI;IACdC,IAAI,EAAE;EACR,CAAC,CAAC;EAEF,MAAMC,UAAU,GAAG,CACjB,YAAY,EACZ,aAAa,EACb,WAAW,EACX,UAAU,EACV,SAAS,EACT,WAAW,EACX,MAAM,EACN,QAAQ,EACR,UAAU,EACV,SAAS,CACV;EAEDjD,SAAS,CAAC,MAAM;IACd;IACA,MAAMkD,iBAA8B,GAAG,CACrC;MACEC,EAAE,EAAE,GAAG;MACPP,IAAI,EAAE,wBAAwB;MAC9BC,WAAW,EAAE,gEAAgE;MAC7EC,QAAQ,EAAE,aAAa;MACvBM,WAAW,EAAE,IAAI;MACjBC,SAAS,EAAE,EAAE;MACbN,QAAQ,EAAE,IAAI;MACdO,QAAQ,EAAE,KAAK;MACfC,aAAa,EAAE,CAAC,OAAO,EAAE,OAAO,EAAE,OAAO,CAAC;MAC1CP,IAAI,EAAE,CAAC,SAAS,EAAE,aAAa,EAAE,UAAU,CAAC;MAC5CQ,SAAS,EAAE,IAAIC,IAAI,CAAC,YAAY;IAClC,CAAC,EACD;MACEN,EAAE,EAAE,GAAG;MACPP,IAAI,EAAE,kBAAkB;MACxBC,WAAW,EAAE,6DAA6D;MAC1EC,QAAQ,EAAE,YAAY;MACtBM,WAAW,EAAE,GAAG;MAChBC,SAAS,EAAE,GAAG;MACdN,QAAQ,EAAE,IAAI;MACdO,QAAQ,EAAE,IAAI;MACdC,aAAa,EAAE,CAAC,OAAO,EAAE,OAAO,EAAE,OAAO,CAAC;MAC1CP,IAAI,EAAE,CAAC,IAAI,EAAE,YAAY,EAAE,YAAY,CAAC;MACxCQ,SAAS,EAAE,IAAIC,IAAI,CAAC,YAAY;IAClC,CAAC,EACD;MACEN,EAAE,EAAE,GAAG;MACPP,IAAI,EAAE,qBAAqB;MAC3BC,WAAW,EAAE,0DAA0D;MACvEC,QAAQ,EAAE,UAAU;MACpBM,WAAW,EAAE,GAAG;MAChBC,SAAS,EAAE,EAAE;MACbN,QAAQ,EAAE,IAAI;MACdO,QAAQ,EAAE,KAAK;MACfC,aAAa,EAAE,CAAC,OAAO,EAAE,OAAO,EAAE,OAAO,CAAC;MAC1CP,IAAI,EAAE,CAAC,cAAc,EAAE,UAAU,EAAE,SAAS,CAAC;MAC7CQ,SAAS,EAAE,IAAIC,IAAI,CAAC,YAAY;IAClC,CAAC,EACD;MACEN,EAAE,EAAE,GAAG;MACPP,IAAI,EAAE,oBAAoB;MAC1BC,WAAW,EAAE,2DAA2D;MACxEC,QAAQ,EAAE,SAAS;MACnBM,WAAW,EAAE,GAAG;MAChBC,SAAS,EAAE,GAAG;MACdN,QAAQ,EAAE,IAAI;MACdO,QAAQ,EAAE,IAAI;MACdC,aAAa,EAAE,CAAC,QAAQ,EAAE,QAAQ,EAAE,QAAQ,CAAC;MAC7CP,IAAI,EAAE,CAAC,QAAQ,EAAE,YAAY,EAAE,SAAS,CAAC;MACzCQ,SAAS,EAAE,IAAIC,IAAI,CAAC,YAAY;IAClC,CAAC,EACD;MACEN,EAAE,EAAE,GAAG;MACPP,IAAI,EAAE,oBAAoB;MAC1BC,WAAW,EAAE,2DAA2D;MACxEC,QAAQ,EAAE,WAAW;MACrBM,WAAW,EAAE,GAAG;MAChBC,SAAS,EAAE,EAAE;MACbN,QAAQ,EAAE,IAAI;MACdO,QAAQ,EAAE,KAAK;MACfC,aAAa,EAAE,CAAC,QAAQ,EAAE,QAAQ,EAAE,QAAQ,CAAC;MAC7CP,IAAI,EAAE,CAAC,gBAAgB,EAAE,WAAW,EAAE,KAAK,CAAC;MAC5CQ,SAAS,EAAE,IAAIC,IAAI,CAAC,YAAY;IAClC,CAAC,CACF;IACDtB,cAAc,CAACe,iBAAiB,CAAC;EACnC,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMQ,mBAAmB,GAAGxB,WAAW,CAACyB,MAAM,CAACC,SAAS,IAAI;IAC1D,MAAMC,aAAa,GAAGD,SAAS,CAAChB,IAAI,CAACkB,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC3B,WAAW,CAAC0B,WAAW,CAAC,CAAC,CAAC,IACjEF,SAAS,CAACf,WAAW,CAACiB,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC3B,WAAW,CAAC0B,WAAW,CAAC,CAAC,CAAC,IACvEF,SAAS,CAACZ,IAAI,CAACgB,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACH,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC3B,WAAW,CAAC0B,WAAW,CAAC,CAAC,CAAC,CAAC;IACtG,MAAMI,eAAe,GAAG5B,gBAAgB,KAAK,KAAK,IAAIsB,SAAS,CAACd,QAAQ,KAAKR,gBAAgB;IAC7F,OAAOuB,aAAa,IAAIK,eAAe;EACzC,CAAC,CAAC;EAEF,MAAMC,mBAAmB,GAAIC,WAAmB,IAAK;IACnDjC,cAAc,CAACD,WAAW,CAACmC,GAAG,CAACT,SAAS,IACtCA,SAAS,CAACT,EAAE,KAAKiB,WAAW,GACxB;MACE,GAAGR,SAAS;MACZN,QAAQ,EAAE,CAACM,SAAS,CAACN,QAAQ;MAC7BF,WAAW,EAAEQ,SAAS,CAACN,QAAQ,GAAGM,SAAS,CAACR,WAAW,GAAG,CAAC,GAAGQ,SAAS,CAACR,WAAW,GAAG;IACxF,CAAC,GACDQ,SACN,CAAC,CAAC;EACJ,CAAC;EAED,MAAMU,qBAAqB,GAAGA,CAAA,KAAM;IAClC,IAAI,CAAC5B,YAAY,CAACE,IAAI,CAAC2B,IAAI,CAAC,CAAC,IAAI,CAAC7B,YAAY,CAACG,WAAW,CAAC0B,IAAI,CAAC,CAAC,EAAE;IAEnE,MAAMX,SAAoB,GAAG;MAC3BT,EAAE,EAAEM,IAAI,CAACe,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC;MACzB7B,IAAI,EAAEF,YAAY,CAACE,IAAI;MACvBC,WAAW,EAAEH,YAAY,CAACG,WAAW;MACrCC,QAAQ,EAAEJ,YAAY,CAACI,QAAQ;MAC/BM,WAAW,EAAE,CAAC;MACdC,SAAS,EAAE,CAAC;MACZN,QAAQ,EAAEL,YAAY,CAACK,QAAQ;MAC/BO,QAAQ,EAAE,IAAI;MACdC,aAAa,EAAE,CAAC,CAAAtB,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEyC,GAAG,KAAI,EAAE,CAAC;MACvC1B,IAAI,EAAEN,YAAY,CAACM,IAAI;MACvBQ,SAAS,EAAE,IAAIC,IAAI,CAAC;IACtB,CAAC;IAEDtB,cAAc,CAAC,CAACyB,SAAS,EAAE,GAAG1B,WAAW,CAAC,CAAC;IAC3CS,eAAe,CAAC;MACdC,IAAI,EAAE,EAAE;MACRC,WAAW,EAAE,EAAE;MACfC,QAAQ,EAAE,EAAE;MACZC,QAAQ,EAAE,IAAI;MACdC,IAAI,EAAE;IACR,CAAC,CAAC;IACFP,mBAAmB,CAAC,KAAK,CAAC;EAC5B,CAAC;EAED,MAAMkC,YAAY,GAAIC,GAAW,IAAK;IACpC,IAAIA,GAAG,IAAI,IAAI,EAAE;MACf,OAAO,CAACA,GAAG,GAAG,IAAI,EAAEC,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;IACtC;IACA,OAAOD,GAAG,CAACH,QAAQ,CAAC,CAAC;EACvB,CAAC;EAED,oBACE3C,OAAA,CAAC7B,SAAS;IAAC6E,QAAQ,EAAC,IAAI;IAAAC,QAAA,eACtBjD,OAAA,CAACrB,GAAG;MAACuE,EAAE,EAAE;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAF,QAAA,gBACjBjD,OAAA,CAACxB,UAAU;QAAC4E,OAAO,EAAC,IAAI;QAACC,YAAY;QAACH,EAAE,EAAE;UAAEI,OAAO,EAAE,MAAM;UAAEC,UAAU,EAAE,QAAQ;UAAEC,GAAG,EAAE;QAAE,CAAE;QAAAP,QAAA,gBAC1FjD,OAAA,CAACN,MAAM;UAAA+D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAEZ;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACb5D,OAAA,CAACxB,UAAU;QAAC4E,OAAO,EAAC,OAAO;QAACS,KAAK,EAAC,gBAAgB;QAACX,EAAE,EAAE;UAAEY,EAAE,EAAE;QAAE,CAAE;QAAAb,QAAA,EAAC;MAElE;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAGb5D,OAAA,CAACV,KAAK;QAAC4D,EAAE,EAAE;UAAEa,CAAC,EAAE,CAAC;UAAED,EAAE,EAAE;QAAE,CAAE;QAAAb,QAAA,eACzBjD,OAAA,CAAC5B,IAAI;UAAC4F,SAAS;UAACC,OAAO,EAAE,CAAE;UAACV,UAAU,EAAC,QAAQ;UAAAN,QAAA,gBAC7CjD,OAAA,CAAC5B,IAAI;YAAC8F,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAnB,QAAA,eACvBjD,OAAA,CAACtB,SAAS;cACR2F,SAAS;cACTC,WAAW,EAAC,uBAAuB;cACnCC,KAAK,EAAEjE,WAAY;cACnBkE,QAAQ,EAAGC,CAAC,IAAKlE,cAAc,CAACkE,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;cAChDI,UAAU,EAAE;gBACVC,cAAc,eAAE5E,OAAA,CAACH,MAAM;kBAACqD,EAAE,EAAE;oBAAE2B,EAAE,EAAE,CAAC;oBAAEhB,KAAK,EAAE;kBAAiB;gBAAE;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cACnE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACP5D,OAAA,CAAC5B,IAAI;YAAC8F,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAnB,QAAA,eACvBjD,OAAA,CAACd,WAAW;cAACmF,SAAS;cAAApB,QAAA,gBACpBjD,OAAA,CAACb,UAAU;gBAAA8D,QAAA,EAAC;cAAQ;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACjC5D,OAAA,CAACZ,MAAM;gBACLmF,KAAK,EAAE/D,gBAAiB;gBACxBsE,KAAK,EAAC,UAAU;gBAChBN,QAAQ,EAAGC,CAAC,IAAKhE,mBAAmB,CAACgE,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;gBAAAtB,QAAA,gBAErDjD,OAAA,CAACX,QAAQ;kBAACkF,KAAK,EAAC,KAAK;kBAAAtB,QAAA,EAAC;gBAAc;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC,EAC9CzC,UAAU,CAACoB,GAAG,CAAEvB,QAAQ,iBACvBhB,OAAA,CAACX,QAAQ;kBAAgBkF,KAAK,EAAEvD,QAAS;kBAAAiC,QAAA,EACtCjC;gBAAQ,GADIA,QAAQ;kBAAAyC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEb,CACX,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eACP5D,OAAA,CAAC5B,IAAI;YAAC8F,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAnB,QAAA,eACvBjD,OAAA,CAACvB,MAAM;cACL4F,SAAS;cACTjB,OAAO,EAAC,WAAW;cACnB2B,SAAS,eAAE/E,OAAA,CAACP,GAAG;gBAAAgE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACnBoB,OAAO,EAAEA,CAAA,KAAMrE,mBAAmB,CAAC,IAAI,CAAE;cACzCsE,QAAQ,EAAE,CAAC9E,WAAY;cAAA8C,QAAA,EACxB;YAED;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAGR5D,OAAA,CAAC5B,IAAI;QAAC4F,SAAS;QAACC,OAAO,EAAE,CAAE;QAAAhB,QAAA,EACxBrB,mBAAmB,CAACW,GAAG,CAAET,SAAS,iBACjC9B,OAAA,CAAC5B,IAAI;UAAC8F,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAACc,EAAE,EAAE,CAAE;UAAAjC,QAAA,eAC9BjD,OAAA,CAAC3B,IAAI;YAAC6E,EAAE,EAAE;cAAEiC,MAAM,EAAE,MAAM;cAAE7B,OAAO,EAAE,MAAM;cAAE8B,aAAa,EAAE;YAAS,CAAE;YAAAnC,QAAA,gBACrEjD,OAAA,CAAC1B,WAAW;cAAC4E,EAAE,EAAE;gBAAEmC,QAAQ,EAAE;cAAE,CAAE;cAAApC,QAAA,gBAC/BjD,OAAA,CAACrB,GAAG;gBAACuE,EAAE,EAAE;kBAAEI,OAAO,EAAE,MAAM;kBAAEC,UAAU,EAAE,QAAQ;kBAAEO,EAAE,EAAE;gBAAE,CAAE;gBAAAb,QAAA,gBACxDjD,OAAA,CAACpB,MAAM;kBAACsE,EAAE,EAAE;oBAAE2B,EAAE,EAAE,CAAC;oBAAES,OAAO,EAAE;kBAAe,CAAE;kBAAArC,QAAA,EAC5CnB,SAAS,CAAChB,IAAI,CAAC,CAAC;gBAAC;kBAAA2C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACZ,CAAC,eACT5D,OAAA,CAACrB,GAAG;kBAACuE,EAAE,EAAE;oBAAEmC,QAAQ,EAAE;kBAAE,CAAE;kBAAApC,QAAA,gBACvBjD,OAAA,CAACxB,UAAU;oBAAC4E,OAAO,EAAC,IAAI;oBAACmC,SAAS,EAAC,IAAI;oBAAAtC,QAAA,EACpCnB,SAAS,CAAChB;kBAAI;oBAAA2C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL,CAAC,eACb5D,OAAA,CAACrB,GAAG;oBAACuE,EAAE,EAAE;sBAAEI,OAAO,EAAE,MAAM;sBAAEC,UAAU,EAAE,QAAQ;sBAAEC,GAAG,EAAE;oBAAE,CAAE;oBAAAP,QAAA,GACxDnB,SAAS,CAACb,QAAQ,gBAAGjB,OAAA,CAACL,MAAM;sBAAC6F,QAAQ,EAAC;oBAAO;sBAAA/B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,gBAAG5D,OAAA,CAACJ,IAAI;sBAAC4F,QAAQ,EAAC;oBAAO;sBAAA/B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eAC7E5D,OAAA,CAACxB,UAAU;sBAAC4E,OAAO,EAAC,SAAS;sBAACS,KAAK,EAAC,gBAAgB;sBAAAZ,QAAA,EACjDnB,SAAS,CAACb,QAAQ,GAAG,QAAQ,GAAG;oBAAS;sBAAAwC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAChC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAEN5D,OAAA,CAACxB,UAAU;gBAAC4E,OAAO,EAAC,OAAO;gBAACS,KAAK,EAAC,gBAAgB;gBAACX,EAAE,EAAE;kBAAEY,EAAE,EAAE;gBAAE,CAAE;gBAAAb,QAAA,EAC9DnB,SAAS,CAACf;cAAW;gBAAA0C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACZ,CAAC,eAEb5D,OAAA,CAACrB,GAAG;gBAACuE,EAAE,EAAE;kBAAEY,EAAE,EAAE;gBAAE,CAAE;gBAAAb,QAAA,EAChBnB,SAAS,CAACZ,IAAI,CAACqB,GAAG,CAAEJ,GAAG,iBACtBnC,OAAA,CAACnB,IAAI;kBAEHiG,KAAK,EAAE,IAAI3C,GAAG,EAAG;kBACjBsD,IAAI,EAAC,OAAO;kBACZvC,EAAE,EAAE;oBAAE2B,EAAE,EAAE,CAAC;oBAAEf,EAAE,EAAE;kBAAE,CAAE;kBACrBD,KAAK,EAAC,SAAS;kBACfT,OAAO,EAAC;gBAAU,GALbjB,GAAG;kBAAAsB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAMT,CACF;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAEN5D,OAAA,CAACT,OAAO;gBAAC2D,EAAE,EAAE;kBAAEwC,EAAE,EAAE;gBAAE;cAAE;gBAAAjC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAE1B5D,OAAA,CAACrB,GAAG;gBAACuE,EAAE,EAAE;kBAAEI,OAAO,EAAE,MAAM;kBAAEqC,cAAc,EAAE,eAAe;kBAAEpC,UAAU,EAAE,QAAQ;kBAAEO,EAAE,EAAE;gBAAE,CAAE;gBAAAb,QAAA,gBACzFjD,OAAA,CAACrB,GAAG;kBAACuE,EAAE,EAAE;oBAAEI,OAAO,EAAE,MAAM;oBAAEE,GAAG,EAAE;kBAAE,CAAE;kBAAAP,QAAA,gBACnCjD,OAAA,CAACrB,GAAG;oBAACuE,EAAE,EAAE;sBAAE0C,SAAS,EAAE;oBAAS,CAAE;oBAAA3C,QAAA,gBAC/BjD,OAAA,CAACxB,UAAU;sBAAC4E,OAAO,EAAC,IAAI;sBAACS,KAAK,EAAC,SAAS;sBAAAZ,QAAA,EACrCJ,YAAY,CAACf,SAAS,CAACR,WAAW;oBAAC;sBAAAmC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC1B,CAAC,eACb5D,OAAA,CAACxB,UAAU;sBAAC4E,OAAO,EAAC,SAAS;sBAACS,KAAK,EAAC,gBAAgB;sBAAAZ,QAAA,EAAC;oBAErD;sBAAAQ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV,CAAC,eACN5D,OAAA,CAACrB,GAAG;oBAACuE,EAAE,EAAE;sBAAE0C,SAAS,EAAE;oBAAS,CAAE;oBAAA3C,QAAA,gBAC/BjD,OAAA,CAACxB,UAAU;sBAAC4E,OAAO,EAAC,IAAI;sBAACS,KAAK,EAAC,SAAS;sBAAAZ,QAAA,EACrCnB,SAAS,CAACP;oBAAS;sBAAAkC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACV,CAAC,eACb5D,OAAA,CAACxB,UAAU;sBAAC4E,OAAO,EAAC,SAAS;sBAACS,KAAK,EAAC,gBAAgB;sBAAAZ,QAAA,EAAC;oBAErD;sBAAAQ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACN5D,OAAA,CAACR,WAAW;kBAACqG,GAAG,EAAE,CAAE;kBAAC3C,EAAE,EAAE;oBAAE,mBAAmB,EAAE;sBAAE4C,KAAK,EAAE,EAAE;sBAAEX,MAAM,EAAE;oBAAG;kBAAE,CAAE;kBAAAlC,QAAA,EACzEnB,SAAS,CAACL,aAAa,CAACc,GAAG,CAAC,CAACwD,MAAM,EAAEC,KAAK,kBACzChG,OAAA,CAACpB,MAAM;oBAAcsE,EAAE,EAAE;sBAAEsC,QAAQ,EAAE;oBAAU,CAAE;oBAAAvC,QAAA,EAC9CgD,MAAM,CAACC,YAAY,CAAC,EAAE,GAAGF,KAAK;kBAAC,GADrBD,MAAM;oBAAAtC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAEX,CACT;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACS,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACX,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK,CAAC,eAEd5D,OAAA,CAACzB,WAAW;cAAC2E,EAAE,EAAE;gBAAEa,CAAC,EAAE,CAAC;gBAAEoC,EAAE,EAAE;cAAE,CAAE;cAAAlD,QAAA,eAC/BjD,OAAA,CAACvB,MAAM;gBACL4F,SAAS;gBACTjB,OAAO,EAAEtB,SAAS,CAACN,QAAQ,GAAG,UAAU,GAAG,WAAY;gBACvDwD,OAAO,EAAEA,CAAA,KAAM3C,mBAAmB,CAACP,SAAS,CAACT,EAAE,CAAE;gBACjD4D,QAAQ,EAAE,CAAC9E,WAAY;gBAAA8C,QAAA,EAEtBnB,SAAS,CAACN,QAAQ,GAAG,OAAO,GAAG;cAAM;gBAAAiC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC,GA9E6B9B,SAAS,CAACT,EAAE;UAAAoC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OA+E5C,CACP;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAGP5D,OAAA,CAAClB,MAAM;QAACsH,IAAI,EAAE1F,gBAAiB;QAAC2F,OAAO,EAAEA,CAAA,KAAM1F,mBAAmB,CAAC,KAAK,CAAE;QAACqC,QAAQ,EAAC,IAAI;QAACqB,SAAS;QAAApB,QAAA,gBAChGjD,OAAA,CAACjB,WAAW;UAAAkE,QAAA,EAAC;QAAoB;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa,CAAC,eAC/C5D,OAAA,CAAChB,aAAa;UAAAiE,QAAA,gBACZjD,OAAA,CAACtB,SAAS;YACR4H,SAAS;YACTC,MAAM,EAAC,OAAO;YACdzB,KAAK,EAAC,gBAAgB;YACtBT,SAAS;YACTjB,OAAO,EAAC,UAAU;YAClBmB,KAAK,EAAE3D,YAAY,CAACE,IAAK;YACzB0D,QAAQ,EAAGC,CAAC,IAAK5D,eAAe,CAAC;cAAE,GAAGD,YAAY;cAAEE,IAAI,EAAE2D,CAAC,CAACC,MAAM,CAACH;YAAM,CAAC,CAAE;YAC5ErB,EAAE,EAAE;cAAEY,EAAE,EAAE;YAAE;UAAE;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACf,CAAC,eAEF5D,OAAA,CAACtB,SAAS;YACR6H,MAAM,EAAC,OAAO;YACdzB,KAAK,EAAC,aAAa;YACnBT,SAAS;YACTmC,SAAS;YACTC,IAAI,EAAE,CAAE;YACRrD,OAAO,EAAC,UAAU;YAClBmB,KAAK,EAAE3D,YAAY,CAACG,WAAY;YAChCyD,QAAQ,EAAGC,CAAC,IAAK5D,eAAe,CAAC;cAAE,GAAGD,YAAY;cAAEG,WAAW,EAAE0D,CAAC,CAACC,MAAM,CAACH;YAAM,CAAC,CAAE;YACnFrB,EAAE,EAAE;cAAEY,EAAE,EAAE;YAAE;UAAE;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACf,CAAC,eAEF5D,OAAA,CAACd,WAAW;YAACmF,SAAS;YAACnB,EAAE,EAAE;cAAEY,EAAE,EAAE;YAAE,CAAE;YAAAb,QAAA,gBACnCjD,OAAA,CAACb,UAAU;cAAA8D,QAAA,EAAC;YAAQ;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACjC5D,OAAA,CAACZ,MAAM;cACLmF,KAAK,EAAE3D,YAAY,CAACI,QAAS;cAC7B8D,KAAK,EAAC,UAAU;cAChBN,QAAQ,EAAGC,CAAC,IAAK5D,eAAe,CAAC;gBAAE,GAAGD,YAAY;gBAAEI,QAAQ,EAAEyD,CAAC,CAACC,MAAM,CAACH;cAAM,CAAC,CAAE;cAAAtB,QAAA,EAE/E9B,UAAU,CAACoB,GAAG,CAAEvB,QAAQ,iBACvBhB,OAAA,CAACX,QAAQ;gBAAgBkF,KAAK,EAAEvD,QAAS;gBAAAiC,QAAA,EACtCjC;cAAQ,GADIA,QAAQ;gBAAAyC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEb,CACX;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAEd5D,OAAA,CAACd,WAAW;YAACmF,SAAS;YAAApB,QAAA,gBACpBjD,OAAA,CAACb,UAAU;cAAA8D,QAAA,EAAC;YAAO;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAChC5D,OAAA,CAACZ,MAAM;cACLmF,KAAK,EAAE3D,YAAY,CAACK,QAAQ,GAAG,QAAQ,GAAG,SAAU;cACpD6D,KAAK,EAAC,SAAS;cACfN,QAAQ,EAAGC,CAAC,IAAK5D,eAAe,CAAC;gBAAE,GAAGD,YAAY;gBAAEK,QAAQ,EAAEwD,CAAC,CAACC,MAAM,CAACH,KAAK,KAAK;cAAS,CAAC,CAAE;cAAAtB,QAAA,gBAE7FjD,OAAA,CAACX,QAAQ;gBAACkF,KAAK,EAAC,QAAQ;gBAAAtB,QAAA,EAAC;cAAwB;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eAC5D5D,OAAA,CAACX,QAAQ;gBAACkF,KAAK,EAAC,SAAS;gBAAAtB,QAAA,EAAC;cAAqB;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAChB5D,OAAA,CAACf,aAAa;UAAAgE,QAAA,gBACZjD,OAAA,CAACvB,MAAM;YAACuG,OAAO,EAAEA,CAAA,KAAMrE,mBAAmB,CAAC,KAAK,CAAE;YAAAsC,QAAA,EAAC;UAAM;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAClE5D,OAAA,CAACvB,MAAM;YACLuG,OAAO,EAAExC,qBAAsB;YAC/BY,OAAO,EAAC,WAAW;YACnB6B,QAAQ,EAAE,CAACrE,YAAY,CAACE,IAAI,CAAC2B,IAAI,CAAC,CAAC,IAAI,CAAC7B,YAAY,CAACG,WAAW,CAAC0B,IAAI,CAAC,CAAC,IAAI,CAAC7B,YAAY,CAACI,QAAS;YAAAiC,QAAA,EACnG;UAED;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACG,CAAC;AAEhB,CAAC;AAAC1D,EAAA,CA5WID,WAAqB;EAAA,QACDH,OAAO;AAAA;AAAA4G,EAAA,GAD3BzG,WAAqB;AA8W3B,eAAeA,WAAW;AAAC,IAAAyG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}