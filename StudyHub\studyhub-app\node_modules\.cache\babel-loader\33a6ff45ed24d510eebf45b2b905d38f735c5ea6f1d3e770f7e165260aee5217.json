{"ast": null, "code": "import _objectSpread from\"C:/Users/<USER>/OneDrive/\\u30C9\\u30AD\\u30E5\\u30E1\\u30F3\\u30C8/Projects/StudyHub/studyhub-app/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import React,{useState}from'react';import{Container,Paper,TextField,Button,Typography,Box,Link as MuiLink,Alert,CircularProgress,Grid}from'@mui/material';import{Link,useNavigate}from'react-router-dom';import{useAuth}from'../contexts/AuthContext';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const Register=()=>{const[formData,setFormData]=useState({firstName:'',lastName:'',email:'',password:'',confirmPassword:''});const[loading,setLoading]=useState(false);const[error,setError]=useState('');const navigate=useNavigate();const{register}=useAuth();const handleChange=e=>{setFormData(_objectSpread(_objectSpread({},formData),{},{[e.target.name]:e.target.value}));};const handleSubmit=async e=>{e.preventDefault();setLoading(true);setError('');// Validation\nif(formData.password!==formData.confirmPassword){setError('Passwords do not match');setLoading(false);return;}if(formData.password.length<6){setError('Password must be at least 6 characters long');setLoading(false);return;}try{await register(formData.email,formData.password,formData.firstName,formData.lastName);navigate('/dashboard');}catch(error){setError(error.message||'An error occurred during registration');}finally{setLoading(false);}};return/*#__PURE__*/_jsx(Container,{component:\"main\",maxWidth:\"sm\",children:/*#__PURE__*/_jsx(Box,{sx:{marginTop:8,display:'flex',flexDirection:'column',alignItems:'center'},children:/*#__PURE__*/_jsxs(Paper,{elevation:3,sx:{padding:4,display:'flex',flexDirection:'column',alignItems:'center',width:'100%'},children:[/*#__PURE__*/_jsx(Typography,{component:\"h1\",variant:\"h4\",gutterBottom:true,children:\"Sign Up\"}),/*#__PURE__*/_jsx(Typography,{variant:\"body2\",color:\"text.secondary\",sx:{mb:3},children:\"Create your StudyHub account\"}),error&&/*#__PURE__*/_jsx(Alert,{severity:\"error\",sx:{width:'100%',mb:2},children:error}),/*#__PURE__*/_jsxs(Box,{component:\"form\",onSubmit:handleSubmit,sx:{mt:1,width:'100%'},children:[/*#__PURE__*/_jsxs(Grid,{container:true,spacing:2,children:[/*#__PURE__*/_jsx(Grid,{xs:12,sm:6,children:/*#__PURE__*/_jsx(TextField,{autoComplete:\"given-name\",name:\"firstName\",required:true,fullWidth:true,id:\"firstName\",label:\"First Name\",autoFocus:true,value:formData.firstName,onChange:handleChange,disabled:loading})}),/*#__PURE__*/_jsx(Grid,{xs:12,sm:6,children:/*#__PURE__*/_jsx(TextField,{required:true,fullWidth:true,id:\"lastName\",label:\"Last Name\",name:\"lastName\",autoComplete:\"family-name\",value:formData.lastName,onChange:handleChange,disabled:loading})}),/*#__PURE__*/_jsx(Grid,{xs:12,children:/*#__PURE__*/_jsx(TextField,{required:true,fullWidth:true,id:\"email\",label:\"Email Address\",name:\"email\",autoComplete:\"email\",value:formData.email,onChange:handleChange,disabled:loading})}),/*#__PURE__*/_jsx(Grid,{xs:12,children:/*#__PURE__*/_jsx(TextField,{required:true,fullWidth:true,name:\"password\",label:\"Password\",type:\"password\",id:\"password\",autoComplete:\"new-password\",value:formData.password,onChange:handleChange,disabled:loading})}),/*#__PURE__*/_jsx(Grid,{xs:12,children:/*#__PURE__*/_jsx(TextField,{required:true,fullWidth:true,name:\"confirmPassword\",label:\"Confirm Password\",type:\"password\",id:\"confirmPassword\",value:formData.confirmPassword,onChange:handleChange,disabled:loading})})]}),/*#__PURE__*/_jsx(Button,{type:\"submit\",fullWidth:true,variant:\"contained\",sx:{mt:3,mb:2},disabled:loading,children:loading?/*#__PURE__*/_jsx(CircularProgress,{size:24}):'Sign Up'}),/*#__PURE__*/_jsx(Box,{sx:{textAlign:'center'},children:/*#__PURE__*/_jsx(MuiLink,{component:Link,to:\"/login\",variant:\"body2\",children:\"Already have an account? Sign In\"})})]})]})})});};export default Register;", "map": {"version": 3, "names": ["React", "useState", "Container", "Paper", "TextField", "<PERSON><PERSON>", "Typography", "Box", "Link", "MuiLink", "<PERSON><PERSON>", "CircularProgress", "Grid", "useNavigate", "useAuth", "jsx", "_jsx", "jsxs", "_jsxs", "Register", "formData", "setFormData", "firstName", "lastName", "email", "password", "confirmPassword", "loading", "setLoading", "error", "setError", "navigate", "register", "handleChange", "e", "_objectSpread", "target", "name", "value", "handleSubmit", "preventDefault", "length", "message", "component", "max<PERSON><PERSON><PERSON>", "children", "sx", "marginTop", "display", "flexDirection", "alignItems", "elevation", "padding", "width", "variant", "gutterBottom", "color", "mb", "severity", "onSubmit", "mt", "container", "spacing", "xs", "sm", "autoComplete", "required", "fullWidth", "id", "label", "autoFocus", "onChange", "disabled", "type", "size", "textAlign", "to"], "sources": ["C:/Users/<USER>/OneDrive/ドキュメント/Projects/StudyHub/studyhub-app/src/pages/Register.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport {\n  Container,\n  Paper,\n  TextField,\n  Button,\n  Typography,\n  Box,\n  Link as MuiLink,\n  Alert,\n  CircularProgress,\n  Grid,\n} from '@mui/material';\nimport { Link, useNavigate } from 'react-router-dom';\nimport { useAuth } from '../contexts/AuthContext';\n\nconst Register: React.FC = () => {\n  const [formData, setFormData] = useState({\n    firstName: '',\n    lastName: '',\n    email: '',\n    password: '',\n    confirmPassword: '',\n  });\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n  const navigate = useNavigate();\n  const { register } = useAuth();\n\n  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    setFormData({\n      ...formData,\n      [e.target.name]: e.target.value,\n    });\n  };\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    setLoading(true);\n    setError('');\n\n    // Validation\n    if (formData.password !== formData.confirmPassword) {\n      setError('Passwords do not match');\n      setLoading(false);\n      return;\n    }\n\n    if (formData.password.length < 6) {\n      setError('Password must be at least 6 characters long');\n      setLoading(false);\n      return;\n    }\n\n    try {\n      await register(formData.email, formData.password, formData.firstName, formData.lastName);\n      navigate('/dashboard');\n    } catch (error: any) {\n      setError(error.message || 'An error occurred during registration');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  return (\n    <Container component=\"main\" maxWidth=\"sm\">\n      <Box\n        sx={{\n          marginTop: 8,\n          display: 'flex',\n          flexDirection: 'column',\n          alignItems: 'center',\n        }}\n      >\n        <Paper\n          elevation={3}\n          sx={{\n            padding: 4,\n            display: 'flex',\n            flexDirection: 'column',\n            alignItems: 'center',\n            width: '100%',\n          }}\n        >\n          <Typography component=\"h1\" variant=\"h4\" gutterBottom>\n            Sign Up\n          </Typography>\n          <Typography variant=\"body2\" color=\"text.secondary\" sx={{ mb: 3 }}>\n            Create your StudyHub account\n          </Typography>\n\n          {error && (\n            <Alert severity=\"error\" sx={{ width: '100%', mb: 2 }}>\n              {error}\n            </Alert>\n          )}\n\n          <Box component=\"form\" onSubmit={handleSubmit} sx={{ mt: 1, width: '100%' }}>\n            <Grid container spacing={2}>\n              <Grid xs={12} sm={6}>\n                <TextField\n                  autoComplete=\"given-name\"\n                  name=\"firstName\"\n                  required\n                  fullWidth\n                  id=\"firstName\"\n                  label=\"First Name\"\n                  autoFocus\n                  value={formData.firstName}\n                  onChange={handleChange}\n                  disabled={loading}\n                />\n              </Grid>\n              <Grid xs={12} sm={6}>\n                <TextField\n                  required\n                  fullWidth\n                  id=\"lastName\"\n                  label=\"Last Name\"\n                  name=\"lastName\"\n                  autoComplete=\"family-name\"\n                  value={formData.lastName}\n                  onChange={handleChange}\n                  disabled={loading}\n                />\n              </Grid>\n              <Grid xs={12}>\n                <TextField\n                  required\n                  fullWidth\n                  id=\"email\"\n                  label=\"Email Address\"\n                  name=\"email\"\n                  autoComplete=\"email\"\n                  value={formData.email}\n                  onChange={handleChange}\n                  disabled={loading}\n                />\n              </Grid>\n              <Grid xs={12}>\n                <TextField\n                  required\n                  fullWidth\n                  name=\"password\"\n                  label=\"Password\"\n                  type=\"password\"\n                  id=\"password\"\n                  autoComplete=\"new-password\"\n                  value={formData.password}\n                  onChange={handleChange}\n                  disabled={loading}\n                />\n              </Grid>\n              <Grid xs={12}>\n                <TextField\n                  required\n                  fullWidth\n                  name=\"confirmPassword\"\n                  label=\"Confirm Password\"\n                  type=\"password\"\n                  id=\"confirmPassword\"\n                  value={formData.confirmPassword}\n                  onChange={handleChange}\n                  disabled={loading}\n                />\n              </Grid>\n            </Grid>\n            <Button\n              type=\"submit\"\n              fullWidth\n              variant=\"contained\"\n              sx={{ mt: 3, mb: 2 }}\n              disabled={loading}\n            >\n              {loading ? <CircularProgress size={24} /> : 'Sign Up'}\n            </Button>\n            <Box sx={{ textAlign: 'center' }}>\n              <MuiLink component={Link} to=\"/login\" variant=\"body2\">\n                Already have an account? Sign In\n              </MuiLink>\n            </Box>\n          </Box>\n        </Paper>\n      </Box>\n    </Container>\n  );\n};\n\nexport default Register;\n"], "mappings": "gLAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,KAAQ,OAAO,CACvC,OACEC,SAAS,CACTC,KAAK,CACLC,SAAS,CACTC,MAAM,CACNC,UAAU,CACVC,GAAG,CACHC,IAAI,GAAI,CAAAC,OAAO,CACfC,KAAK,CACLC,gBAAgB,CAChBC,IAAI,KACC,eAAe,CACtB,OAASJ,IAAI,CAAEK,WAAW,KAAQ,kBAAkB,CACpD,OAASC,OAAO,KAAQ,yBAAyB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAElD,KAAM,CAAAC,QAAkB,CAAGA,CAAA,GAAM,CAC/B,KAAM,CAACC,QAAQ,CAAEC,WAAW,CAAC,CAAGpB,QAAQ,CAAC,CACvCqB,SAAS,CAAE,EAAE,CACbC,QAAQ,CAAE,EAAE,CACZC,KAAK,CAAE,EAAE,CACTC,QAAQ,CAAE,EAAE,CACZC,eAAe,CAAE,EACnB,CAAC,CAAC,CACF,KAAM,CAACC,OAAO,CAAEC,UAAU,CAAC,CAAG3B,QAAQ,CAAC,KAAK,CAAC,CAC7C,KAAM,CAAC4B,KAAK,CAAEC,QAAQ,CAAC,CAAG7B,QAAQ,CAAC,EAAE,CAAC,CACtC,KAAM,CAAA8B,QAAQ,CAAGlB,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAEmB,QAAS,CAAC,CAAGlB,OAAO,CAAC,CAAC,CAE9B,KAAM,CAAAmB,YAAY,CAAIC,CAAsC,EAAK,CAC/Db,WAAW,CAAAc,aAAA,CAAAA,aAAA,IACNf,QAAQ,MACX,CAACc,CAAC,CAACE,MAAM,CAACC,IAAI,EAAGH,CAAC,CAACE,MAAM,CAACE,KAAK,EAChC,CAAC,CACJ,CAAC,CAED,KAAM,CAAAC,YAAY,CAAG,KAAO,CAAAL,CAAkB,EAAK,CACjDA,CAAC,CAACM,cAAc,CAAC,CAAC,CAClBZ,UAAU,CAAC,IAAI,CAAC,CAChBE,QAAQ,CAAC,EAAE,CAAC,CAEZ;AACA,GAAIV,QAAQ,CAACK,QAAQ,GAAKL,QAAQ,CAACM,eAAe,CAAE,CAClDI,QAAQ,CAAC,wBAAwB,CAAC,CAClCF,UAAU,CAAC,KAAK,CAAC,CACjB,OACF,CAEA,GAAIR,QAAQ,CAACK,QAAQ,CAACgB,MAAM,CAAG,CAAC,CAAE,CAChCX,QAAQ,CAAC,6CAA6C,CAAC,CACvDF,UAAU,CAAC,KAAK,CAAC,CACjB,OACF,CAEA,GAAI,CACF,KAAM,CAAAI,QAAQ,CAACZ,QAAQ,CAACI,KAAK,CAAEJ,QAAQ,CAACK,QAAQ,CAAEL,QAAQ,CAACE,SAAS,CAAEF,QAAQ,CAACG,QAAQ,CAAC,CACxFQ,QAAQ,CAAC,YAAY,CAAC,CACxB,CAAE,MAAOF,KAAU,CAAE,CACnBC,QAAQ,CAACD,KAAK,CAACa,OAAO,EAAI,uCAAuC,CAAC,CACpE,CAAC,OAAS,CACRd,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED,mBACEZ,IAAA,CAACd,SAAS,EAACyC,SAAS,CAAC,MAAM,CAACC,QAAQ,CAAC,IAAI,CAAAC,QAAA,cACvC7B,IAAA,CAACT,GAAG,EACFuC,EAAE,CAAE,CACFC,SAAS,CAAE,CAAC,CACZC,OAAO,CAAE,MAAM,CACfC,aAAa,CAAE,QAAQ,CACvBC,UAAU,CAAE,QACd,CAAE,CAAAL,QAAA,cAEF3B,KAAA,CAACf,KAAK,EACJgD,SAAS,CAAE,CAAE,CACbL,EAAE,CAAE,CACFM,OAAO,CAAE,CAAC,CACVJ,OAAO,CAAE,MAAM,CACfC,aAAa,CAAE,QAAQ,CACvBC,UAAU,CAAE,QAAQ,CACpBG,KAAK,CAAE,MACT,CAAE,CAAAR,QAAA,eAEF7B,IAAA,CAACV,UAAU,EAACqC,SAAS,CAAC,IAAI,CAACW,OAAO,CAAC,IAAI,CAACC,YAAY,MAAAV,QAAA,CAAC,SAErD,CAAY,CAAC,cACb7B,IAAA,CAACV,UAAU,EAACgD,OAAO,CAAC,OAAO,CAACE,KAAK,CAAC,gBAAgB,CAACV,EAAE,CAAE,CAAEW,EAAE,CAAE,CAAE,CAAE,CAAAZ,QAAA,CAAC,8BAElE,CAAY,CAAC,CAEZhB,KAAK,eACJb,IAAA,CAACN,KAAK,EAACgD,QAAQ,CAAC,OAAO,CAACZ,EAAE,CAAE,CAAEO,KAAK,CAAE,MAAM,CAAEI,EAAE,CAAE,CAAE,CAAE,CAAAZ,QAAA,CAClDhB,KAAK,CACD,CACR,cAEDX,KAAA,CAACX,GAAG,EAACoC,SAAS,CAAC,MAAM,CAACgB,QAAQ,CAAEpB,YAAa,CAACO,EAAE,CAAE,CAAEc,EAAE,CAAE,CAAC,CAAEP,KAAK,CAAE,MAAO,CAAE,CAAAR,QAAA,eACzE3B,KAAA,CAACN,IAAI,EAACiD,SAAS,MAACC,OAAO,CAAE,CAAE,CAAAjB,QAAA,eACzB7B,IAAA,CAACJ,IAAI,EAACmD,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAAAnB,QAAA,cAClB7B,IAAA,CAACZ,SAAS,EACR6D,YAAY,CAAC,YAAY,CACzB5B,IAAI,CAAC,WAAW,CAChB6B,QAAQ,MACRC,SAAS,MACTC,EAAE,CAAC,WAAW,CACdC,KAAK,CAAC,YAAY,CAClBC,SAAS,MACThC,KAAK,CAAElB,QAAQ,CAACE,SAAU,CAC1BiD,QAAQ,CAAEtC,YAAa,CACvBuC,QAAQ,CAAE7C,OAAQ,CACnB,CAAC,CACE,CAAC,cACPX,IAAA,CAACJ,IAAI,EAACmD,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAAAnB,QAAA,cAClB7B,IAAA,CAACZ,SAAS,EACR8D,QAAQ,MACRC,SAAS,MACTC,EAAE,CAAC,UAAU,CACbC,KAAK,CAAC,WAAW,CACjBhC,IAAI,CAAC,UAAU,CACf4B,YAAY,CAAC,aAAa,CAC1B3B,KAAK,CAAElB,QAAQ,CAACG,QAAS,CACzBgD,QAAQ,CAAEtC,YAAa,CACvBuC,QAAQ,CAAE7C,OAAQ,CACnB,CAAC,CACE,CAAC,cACPX,IAAA,CAACJ,IAAI,EAACmD,EAAE,CAAE,EAAG,CAAAlB,QAAA,cACX7B,IAAA,CAACZ,SAAS,EACR8D,QAAQ,MACRC,SAAS,MACTC,EAAE,CAAC,OAAO,CACVC,KAAK,CAAC,eAAe,CACrBhC,IAAI,CAAC,OAAO,CACZ4B,YAAY,CAAC,OAAO,CACpB3B,KAAK,CAAElB,QAAQ,CAACI,KAAM,CACtB+C,QAAQ,CAAEtC,YAAa,CACvBuC,QAAQ,CAAE7C,OAAQ,CACnB,CAAC,CACE,CAAC,cACPX,IAAA,CAACJ,IAAI,EAACmD,EAAE,CAAE,EAAG,CAAAlB,QAAA,cACX7B,IAAA,CAACZ,SAAS,EACR8D,QAAQ,MACRC,SAAS,MACT9B,IAAI,CAAC,UAAU,CACfgC,KAAK,CAAC,UAAU,CAChBI,IAAI,CAAC,UAAU,CACfL,EAAE,CAAC,UAAU,CACbH,YAAY,CAAC,cAAc,CAC3B3B,KAAK,CAAElB,QAAQ,CAACK,QAAS,CACzB8C,QAAQ,CAAEtC,YAAa,CACvBuC,QAAQ,CAAE7C,OAAQ,CACnB,CAAC,CACE,CAAC,cACPX,IAAA,CAACJ,IAAI,EAACmD,EAAE,CAAE,EAAG,CAAAlB,QAAA,cACX7B,IAAA,CAACZ,SAAS,EACR8D,QAAQ,MACRC,SAAS,MACT9B,IAAI,CAAC,iBAAiB,CACtBgC,KAAK,CAAC,kBAAkB,CACxBI,IAAI,CAAC,UAAU,CACfL,EAAE,CAAC,iBAAiB,CACpB9B,KAAK,CAAElB,QAAQ,CAACM,eAAgB,CAChC6C,QAAQ,CAAEtC,YAAa,CACvBuC,QAAQ,CAAE7C,OAAQ,CACnB,CAAC,CACE,CAAC,EACH,CAAC,cACPX,IAAA,CAACX,MAAM,EACLoE,IAAI,CAAC,QAAQ,CACbN,SAAS,MACTb,OAAO,CAAC,WAAW,CACnBR,EAAE,CAAE,CAAEc,EAAE,CAAE,CAAC,CAAEH,EAAE,CAAE,CAAE,CAAE,CACrBe,QAAQ,CAAE7C,OAAQ,CAAAkB,QAAA,CAEjBlB,OAAO,cAAGX,IAAA,CAACL,gBAAgB,EAAC+D,IAAI,CAAE,EAAG,CAAE,CAAC,CAAG,SAAS,CAC/C,CAAC,cACT1D,IAAA,CAACT,GAAG,EAACuC,EAAE,CAAE,CAAE6B,SAAS,CAAE,QAAS,CAAE,CAAA9B,QAAA,cAC/B7B,IAAA,CAACP,OAAO,EAACkC,SAAS,CAAEnC,IAAK,CAACoE,EAAE,CAAC,QAAQ,CAACtB,OAAO,CAAC,OAAO,CAAAT,QAAA,CAAC,kCAEtD,CAAS,CAAC,CACP,CAAC,EACH,CAAC,EACD,CAAC,CACL,CAAC,CACG,CAAC,CAEhB,CAAC,CAED,cAAe,CAAA1B,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}