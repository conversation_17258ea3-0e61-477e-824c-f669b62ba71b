{"ast": null, "code": "export default function prepareTypographyVars(typography) {\n  const vars = {};\n  const entries = Object.entries(typography);\n  entries.forEach(entry => {\n    const [key, value] = entry;\n    if (typeof value === 'object') {\n      vars[key] = \"\".concat(value.fontStyle ? \"\".concat(value.fontStyle, \" \") : '').concat(value.fontVariant ? \"\".concat(value.fontVariant, \" \") : '').concat(value.fontWeight ? \"\".concat(value.fontWeight, \" \") : '').concat(value.fontStretch ? \"\".concat(value.fontStretch, \" \") : '').concat(value.fontSize || '').concat(value.lineHeight ? \"/\".concat(value.lineHeight, \" \") : '').concat(value.fontFamily || '');\n    }\n  });\n  return vars;\n}", "map": {"version": 3, "names": ["prepareTypographyVars", "typography", "vars", "entries", "Object", "for<PERSON>ach", "entry", "key", "value", "concat", "fontStyle", "fontVariant", "fontWeight", "fontStretch", "fontSize", "lineHeight", "fontFamily"], "sources": ["C:/Users/<USER>/OneDrive/ドキュメント/Projects/StudyHub/studyhub-app/node_modules/@mui/system/esm/cssVars/prepareTypographyVars.js"], "sourcesContent": ["export default function prepareTypographyVars(typography) {\n  const vars = {};\n  const entries = Object.entries(typography);\n  entries.forEach(entry => {\n    const [key, value] = entry;\n    if (typeof value === 'object') {\n      vars[key] = `${value.fontStyle ? `${value.fontStyle} ` : ''}${value.fontVariant ? `${value.fontVariant} ` : ''}${value.fontWeight ? `${value.fontWeight} ` : ''}${value.fontStretch ? `${value.fontStretch} ` : ''}${value.fontSize || ''}${value.lineHeight ? `/${value.lineHeight} ` : ''}${value.fontFamily || ''}`;\n    }\n  });\n  return vars;\n}"], "mappings": "AAAA,eAAe,SAASA,qBAAqBA,CAACC,UAAU,EAAE;EACxD,MAAMC,IAAI,GAAG,CAAC,CAAC;EACf,MAAMC,OAAO,GAAGC,MAAM,CAACD,OAAO,CAACF,UAAU,CAAC;EAC1CE,OAAO,CAACE,OAAO,CAACC,KAAK,IAAI;IACvB,MAAM,CAACC,GAAG,EAAEC,KAAK,CAAC,GAAGF,KAAK;IAC1B,IAAI,OAAOE,KAAK,KAAK,QAAQ,EAAE;MAC7BN,IAAI,CAACK,GAAG,CAAC,MAAAE,MAAA,CAAMD,KAAK,CAACE,SAAS,MAAAD,MAAA,CAAMD,KAAK,CAACE,SAAS,SAAM,EAAE,EAAAD,MAAA,CAAGD,KAAK,CAACG,WAAW,MAAAF,MAAA,CAAMD,KAAK,CAACG,WAAW,SAAM,EAAE,EAAAF,MAAA,CAAGD,KAAK,CAACI,UAAU,MAAAH,MAAA,CAAMD,KAAK,CAACI,UAAU,SAAM,EAAE,EAAAH,MAAA,CAAGD,KAAK,CAACK,WAAW,MAAAJ,MAAA,CAAMD,KAAK,CAACK,WAAW,SAAM,EAAE,EAAAJ,MAAA,CAAGD,KAAK,CAACM,QAAQ,IAAI,EAAE,EAAAL,MAAA,CAAGD,KAAK,CAACO,UAAU,OAAAN,MAAA,CAAOD,KAAK,CAACO,UAAU,SAAM,EAAE,EAAAN,MAAA,CAAGD,KAAK,CAACQ,UAAU,IAAI,EAAE,CAAE;IACxT;EACF,CAAC,CAAC;EACF,OAAOd,IAAI;AACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}