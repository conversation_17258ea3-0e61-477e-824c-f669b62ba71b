{"ast": null, "code": "'use strict';\n\nvar isCallable = require('../internals/is-callable');\nvar tryToString = require('../internals/try-to-string');\nvar $TypeError = TypeError;\n\n// `Assert: IsCallable(argument) is true`\nmodule.exports = function (argument) {\n  if (isCallable(argument)) return argument;\n  throw new $TypeError(tryToString(argument) + ' is not a function');\n};", "map": {"version": 3, "names": ["isCallable", "require", "tryToString", "$TypeError", "TypeError", "module", "exports", "argument"], "sources": ["C:/Users/<USER>/OneDrive/ドキュメント/Projects/StudyHub/studyhub-app/node_modules/core-js-pure/internals/a-callable.js"], "sourcesContent": ["'use strict';\nvar isCallable = require('../internals/is-callable');\nvar tryToString = require('../internals/try-to-string');\n\nvar $TypeError = TypeError;\n\n// `Assert: IsCallable(argument) is true`\nmodule.exports = function (argument) {\n  if (isCallable(argument)) return argument;\n  throw new $TypeError(tryToString(argument) + ' is not a function');\n};\n"], "mappings": "AAAA,YAAY;;AACZ,IAAIA,UAAU,GAAGC,OAAO,CAAC,0BAA0B,CAAC;AACpD,IAAIC,WAAW,GAAGD,OAAO,CAAC,4BAA4B,CAAC;AAEvD,IAAIE,UAAU,GAAGC,SAAS;;AAE1B;AACAC,MAAM,CAACC,OAAO,GAAG,UAAUC,QAAQ,EAAE;EACnC,IAAIP,UAAU,CAACO,QAAQ,CAAC,EAAE,OAAOA,QAAQ;EACzC,MAAM,IAAIJ,UAAU,CAACD,WAAW,CAACK,QAAQ,CAAC,GAAG,oBAAoB,CAAC;AACpE,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}