{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\\\u30C9\\u30AD\\u30E5\\u30E1\\u30F3\\u30C8\\\\Projects\\\\StudyHub\\\\studyhub-app\\\\src\\\\pages\\\\Feed.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Container, Grid, Card, CardContent, CardActions, Typography, Button, TextField, Box, Avatar, IconButton, Fab, Dialog, DialogTitle, DialogContent, DialogActions, Chip, Paper, Divider } from '@mui/material';\nimport { Add, Favorite, FavoriteBorder, Comment, Share, MoreVert, Send } from '@mui/icons-material';\nimport { useAuth } from '../contexts/AuthContext';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\n// Post interface is now imported from PostService\n\nconst Feed = () => {\n  _s();\n  var _userProfile$firstNam, _userProfile$lastName;\n  const {\n    currentUser,\n    userProfile\n  } = useAuth();\n  const [posts, setPosts] = useState([]);\n  const [newPostContent, setNewPostContent] = useState('');\n  const [createPostOpen, setCreatePostOpen] = useState(false);\n  const [selectedTags, setSelectedTags] = useState([]);\n\n  // Sample data for demonstration\n  useEffect(() => {\n    const samplePosts = [{\n      id: '1',\n      authorId: 'user1',\n      authorName: 'Alice Johnson',\n      authorAvatar: '',\n      content: 'Just had an amazing discussion about climate change solutions! What are your thoughts on renewable energy adoption?',\n      timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000),\n      // 2 hours ago\n      likes: 24,\n      comments: 8,\n      isLiked: false,\n      tags: ['climate', 'environment', 'discussion']\n    }, {\n      id: '2',\n      authorId: 'user2',\n      authorName: 'Bob Smith',\n      authorAvatar: '',\n      content: 'The future of AI is fascinating! I believe we\\'re on the verge of breakthrough innovations that will change everything.',\n      timestamp: new Date(Date.now() - 4 * 60 * 60 * 1000),\n      // 4 hours ago\n      likes: 42,\n      comments: 15,\n      isLiked: true,\n      tags: ['AI', 'technology', 'future']\n    }, {\n      id: '3',\n      authorId: 'user3',\n      authorName: 'Carol Davis',\n      authorAvatar: '',\n      content: 'Sharing my thoughts on work-life balance. It\\'s crucial to set boundaries and prioritize mental health.',\n      timestamp: new Date(Date.now() - 6 * 60 * 60 * 1000),\n      // 6 hours ago\n      likes: 18,\n      comments: 6,\n      isLiked: false,\n      tags: ['wellness', 'work', 'mentalhealth']\n    }];\n    setPosts(samplePosts);\n  }, []);\n  const handleCreatePost = () => {\n    if (!newPostContent.trim()) return;\n    const newPost = {\n      id: Date.now().toString(),\n      authorId: (currentUser === null || currentUser === void 0 ? void 0 : currentUser.uid) || '',\n      authorName: (userProfile === null || userProfile === void 0 ? void 0 : userProfile.firstName) + ' ' + (userProfile === null || userProfile === void 0 ? void 0 : userProfile.lastName) || 'Anonymous',\n      authorAvatar: (userProfile === null || userProfile === void 0 ? void 0 : userProfile.profilePicture) || '',\n      content: newPostContent,\n      timestamp: new Date(),\n      likes: 0,\n      comments: 0,\n      isLiked: false,\n      tags: selectedTags\n    };\n    setPosts([newPost, ...posts]);\n    setNewPostContent('');\n    setSelectedTags([]);\n    setCreatePostOpen(false);\n  };\n  const handleLike = postId => {\n    setPosts(posts.map(post => post.id === postId ? {\n      ...post,\n      isLiked: !post.isLiked,\n      likes: post.isLiked ? post.likes - 1 : post.likes + 1\n    } : post));\n  };\n  const formatTimeAgo = timestamp => {\n    const now = new Date();\n    const diffInHours = Math.floor((now.getTime() - timestamp.getTime()) / (1000 * 60 * 60));\n    if (diffInHours < 1) return 'Just now';\n    if (diffInHours < 24) return `${diffInHours}h ago`;\n    return `${Math.floor(diffInHours / 24)}d ago`;\n  };\n  const availableTags = ['technology', 'AI', 'climate', 'environment', 'wellness', 'work', 'mentalhealth', 'discussion', 'future', 'innovation'];\n  return /*#__PURE__*/_jsxDEV(Container, {\n    maxWidth: \"md\",\n    children: /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        py: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h4\",\n        gutterBottom: true,\n        children: \"Your Feed\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 137,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"body1\",\n        color: \"text.secondary\",\n        sx: {\n          mb: 4\n        },\n        children: \"Share your thoughts and discover what others are talking about\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 140,\n        columnNumber: 9\n      }, this), currentUser && /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: 3,\n          mb: 3\n        },\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center',\n            gap: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(Avatar, {\n            src: userProfile === null || userProfile === void 0 ? void 0 : userProfile.profilePicture,\n            children: [userProfile === null || userProfile === void 0 ? void 0 : (_userProfile$firstNam = userProfile.firstName) === null || _userProfile$firstNam === void 0 ? void 0 : _userProfile$firstNam[0], userProfile === null || userProfile === void 0 ? void 0 : (_userProfile$lastName = userProfile.lastName) === null || _userProfile$lastName === void 0 ? void 0 : _userProfile$lastName[0]]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 148,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            fullWidth: true,\n            placeholder: \"What's on your mind?\",\n            variant: \"outlined\",\n            onClick: () => setCreatePostOpen(true),\n            sx: {\n              cursor: 'pointer'\n            },\n            InputProps: {\n              readOnly: true\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 151,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 147,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 146,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: 3,\n        children: posts.map(post => /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            children: [/*#__PURE__*/_jsxDEV(CardContent, {\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  alignItems: 'center',\n                  mb: 2\n                },\n                children: [/*#__PURE__*/_jsxDEV(Avatar, {\n                  src: post.authorAvatar,\n                  sx: {\n                    mr: 2\n                  },\n                  children: post.authorName.split(' ').map(n => n[0]).join('')\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 172,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    flexGrow: 1\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"subtitle1\",\n                    fontWeight: \"bold\",\n                    children: post.authorName\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 176,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"caption\",\n                    color: \"text.secondary\",\n                    children: formatTimeAgo(post.timestamp)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 179,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 175,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n                  children: /*#__PURE__*/_jsxDEV(MoreVert, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 184,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 183,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 171,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body1\",\n                sx: {\n                  mb: 2\n                },\n                children: post.content\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 188,\n                columnNumber: 19\n              }, this), post.tags.length > 0 && /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  mb: 2\n                },\n                children: post.tags.map(tag => /*#__PURE__*/_jsxDEV(Chip, {\n                  label: `#${tag}`,\n                  size: \"small\",\n                  sx: {\n                    mr: 1,\n                    mb: 1\n                  },\n                  color: \"primary\",\n                  variant: \"outlined\"\n                }, tag, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 195,\n                  columnNumber: 25\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 193,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 170,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 208,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(CardActions, {\n              sx: {\n                justifyContent: 'space-between',\n                px: 2\n              },\n              children: /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  gap: 1\n                },\n                children: [/*#__PURE__*/_jsxDEV(Button, {\n                  startIcon: post.isLiked ? /*#__PURE__*/_jsxDEV(Favorite, {\n                    color: \"error\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 213,\n                    columnNumber: 49\n                  }, this) : /*#__PURE__*/_jsxDEV(FavoriteBorder, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 213,\n                    columnNumber: 78\n                  }, this),\n                  onClick: () => handleLike(post.id),\n                  color: post.isLiked ? \"error\" : \"inherit\",\n                  children: post.likes\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 212,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Button, {\n                  startIcon: /*#__PURE__*/_jsxDEV(Comment, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 219,\n                    columnNumber: 40\n                  }, this),\n                  children: post.comments\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 219,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Button, {\n                  startIcon: /*#__PURE__*/_jsxDEV(Share, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 222,\n                    columnNumber: 40\n                  }, this),\n                  children: \"Share\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 222,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 211,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 210,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 169,\n            columnNumber: 15\n          }, this)\n        }, post.id, false, {\n          fileName: _jsxFileName,\n          lineNumber: 168,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 166,\n        columnNumber: 9\n      }, this), currentUser && /*#__PURE__*/_jsxDEV(Fab, {\n        color: \"primary\",\n        \"aria-label\": \"create post\",\n        sx: {\n          position: 'fixed',\n          bottom: 16,\n          right: 16\n        },\n        onClick: () => setCreatePostOpen(true),\n        children: /*#__PURE__*/_jsxDEV(Add, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 240,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 234,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n        open: createPostOpen,\n        onClose: () => setCreatePostOpen(false),\n        maxWidth: \"sm\",\n        fullWidth: true,\n        children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n          children: \"Create New Post\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 246,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n          children: [/*#__PURE__*/_jsxDEV(TextField, {\n            autoFocus: true,\n            margin: \"dense\",\n            label: \"What's on your mind?\",\n            fullWidth: true,\n            multiline: true,\n            rows: 4,\n            variant: \"outlined\",\n            value: newPostContent,\n            onChange: e => setNewPostContent(e.target.value),\n            sx: {\n              mb: 2\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 248,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle2\",\n            gutterBottom: true,\n            children: \"Add Tags:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 261,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              flexWrap: 'wrap',\n              gap: 1,\n              mb: 2\n            },\n            children: availableTags.map(tag => /*#__PURE__*/_jsxDEV(Chip, {\n              label: `#${tag}`,\n              clickable: true,\n              color: selectedTags.includes(tag) ? \"primary\" : \"default\",\n              onClick: () => {\n                setSelectedTags(prev => prev.includes(tag) ? prev.filter(t => t !== tag) : [...prev, tag]);\n              }\n            }, tag, false, {\n              fileName: _jsxFileName,\n              lineNumber: 266,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 264,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 247,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            onClick: () => setCreatePostOpen(false),\n            children: \"Cancel\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 283,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleCreatePost,\n            variant: \"contained\",\n            disabled: !newPostContent.trim(),\n            startIcon: /*#__PURE__*/_jsxDEV(Send, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 288,\n              columnNumber: 26\n            }, this),\n            children: \"Post\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 284,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 282,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 245,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 136,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 135,\n    columnNumber: 5\n  }, this);\n};\n_s(Feed, \"YByyapOswd2SElwbA6LNLCG1Tzw=\", false, function () {\n  return [useAuth];\n});\n_c = Feed;\nexport default Feed;\nvar _c;\n$RefreshReg$(_c, \"Feed\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Container", "Grid", "Card", "<PERSON><PERSON><PERSON><PERSON>", "CardActions", "Typography", "<PERSON><PERSON>", "TextField", "Box", "Avatar", "IconButton", "Fab", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "Chip", "Paper", "Divider", "Add", "Favorite", "FavoriteBorder", "Comment", "Share", "<PERSON><PERSON><PERSON>", "Send", "useAuth", "jsxDEV", "_jsxDEV", "Feed", "_s", "_userProfile$firstNam", "_userProfile$lastName", "currentUser", "userProfile", "posts", "setPosts", "newPostContent", "set<PERSON>ew<PERSON>ost<PERSON><PERSON>nt", "createPostOpen", "setCreatePostOpen", "selectedTags", "setSelectedTags", "samplePosts", "id", "authorId", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "content", "timestamp", "Date", "now", "likes", "comments", "isLiked", "tags", "handleCreatePost", "trim", "newPost", "toString", "uid", "firstName", "lastName", "profilePicture", "handleLike", "postId", "map", "post", "formatTimeAgo", "diffInHours", "Math", "floor", "getTime", "availableTags", "max<PERSON><PERSON><PERSON>", "children", "sx", "py", "variant", "gutterBottom", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "color", "mb", "p", "display", "alignItems", "gap", "src", "fullWidth", "placeholder", "onClick", "cursor", "InputProps", "readOnly", "container", "spacing", "item", "xs", "mr", "split", "n", "join", "flexGrow", "fontWeight", "length", "tag", "label", "size", "justifyContent", "px", "startIcon", "position", "bottom", "right", "open", "onClose", "autoFocus", "margin", "multiline", "rows", "value", "onChange", "e", "target", "flexWrap", "clickable", "includes", "prev", "filter", "t", "disabled", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/ドキュメント/Projects/StudyHub/studyhub-app/src/pages/Feed.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  <PERSON><PERSON><PERSON>,\n  <PERSON>rid,\n  <PERSON>,\n  CardContent,\n  CardA<PERSON>,\n  Typo<PERSON>,\n  Button,\n  TextField,\n  Box,\n  Avatar,\n  IconButton,\n  Fab,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  Chip,\n  Paper,\n  Divider,\n  CircularProgress,\n  Alert,\n} from '@mui/material';\nimport {\n  Add,\n  Favorite,\n  FavoriteBorder,\n  Comment,\n  Share,\n  MoreVert,\n  Send,\n} from '@mui/icons-material';\nimport { useAuth } from '../contexts/AuthContext';\nimport { PostService, Post } from '../services/postService';\n\n// Post interface is now imported from PostService\n\nconst Feed: React.FC = () => {\n  const { currentUser, userProfile } = useAuth();\n  const [posts, setPosts] = useState<Post[]>([]);\n  const [newPostContent, setNewPostContent] = useState('');\n  const [createPostOpen, setCreatePostOpen] = useState(false);\n  const [selectedTags, setSelectedTags] = useState<string[]>([]);\n\n  // Sample data for demonstration\n  useEffect(() => {\n    const samplePosts: Post[] = [\n      {\n        id: '1',\n        authorId: 'user1',\n        authorName: '<PERSON>',\n        authorAvatar: '',\n        content: 'Just had an amazing discussion about climate change solutions! What are your thoughts on renewable energy adoption?',\n        timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2 hours ago\n        likes: 24,\n        comments: 8,\n        isLiked: false,\n        tags: ['climate', 'environment', 'discussion'],\n      },\n      {\n        id: '2',\n        authorId: 'user2',\n        authorName: 'Bob Smith',\n        authorAvatar: '',\n        content: 'The future of AI is fascinating! I believe we\\'re on the verge of breakthrough innovations that will change everything.',\n        timestamp: new Date(Date.now() - 4 * 60 * 60 * 1000), // 4 hours ago\n        likes: 42,\n        comments: 15,\n        isLiked: true,\n        tags: ['AI', 'technology', 'future'],\n      },\n      {\n        id: '3',\n        authorId: 'user3',\n        authorName: 'Carol Davis',\n        authorAvatar: '',\n        content: 'Sharing my thoughts on work-life balance. It\\'s crucial to set boundaries and prioritize mental health.',\n        timestamp: new Date(Date.now() - 6 * 60 * 60 * 1000), // 6 hours ago\n        likes: 18,\n        comments: 6,\n        isLiked: false,\n        tags: ['wellness', 'work', 'mentalhealth'],\n      },\n    ];\n    setPosts(samplePosts);\n  }, []);\n\n  const handleCreatePost = () => {\n    if (!newPostContent.trim()) return;\n\n    const newPost: Post = {\n      id: Date.now().toString(),\n      authorId: currentUser?.uid || '',\n      authorName: userProfile?.firstName + ' ' + userProfile?.lastName || 'Anonymous',\n      authorAvatar: userProfile?.profilePicture || '',\n      content: newPostContent,\n      timestamp: new Date(),\n      likes: 0,\n      comments: 0,\n      isLiked: false,\n      tags: selectedTags,\n    };\n\n    setPosts([newPost, ...posts]);\n    setNewPostContent('');\n    setSelectedTags([]);\n    setCreatePostOpen(false);\n  };\n\n  const handleLike = (postId: string) => {\n    setPosts(posts.map(post => \n      post.id === postId \n        ? { \n            ...post, \n            isLiked: !post.isLiked, \n            likes: post.isLiked ? post.likes - 1 : post.likes + 1 \n          }\n        : post\n    ));\n  };\n\n  const formatTimeAgo = (timestamp: Date) => {\n    const now = new Date();\n    const diffInHours = Math.floor((now.getTime() - timestamp.getTime()) / (1000 * 60 * 60));\n    \n    if (diffInHours < 1) return 'Just now';\n    if (diffInHours < 24) return `${diffInHours}h ago`;\n    return `${Math.floor(diffInHours / 24)}d ago`;\n  };\n\n  const availableTags = ['technology', 'AI', 'climate', 'environment', 'wellness', 'work', 'mentalhealth', 'discussion', 'future', 'innovation'];\n\n  return (\n    <Container maxWidth=\"md\">\n      <Box sx={{ py: 3 }}>\n        <Typography variant=\"h4\" gutterBottom>\n          Your Feed\n        </Typography>\n        <Typography variant=\"body1\" color=\"text.secondary\" sx={{ mb: 4 }}>\n          Share your thoughts and discover what others are talking about\n        </Typography>\n\n        {/* Create Post Section */}\n        {currentUser && (\n          <Paper sx={{ p: 3, mb: 3 }}>\n            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>\n              <Avatar src={userProfile?.profilePicture}>\n                {userProfile?.firstName?.[0]}{userProfile?.lastName?.[0]}\n              </Avatar>\n              <TextField\n                fullWidth\n                placeholder=\"What's on your mind?\"\n                variant=\"outlined\"\n                onClick={() => setCreatePostOpen(true)}\n                sx={{ cursor: 'pointer' }}\n                InputProps={{\n                  readOnly: true,\n                }}\n              />\n            </Box>\n          </Paper>\n        )}\n\n        {/* Posts */}\n        <Grid container spacing={3}>\n          {posts.map((post) => (\n            <Grid item xs={12} key={post.id}>\n              <Card>\n                <CardContent>\n                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>\n                    <Avatar src={post.authorAvatar} sx={{ mr: 2 }}>\n                      {post.authorName.split(' ').map(n => n[0]).join('')}\n                    </Avatar>\n                    <Box sx={{ flexGrow: 1 }}>\n                      <Typography variant=\"subtitle1\" fontWeight=\"bold\">\n                        {post.authorName}\n                      </Typography>\n                      <Typography variant=\"caption\" color=\"text.secondary\">\n                        {formatTimeAgo(post.timestamp)}\n                      </Typography>\n                    </Box>\n                    <IconButton>\n                      <MoreVert />\n                    </IconButton>\n                  </Box>\n\n                  <Typography variant=\"body1\" sx={{ mb: 2 }}>\n                    {post.content}\n                  </Typography>\n\n                  {post.tags.length > 0 && (\n                    <Box sx={{ mb: 2 }}>\n                      {post.tags.map((tag) => (\n                        <Chip\n                          key={tag}\n                          label={`#${tag}`}\n                          size=\"small\"\n                          sx={{ mr: 1, mb: 1 }}\n                          color=\"primary\"\n                          variant=\"outlined\"\n                        />\n                      ))}\n                    </Box>\n                  )}\n                </CardContent>\n\n                <Divider />\n\n                <CardActions sx={{ justifyContent: 'space-between', px: 2 }}>\n                  <Box sx={{ display: 'flex', gap: 1 }}>\n                    <Button\n                      startIcon={post.isLiked ? <Favorite color=\"error\" /> : <FavoriteBorder />}\n                      onClick={() => handleLike(post.id)}\n                      color={post.isLiked ? \"error\" : \"inherit\"}\n                    >\n                      {post.likes}\n                    </Button>\n                    <Button startIcon={<Comment />}>\n                      {post.comments}\n                    </Button>\n                    <Button startIcon={<Share />}>\n                      Share\n                    </Button>\n                  </Box>\n                </CardActions>\n              </Card>\n            </Grid>\n          ))}\n        </Grid>\n\n        {/* Floating Action Button */}\n        {currentUser && (\n          <Fab\n            color=\"primary\"\n            aria-label=\"create post\"\n            sx={{ position: 'fixed', bottom: 16, right: 16 }}\n            onClick={() => setCreatePostOpen(true)}\n          >\n            <Add />\n          </Fab>\n        )}\n\n        {/* Create Post Dialog */}\n        <Dialog open={createPostOpen} onClose={() => setCreatePostOpen(false)} maxWidth=\"sm\" fullWidth>\n          <DialogTitle>Create New Post</DialogTitle>\n          <DialogContent>\n            <TextField\n              autoFocus\n              margin=\"dense\"\n              label=\"What's on your mind?\"\n              fullWidth\n              multiline\n              rows={4}\n              variant=\"outlined\"\n              value={newPostContent}\n              onChange={(e) => setNewPostContent(e.target.value)}\n              sx={{ mb: 2 }}\n            />\n            \n            <Typography variant=\"subtitle2\" gutterBottom>\n              Add Tags:\n            </Typography>\n            <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mb: 2 }}>\n              {availableTags.map((tag) => (\n                <Chip\n                  key={tag}\n                  label={`#${tag}`}\n                  clickable\n                  color={selectedTags.includes(tag) ? \"primary\" : \"default\"}\n                  onClick={() => {\n                    setSelectedTags(prev => \n                      prev.includes(tag) \n                        ? prev.filter(t => t !== tag)\n                        : [...prev, tag]\n                    );\n                  }}\n                />\n              ))}\n            </Box>\n          </DialogContent>\n          <DialogActions>\n            <Button onClick={() => setCreatePostOpen(false)}>Cancel</Button>\n            <Button \n              onClick={handleCreatePost} \n              variant=\"contained\"\n              disabled={!newPostContent.trim()}\n              startIcon={<Send />}\n            >\n              Post\n            </Button>\n          </DialogActions>\n        </Dialog>\n      </Box>\n    </Container>\n  );\n};\n\nexport default Feed;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,SAAS,EACTC,IAAI,EACJC,IAAI,EACJC,WAAW,EACXC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,SAAS,EACTC,GAAG,EACHC,MAAM,EACNC,UAAU,EACVC,GAAG,EACHC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,IAAI,EACJC,KAAK,EACLC,OAAO,QAGF,eAAe;AACtB,SACEC,GAAG,EACHC,QAAQ,EACRC,cAAc,EACdC,OAAO,EACPC,KAAK,EACLC,QAAQ,EACRC,IAAI,QACC,qBAAqB;AAC5B,SAASC,OAAO,QAAQ,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAGlD;;AAEA,MAAMC,IAAc,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,qBAAA,EAAAC,qBAAA;EAC3B,MAAM;IAAEC,WAAW;IAAEC;EAAY,CAAC,GAAGR,OAAO,CAAC,CAAC;EAC9C,MAAM,CAACS,KAAK,EAAEC,QAAQ,CAAC,GAAGtC,QAAQ,CAAS,EAAE,CAAC;EAC9C,MAAM,CAACuC,cAAc,EAAEC,iBAAiB,CAAC,GAAGxC,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAACyC,cAAc,EAAEC,iBAAiB,CAAC,GAAG1C,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAAC2C,YAAY,EAAEC,eAAe,CAAC,GAAG5C,QAAQ,CAAW,EAAE,CAAC;;EAE9D;EACAC,SAAS,CAAC,MAAM;IACd,MAAM4C,WAAmB,GAAG,CAC1B;MACEC,EAAE,EAAE,GAAG;MACPC,QAAQ,EAAE,OAAO;MACjBC,UAAU,EAAE,eAAe;MAC3BC,YAAY,EAAE,EAAE;MAChBC,OAAO,EAAE,qHAAqH;MAC9HC,SAAS,EAAE,IAAIC,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;MAAE;MACtDC,KAAK,EAAE,EAAE;MACTC,QAAQ,EAAE,CAAC;MACXC,OAAO,EAAE,KAAK;MACdC,IAAI,EAAE,CAAC,SAAS,EAAE,aAAa,EAAE,YAAY;IAC/C,CAAC,EACD;MACEX,EAAE,EAAE,GAAG;MACPC,QAAQ,EAAE,OAAO;MACjBC,UAAU,EAAE,WAAW;MACvBC,YAAY,EAAE,EAAE;MAChBC,OAAO,EAAE,yHAAyH;MAClIC,SAAS,EAAE,IAAIC,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;MAAE;MACtDC,KAAK,EAAE,EAAE;MACTC,QAAQ,EAAE,EAAE;MACZC,OAAO,EAAE,IAAI;MACbC,IAAI,EAAE,CAAC,IAAI,EAAE,YAAY,EAAE,QAAQ;IACrC,CAAC,EACD;MACEX,EAAE,EAAE,GAAG;MACPC,QAAQ,EAAE,OAAO;MACjBC,UAAU,EAAE,aAAa;MACzBC,YAAY,EAAE,EAAE;MAChBC,OAAO,EAAE,yGAAyG;MAClHC,SAAS,EAAE,IAAIC,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;MAAE;MACtDC,KAAK,EAAE,EAAE;MACTC,QAAQ,EAAE,CAAC;MACXC,OAAO,EAAE,KAAK;MACdC,IAAI,EAAE,CAAC,UAAU,EAAE,MAAM,EAAE,cAAc;IAC3C,CAAC,CACF;IACDnB,QAAQ,CAACO,WAAW,CAAC;EACvB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMa,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,IAAI,CAACnB,cAAc,CAACoB,IAAI,CAAC,CAAC,EAAE;IAE5B,MAAMC,OAAa,GAAG;MACpBd,EAAE,EAAEM,IAAI,CAACC,GAAG,CAAC,CAAC,CAACQ,QAAQ,CAAC,CAAC;MACzBd,QAAQ,EAAE,CAAAZ,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAE2B,GAAG,KAAI,EAAE;MAChCd,UAAU,EAAE,CAAAZ,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAE2B,SAAS,IAAG,GAAG,IAAG3B,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAE4B,QAAQ,KAAI,WAAW;MAC/Ef,YAAY,EAAE,CAAAb,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAE6B,cAAc,KAAI,EAAE;MAC/Cf,OAAO,EAAEX,cAAc;MACvBY,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC;MACrBE,KAAK,EAAE,CAAC;MACRC,QAAQ,EAAE,CAAC;MACXC,OAAO,EAAE,KAAK;MACdC,IAAI,EAAEd;IACR,CAAC;IAEDL,QAAQ,CAAC,CAACsB,OAAO,EAAE,GAAGvB,KAAK,CAAC,CAAC;IAC7BG,iBAAiB,CAAC,EAAE,CAAC;IACrBI,eAAe,CAAC,EAAE,CAAC;IACnBF,iBAAiB,CAAC,KAAK,CAAC;EAC1B,CAAC;EAED,MAAMwB,UAAU,GAAIC,MAAc,IAAK;IACrC7B,QAAQ,CAACD,KAAK,CAAC+B,GAAG,CAACC,IAAI,IACrBA,IAAI,CAACvB,EAAE,KAAKqB,MAAM,GACd;MACE,GAAGE,IAAI;MACPb,OAAO,EAAE,CAACa,IAAI,CAACb,OAAO;MACtBF,KAAK,EAAEe,IAAI,CAACb,OAAO,GAAGa,IAAI,CAACf,KAAK,GAAG,CAAC,GAAGe,IAAI,CAACf,KAAK,GAAG;IACtD,CAAC,GACDe,IACN,CAAC,CAAC;EACJ,CAAC;EAED,MAAMC,aAAa,GAAInB,SAAe,IAAK;IACzC,MAAME,GAAG,GAAG,IAAID,IAAI,CAAC,CAAC;IACtB,MAAMmB,WAAW,GAAGC,IAAI,CAACC,KAAK,CAAC,CAACpB,GAAG,CAACqB,OAAO,CAAC,CAAC,GAAGvB,SAAS,CAACuB,OAAO,CAAC,CAAC,KAAK,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;IAExF,IAAIH,WAAW,GAAG,CAAC,EAAE,OAAO,UAAU;IACtC,IAAIA,WAAW,GAAG,EAAE,EAAE,OAAO,GAAGA,WAAW,OAAO;IAClD,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACF,WAAW,GAAG,EAAE,CAAC,OAAO;EAC/C,CAAC;EAED,MAAMI,aAAa,GAAG,CAAC,YAAY,EAAE,IAAI,EAAE,SAAS,EAAE,aAAa,EAAE,UAAU,EAAE,MAAM,EAAE,cAAc,EAAE,YAAY,EAAE,QAAQ,EAAE,YAAY,CAAC;EAE9I,oBACE7C,OAAA,CAAC5B,SAAS;IAAC0E,QAAQ,EAAC,IAAI;IAAAC,QAAA,eACtB/C,OAAA,CAACpB,GAAG;MAACoE,EAAE,EAAE;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAF,QAAA,gBACjB/C,OAAA,CAACvB,UAAU;QAACyE,OAAO,EAAC,IAAI;QAACC,YAAY;QAAAJ,QAAA,EAAC;MAEtC;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACbvD,OAAA,CAACvB,UAAU;QAACyE,OAAO,EAAC,OAAO;QAACM,KAAK,EAAC,gBAAgB;QAACR,EAAE,EAAE;UAAES,EAAE,EAAE;QAAE,CAAE;QAAAV,QAAA,EAAC;MAElE;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,EAGZlD,WAAW,iBACVL,OAAA,CAACX,KAAK;QAAC2D,EAAE,EAAE;UAAEU,CAAC,EAAE,CAAC;UAAED,EAAE,EAAE;QAAE,CAAE;QAAAV,QAAA,eACzB/C,OAAA,CAACpB,GAAG;UAACoE,EAAE,EAAE;YAAEW,OAAO,EAAE,MAAM;YAAEC,UAAU,EAAE,QAAQ;YAAEC,GAAG,EAAE;UAAE,CAAE;UAAAd,QAAA,gBACzD/C,OAAA,CAACnB,MAAM;YAACiF,GAAG,EAAExD,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAE6B,cAAe;YAAAY,QAAA,GACtCzC,WAAW,aAAXA,WAAW,wBAAAH,qBAAA,GAAXG,WAAW,CAAE2B,SAAS,cAAA9B,qBAAA,uBAAtBA,qBAAA,CAAyB,CAAC,CAAC,EAAEG,WAAW,aAAXA,WAAW,wBAAAF,qBAAA,GAAXE,WAAW,CAAE4B,QAAQ,cAAA9B,qBAAA,uBAArBA,qBAAA,CAAwB,CAAC,CAAC;UAAA;YAAAgD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClD,CAAC,eACTvD,OAAA,CAACrB,SAAS;YACRoF,SAAS;YACTC,WAAW,EAAC,sBAAsB;YAClCd,OAAO,EAAC,UAAU;YAClBe,OAAO,EAAEA,CAAA,KAAMrD,iBAAiB,CAAC,IAAI,CAAE;YACvCoC,EAAE,EAAE;cAAEkB,MAAM,EAAE;YAAU,CAAE;YAC1BC,UAAU,EAAE;cACVC,QAAQ,EAAE;YACZ;UAAE;YAAAhB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CACR,eAGDvD,OAAA,CAAC3B,IAAI;QAACgG,SAAS;QAACC,OAAO,EAAE,CAAE;QAAAvB,QAAA,EACxBxC,KAAK,CAAC+B,GAAG,CAAEC,IAAI,iBACdvC,OAAA,CAAC3B,IAAI;UAACkG,IAAI;UAACC,EAAE,EAAE,EAAG;UAAAzB,QAAA,eAChB/C,OAAA,CAAC1B,IAAI;YAAAyE,QAAA,gBACH/C,OAAA,CAACzB,WAAW;cAAAwE,QAAA,gBACV/C,OAAA,CAACpB,GAAG;gBAACoE,EAAE,EAAE;kBAAEW,OAAO,EAAE,MAAM;kBAAEC,UAAU,EAAE,QAAQ;kBAAEH,EAAE,EAAE;gBAAE,CAAE;gBAAAV,QAAA,gBACxD/C,OAAA,CAACnB,MAAM;kBAACiF,GAAG,EAAEvB,IAAI,CAACpB,YAAa;kBAAC6B,EAAE,EAAE;oBAAEyB,EAAE,EAAE;kBAAE,CAAE;kBAAA1B,QAAA,EAC3CR,IAAI,CAACrB,UAAU,CAACwD,KAAK,CAAC,GAAG,CAAC,CAACpC,GAAG,CAACqC,CAAC,IAAIA,CAAC,CAAC,CAAC,CAAC,CAAC,CAACC,IAAI,CAAC,EAAE;gBAAC;kBAAAxB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7C,CAAC,eACTvD,OAAA,CAACpB,GAAG;kBAACoE,EAAE,EAAE;oBAAE6B,QAAQ,EAAE;kBAAE,CAAE;kBAAA9B,QAAA,gBACvB/C,OAAA,CAACvB,UAAU;oBAACyE,OAAO,EAAC,WAAW;oBAAC4B,UAAU,EAAC,MAAM;oBAAA/B,QAAA,EAC9CR,IAAI,CAACrB;kBAAU;oBAAAkC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC,eACbvD,OAAA,CAACvB,UAAU;oBAACyE,OAAO,EAAC,SAAS;oBAACM,KAAK,EAAC,gBAAgB;oBAAAT,QAAA,EACjDP,aAAa,CAACD,IAAI,CAAClB,SAAS;kBAAC;oBAAA+B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV,CAAC,eACNvD,OAAA,CAAClB,UAAU;kBAAAiE,QAAA,eACT/C,OAAA,CAACJ,QAAQ;oBAAAwD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC,eAENvD,OAAA,CAACvB,UAAU;gBAACyE,OAAO,EAAC,OAAO;gBAACF,EAAE,EAAE;kBAAES,EAAE,EAAE;gBAAE,CAAE;gBAAAV,QAAA,EACvCR,IAAI,CAACnB;cAAO;gBAAAgC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,EAEZhB,IAAI,CAACZ,IAAI,CAACoD,MAAM,GAAG,CAAC,iBACnB/E,OAAA,CAACpB,GAAG;gBAACoE,EAAE,EAAE;kBAAES,EAAE,EAAE;gBAAE,CAAE;gBAAAV,QAAA,EAChBR,IAAI,CAACZ,IAAI,CAACW,GAAG,CAAE0C,GAAG,iBACjBhF,OAAA,CAACZ,IAAI;kBAEH6F,KAAK,EAAE,IAAID,GAAG,EAAG;kBACjBE,IAAI,EAAC,OAAO;kBACZlC,EAAE,EAAE;oBAAEyB,EAAE,EAAE,CAAC;oBAAEhB,EAAE,EAAE;kBAAE,CAAE;kBACrBD,KAAK,EAAC,SAAS;kBACfN,OAAO,EAAC;gBAAU,GALb8B,GAAG;kBAAA5B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAMT,CACF;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACU,CAAC,eAEdvD,OAAA,CAACV,OAAO;cAAA8D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAEXvD,OAAA,CAACxB,WAAW;cAACwE,EAAE,EAAE;gBAAEmC,cAAc,EAAE,eAAe;gBAAEC,EAAE,EAAE;cAAE,CAAE;cAAArC,QAAA,eAC1D/C,OAAA,CAACpB,GAAG;gBAACoE,EAAE,EAAE;kBAAEW,OAAO,EAAE,MAAM;kBAAEE,GAAG,EAAE;gBAAE,CAAE;gBAAAd,QAAA,gBACnC/C,OAAA,CAACtB,MAAM;kBACL2G,SAAS,EAAE9C,IAAI,CAACb,OAAO,gBAAG1B,OAAA,CAACR,QAAQ;oBAACgE,KAAK,EAAC;kBAAO;oBAAAJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,gBAAGvD,OAAA,CAACP,cAAc;oBAAA2D,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAE;kBAC1EU,OAAO,EAAEA,CAAA,KAAM7B,UAAU,CAACG,IAAI,CAACvB,EAAE,CAAE;kBACnCwC,KAAK,EAAEjB,IAAI,CAACb,OAAO,GAAG,OAAO,GAAG,SAAU;kBAAAqB,QAAA,EAEzCR,IAAI,CAACf;gBAAK;kBAAA4B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC,eACTvD,OAAA,CAACtB,MAAM;kBAAC2G,SAAS,eAAErF,OAAA,CAACN,OAAO;oBAAA0D,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAE;kBAAAR,QAAA,EAC5BR,IAAI,CAACd;gBAAQ;kBAAA2B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACR,CAAC,eACTvD,OAAA,CAACtB,MAAM;kBAAC2G,SAAS,eAAErF,OAAA,CAACL,KAAK;oBAAAyD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAE;kBAAAR,QAAA,EAAC;gBAE9B;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC,GA3DehB,IAAI,CAACvB,EAAE;UAAAoC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OA4DzB,CACP;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,EAGNlD,WAAW,iBACVL,OAAA,CAACjB,GAAG;QACFyE,KAAK,EAAC,SAAS;QACf,cAAW,aAAa;QACxBR,EAAE,EAAE;UAAEsC,QAAQ,EAAE,OAAO;UAAEC,MAAM,EAAE,EAAE;UAAEC,KAAK,EAAE;QAAG,CAAE;QACjDvB,OAAO,EAAEA,CAAA,KAAMrD,iBAAiB,CAAC,IAAI,CAAE;QAAAmC,QAAA,eAEvC/C,OAAA,CAACT,GAAG;UAAA6D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CACN,eAGDvD,OAAA,CAAChB,MAAM;QAACyG,IAAI,EAAE9E,cAAe;QAAC+E,OAAO,EAAEA,CAAA,KAAM9E,iBAAiB,CAAC,KAAK,CAAE;QAACkC,QAAQ,EAAC,IAAI;QAACiB,SAAS;QAAAhB,QAAA,gBAC5F/C,OAAA,CAACf,WAAW;UAAA8D,QAAA,EAAC;QAAe;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa,CAAC,eAC1CvD,OAAA,CAACd,aAAa;UAAA6D,QAAA,gBACZ/C,OAAA,CAACrB,SAAS;YACRgH,SAAS;YACTC,MAAM,EAAC,OAAO;YACdX,KAAK,EAAC,sBAAsB;YAC5BlB,SAAS;YACT8B,SAAS;YACTC,IAAI,EAAE,CAAE;YACR5C,OAAO,EAAC,UAAU;YAClB6C,KAAK,EAAEtF,cAAe;YACtBuF,QAAQ,EAAGC,CAAC,IAAKvF,iBAAiB,CAACuF,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;YACnD/C,EAAE,EAAE;cAAES,EAAE,EAAE;YAAE;UAAE;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACf,CAAC,eAEFvD,OAAA,CAACvB,UAAU;YAACyE,OAAO,EAAC,WAAW;YAACC,YAAY;YAAAJ,QAAA,EAAC;UAE7C;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbvD,OAAA,CAACpB,GAAG;YAACoE,EAAE,EAAE;cAAEW,OAAO,EAAE,MAAM;cAAEwC,QAAQ,EAAE,MAAM;cAAEtC,GAAG,EAAE,CAAC;cAAEJ,EAAE,EAAE;YAAE,CAAE;YAAAV,QAAA,EAC3DF,aAAa,CAACP,GAAG,CAAE0C,GAAG,iBACrBhF,OAAA,CAACZ,IAAI;cAEH6F,KAAK,EAAE,IAAID,GAAG,EAAG;cACjBoB,SAAS;cACT5C,KAAK,EAAE3C,YAAY,CAACwF,QAAQ,CAACrB,GAAG,CAAC,GAAG,SAAS,GAAG,SAAU;cAC1Df,OAAO,EAAEA,CAAA,KAAM;gBACbnD,eAAe,CAACwF,IAAI,IAClBA,IAAI,CAACD,QAAQ,CAACrB,GAAG,CAAC,GACdsB,IAAI,CAACC,MAAM,CAACC,CAAC,IAAIA,CAAC,KAAKxB,GAAG,CAAC,GAC3B,CAAC,GAAGsB,IAAI,EAAEtB,GAAG,CACnB,CAAC;cACH;YAAE,GAVGA,GAAG;cAAA5B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAWT,CACF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC,eAChBvD,OAAA,CAACb,aAAa;UAAA4D,QAAA,gBACZ/C,OAAA,CAACtB,MAAM;YAACuF,OAAO,EAAEA,CAAA,KAAMrD,iBAAiB,CAAC,KAAK,CAAE;YAAAmC,QAAA,EAAC;UAAM;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAChEvD,OAAA,CAACtB,MAAM;YACLuF,OAAO,EAAErC,gBAAiB;YAC1BsB,OAAO,EAAC,WAAW;YACnBuD,QAAQ,EAAE,CAAChG,cAAc,CAACoB,IAAI,CAAC,CAAE;YACjCwD,SAAS,eAAErF,OAAA,CAACH,IAAI;cAAAuD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAAAR,QAAA,EACrB;UAED;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACG,CAAC;AAEhB,CAAC;AAACrD,EAAA,CAlQID,IAAc;EAAA,QACmBH,OAAO;AAAA;AAAA4G,EAAA,GADxCzG,IAAc;AAoQpB,eAAeA,IAAI;AAAC,IAAAyG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}