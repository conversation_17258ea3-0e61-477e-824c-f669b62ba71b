{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\\\u30C9\\u30AD\\u30E5\\u30E1\\u30F3\\u30C8\\\\Projects\\\\StudyHub\\\\studyhub-app\\\\src\\\\pages\\\\Chat.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Container, Grid, Paper, List, ListItem, ListItemAvatar, ListItemText, Avatar, Typography, Box, Badge, TextField, InputAdornment, Fab, Dialog, DialogTitle, DialogContent, DialogActions, Button, FormControl, InputLabel, Select, MenuItem, CircularProgress } from '@mui/material';\nimport { Search, Add, Message, Group, Person } from '@mui/icons-material';\nimport { formatDistanceToNow } from 'date-fns';\nimport { ChatService } from '../services/chatService';\nimport { StudyGroupService } from '../services/studyGroupService';\nimport { useAuth } from '../contexts/AuthContext';\nimport GroupChat from '../components/Chat/GroupChat';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst Chat = () => {\n  _s();\n  const {\n    user\n  } = useAuth();\n  const [chats, setChats] = useState([]);\n  const [selectedChat, setSelectedChat] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [newChatOpen, setNewChatOpen] = useState(false);\n  const [chatType, setChatType] = useState('private');\n  const [studyGroups, setStudyGroups] = useState([]);\n  const [selectedUsers, setSelectedUsers] = useState([]);\n  const [newChatData, setNewChatData] = useState({\n    name: '',\n    description: '',\n    participantEmail: ''\n  });\n  useEffect(() => {\n    if (!user) return;\n\n    // Subscribe to user's chats\n    const unsubscribe = ChatService.subscribeToUserChats(user.uid, userChats => {\n      setChats(userChats);\n      setLoading(false);\n    });\n\n    // Load user's study groups for group chat creation\n    loadStudyGroups();\n    return unsubscribe;\n  }, [user]);\n  const loadStudyGroups = async () => {\n    if (!user) return;\n    try {\n      const groups = await StudyGroupService.getUserStudyGroups(user.uid);\n      setStudyGroups(groups);\n    } catch (error) {\n      console.error('Error loading study groups:', error);\n    }\n  };\n  const filteredChats = chats.filter(chat => {\n    if (!searchTerm) return true;\n    const searchLower = searchTerm.toLowerCase();\n    if (chat.type === 'group') {\n      var _chat$name;\n      return (_chat$name = chat.name) === null || _chat$name === void 0 ? void 0 : _chat$name.toLowerCase().includes(searchLower);\n    } else {\n      var _chat$participantDeta;\n      // For private chats, search by participant names\n      return (_chat$participantDeta = chat.participantDetails) === null || _chat$participantDeta === void 0 ? void 0 : _chat$participantDeta.some(p => p.displayName.toLowerCase().includes(searchLower) || p.email.toLowerCase().includes(searchLower));\n    }\n  });\n  const handleCreatePrivateChat = async () => {\n    if (!user || !newChatData.participantEmail.trim()) return;\n    try {\n      var _user$email;\n      setLoading(true);\n\n      // TODO: Find user by email and get their profile\n      // For now, we'll create a mock profile\n      const otherUserProfile = {\n        displayName: newChatData.participantEmail.split('@')[0],\n        email: newChatData.participantEmail,\n        profilePicture: ''\n      };\n      const currentUserProfile = {\n        displayName: user.displayName || ((_user$email = user.email) === null || _user$email === void 0 ? void 0 : _user$email.split('@')[0]) || 'User',\n        email: user.email || '',\n        profilePicture: user.photoURL || ''\n      };\n      const chatData = {\n        participantId: 'temp-user-id',\n        // TODO: Get actual user ID\n        initialMessage: `Hi! I'd like to connect with you on StudyHub.`\n      };\n      const chatId = await ChatService.createPrivateChat(user.uid, chatData, currentUserProfile, otherUserProfile);\n      setNewChatOpen(false);\n      setNewChatData({\n        name: '',\n        description: '',\n        participantEmail: ''\n      });\n    } catch (error) {\n      console.error('Error creating private chat:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleCreateGroupChat = async () => {\n    if (!user || !newChatData.name.trim()) return;\n    try {\n      setLoading(true);\n\n      // TODO: Implement group chat creation\n      console.log('Creating group chat:', newChatData);\n      setNewChatOpen(false);\n      setNewChatData({\n        name: '',\n        description: '',\n        participantEmail: ''\n      });\n    } catch (error) {\n      console.error('Error creating group chat:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const getChatDisplayName = chat => {\n    if (chat.type === 'group') {\n      return chat.name || 'Group Chat';\n    } else {\n      var _chat$participantDeta2;\n      // For private chats, show the other participant's name\n      const otherParticipant = (_chat$participantDeta2 = chat.participantDetails) === null || _chat$participantDeta2 === void 0 ? void 0 : _chat$participantDeta2.find(p => p.uid !== (user === null || user === void 0 ? void 0 : user.uid));\n      return (otherParticipant === null || otherParticipant === void 0 ? void 0 : otherParticipant.displayName) || 'Private Chat';\n    }\n  };\n  const getChatAvatar = chat => {\n    if (chat.type === 'group') {\n      var _chat$name2;\n      return ((_chat$name2 = chat.name) === null || _chat$name2 === void 0 ? void 0 : _chat$name2[0]) || 'G';\n    } else {\n      var _chat$participantDeta3, _otherParticipant$dis;\n      const otherParticipant = (_chat$participantDeta3 = chat.participantDetails) === null || _chat$participantDeta3 === void 0 ? void 0 : _chat$participantDeta3.find(p => p.uid !== (user === null || user === void 0 ? void 0 : user.uid));\n      return (otherParticipant === null || otherParticipant === void 0 ? void 0 : otherParticipant.profilePicture) || (otherParticipant === null || otherParticipant === void 0 ? void 0 : (_otherParticipant$dis = otherParticipant.displayName) === null || _otherParticipant$dis === void 0 ? void 0 : _otherParticipant$dis[0]) || 'U';\n    }\n  };\n  const getLastMessagePreview = chat => {\n    if (!chat.lastMessage) return 'No messages yet';\n    const content = chat.lastMessage.content;\n    const isOwnMessage = chat.lastMessage.senderId === (user === null || user === void 0 ? void 0 : user.uid);\n    const prefix = isOwnMessage ? 'You: ' : '';\n    return `${prefix}${content.length > 50 ? content.substring(0, 50) + '...' : content}`;\n  };\n  const formatLastMessageTime = chat => {\n    if (!chat.lastMessageAt) return '';\n    const date = chat.lastMessageAt.toDate ? chat.lastMessageAt.toDate() : new Date(chat.lastMessageAt);\n    return formatDistanceToNow(date, {\n      addSuffix: true\n    });\n  };\n  const getUnreadCount = chat => {\n    var _chat$unreadCount;\n    return ((_chat$unreadCount = chat.unreadCount) === null || _chat$unreadCount === void 0 ? void 0 : _chat$unreadCount[(user === null || user === void 0 ? void 0 : user.uid) || '']) || 0;\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(Container, {\n      maxWidth: \"lg\",\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        display: \"flex\",\n        justifyContent: \"center\",\n        alignItems: \"center\",\n        height: \"400px\",\n        children: /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 207,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 206,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 205,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(Container, {\n    maxWidth: \"lg\",\n    sx: {\n      py: 3\n    },\n    children: [/*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"h4\",\n      component: \"h1\",\n      gutterBottom: true,\n      children: \"Messages\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 215,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 3,\n      sx: {\n        height: 'calc(100vh - 200px)'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Grid, {\n        size: {\n          xs: 12,\n          md: 4\n        },\n        children: /*#__PURE__*/_jsxDEV(Paper, {\n          sx: {\n            height: '100%',\n            display: 'flex',\n            flexDirection: 'column'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              p: 2,\n              borderBottom: 1,\n              borderColor: 'divider'\n            },\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              placeholder: \"Search conversations...\",\n              value: searchTerm,\n              onChange: e => setSearchTerm(e.target.value),\n              InputProps: {\n                startAdornment: /*#__PURE__*/_jsxDEV(InputAdornment, {\n                  position: \"start\",\n                  children: /*#__PURE__*/_jsxDEV(Search, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 233,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 232,\n                  columnNumber: 21\n                }, this)\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 225,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 224,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(List, {\n            sx: {\n              flexGrow: 1,\n              overflow: 'auto',\n              py: 0\n            },\n            children: filteredChats.length === 0 ? /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                p: 3,\n                textAlign: 'center'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                color: \"text.secondary\",\n                children: \"No conversations yet\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 244,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"caption\",\n                color: \"text.secondary\",\n                children: \"Start a new conversation to get started\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 247,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 243,\n              columnNumber: 17\n            }, this) : filteredChats.map(chat => /*#__PURE__*/_jsxDEV(ListItem, {\n              onClick: () => setSelectedChat(chat),\n              sx: {\n                cursor: 'pointer',\n                borderBottom: 1,\n                borderColor: 'divider',\n                bgcolor: (selectedChat === null || selectedChat === void 0 ? void 0 : selectedChat.id) === chat.id ? 'action.selected' : 'transparent',\n                '&:hover': {\n                  bgcolor: 'action.hover'\n                }\n              },\n              children: [/*#__PURE__*/_jsxDEV(ListItemAvatar, {\n                children: /*#__PURE__*/_jsxDEV(Badge, {\n                  badgeContent: getUnreadCount(chat),\n                  color: \"primary\",\n                  invisible: getUnreadCount(chat) === 0,\n                  children: /*#__PURE__*/_jsxDEV(Avatar, {\n                    src: typeof getChatAvatar(chat) === 'string' && getChatAvatar(chat).startsWith('http') ? getChatAvatar(chat) : undefined,\n                    children: typeof getChatAvatar(chat) === 'string' && !getChatAvatar(chat).startsWith('http') ? getChatAvatar(chat) : ''\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 272,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 267,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 266,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n                primary: /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    display: 'flex',\n                    alignItems: 'center',\n                    gap: 1\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"subtitle2\",\n                    noWrap: true,\n                    children: getChatDisplayName(chat)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 280,\n                    columnNumber: 27\n                  }, this), chat.type === 'group' && /*#__PURE__*/_jsxDEV(Group, {\n                    fontSize: \"small\",\n                    color: \"action\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 284,\n                    columnNumber: 29\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 279,\n                  columnNumber: 25\n                }, this),\n                secondary: /*#__PURE__*/_jsxDEV(Box, {\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    color: \"text.secondary\",\n                    noWrap: true,\n                    children: getLastMessagePreview(chat)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 290,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"caption\",\n                    color: \"text.secondary\",\n                    children: formatLastMessageTime(chat)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 293,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 289,\n                  columnNumber: 25\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 277,\n                columnNumber: 21\n              }, this)]\n            }, chat.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 253,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 241,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 222,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 221,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        size: {\n          xs: 12,\n          md: 8\n        },\n        children: /*#__PURE__*/_jsxDEV(Paper, {\n          sx: {\n            height: '100%'\n          },\n          children: selectedChat ? /*#__PURE__*/_jsxDEV(GroupChat, {\n            chatId: selectedChat.id,\n            chat: selectedChat,\n            onClose: () => setSelectedChat(null)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 310,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              height: '100%',\n              display: 'flex',\n              flexDirection: 'column',\n              alignItems: 'center',\n              justifyContent: 'center',\n              gap: 2\n            },\n            children: [/*#__PURE__*/_jsxDEV(Message, {\n              sx: {\n                fontSize: 64,\n                color: 'text.secondary'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 326,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              color: \"text.secondary\",\n              children: \"Select a conversation to start messaging\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 327,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"text.secondary\",\n              children: \"Choose from your existing conversations or start a new one\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 330,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 316,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 308,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 307,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 219,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Fab, {\n      color: \"primary\",\n      \"aria-label\": \"new chat\",\n      sx: {\n        position: 'fixed',\n        bottom: 16,\n        right: 16\n      },\n      onClick: () => setNewChatOpen(true),\n      children: /*#__PURE__*/_jsxDEV(Add, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 346,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 340,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: newChatOpen,\n      onClose: () => setNewChatOpen(false),\n      maxWidth: \"sm\",\n      fullWidth: true,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: \"Start New Conversation\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 351,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            pt: 1\n          },\n          children: [/*#__PURE__*/_jsxDEV(FormControl, {\n            fullWidth: true,\n            sx: {\n              mb: 2\n            },\n            children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n              children: \"Chat Type\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 355,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Select, {\n              value: chatType,\n              label: \"Chat Type\",\n              onChange: e => setChatType(e.target.value),\n              children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"private\",\n                children: /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    display: 'flex',\n                    alignItems: 'center',\n                    gap: 1\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Person, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 363,\n                    columnNumber: 21\n                  }, this), \"Private Chat\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 362,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 361,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"group\",\n                children: /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    display: 'flex',\n                    alignItems: 'center',\n                    gap: 1\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Group, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 369,\n                    columnNumber: 21\n                  }, this), \"Group Chat\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 368,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 367,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 356,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 354,\n            columnNumber: 13\n          }, this), chatType === 'private' ? /*#__PURE__*/_jsxDEV(TextField, {\n            fullWidth: true,\n            label: \"User Email\",\n            value: newChatData.participantEmail,\n            onChange: e => setNewChatData({\n              ...newChatData,\n              participantEmail: e.target.value\n            }),\n            placeholder: \"Enter email address of the person you want to chat with\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 377,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Group Name\",\n              value: newChatData.name,\n              onChange: e => setNewChatData({\n                ...newChatData,\n                name: e.target.value\n              }),\n              sx: {\n                mb: 2\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 386,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Description (optional)\",\n              multiline: true,\n              rows: 2,\n              value: newChatData.description,\n              onChange: e => setNewChatData({\n                ...newChatData,\n                description: e.target.value\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 393,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 353,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 352,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: () => setNewChatOpen(false),\n          children: \"Cancel\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 406,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: chatType === 'private' ? handleCreatePrivateChat : handleCreateGroupChat,\n          variant: \"contained\",\n          disabled: loading || chatType === 'private' && !newChatData.participantEmail.trim() || chatType === 'group' && !newChatData.name.trim(),\n          children: loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n            size: 20\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 416,\n            columnNumber: 24\n          }, this) : 'Start Chat'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 407,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 405,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 350,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 214,\n    columnNumber: 5\n  }, this);\n};\n_s(Chat, \"yTi2OZhPbbkCWKAzQKPLfevPGN4=\", false, function () {\n  return [useAuth];\n});\n_c = Chat;\nexport default Chat;\nvar _c;\n$RefreshReg$(_c, \"Chat\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Container", "Grid", "Paper", "List", "ListItem", "ListItemAvatar", "ListItemText", "Avatar", "Typography", "Box", "Badge", "TextField", "InputAdornment", "Fab", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "<PERSON><PERSON>", "FormControl", "InputLabel", "Select", "MenuItem", "CircularProgress", "Search", "Add", "Message", "Group", "Person", "formatDistanceToNow", "ChatService", "StudyGroupService", "useAuth", "GroupChat", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Cha<PERSON>", "_s", "user", "chats", "setChats", "selectedC<PERSON>", "setSelectedChat", "loading", "setLoading", "searchTerm", "setSearchTerm", "newChatOpen", "setNewChatOpen", "chatType", "setChatType", "studyGroups", "setStudyGroups", "selectedUsers", "setSelectedUsers", "newChatData", "setNewChatData", "name", "description", "participantEmail", "unsubscribe", "subscribeToUserChats", "uid", "userChats", "loadStudyGroups", "groups", "getUserStudyGroups", "error", "console", "filteredChats", "filter", "chat", "searchLower", "toLowerCase", "type", "_chat$name", "includes", "_chat$participantDeta", "participantDetails", "some", "p", "displayName", "email", "handleCreatePrivateChat", "trim", "_user$email", "otherUserProfile", "split", "profilePicture", "currentUserProfile", "photoURL", "chatData", "participantId", "initialMessage", "chatId", "createPrivateChat", "handleCreateGroupChat", "log", "getChatDisplayName", "_chat$participantDeta2", "otherParticipant", "find", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_chat$name2", "_chat$participantDeta3", "_otherParticipant$dis", "getLastMessagePreview", "lastMessage", "content", "isOwnMessage", "senderId", "prefix", "length", "substring", "formatLastMessageTime", "lastMessageAt", "date", "toDate", "Date", "addSuffix", "getUnreadCount", "_chat$unreadCount", "unreadCount", "max<PERSON><PERSON><PERSON>", "children", "display", "justifyContent", "alignItems", "height", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "sx", "py", "variant", "component", "gutterBottom", "container", "spacing", "size", "xs", "md", "flexDirection", "borderBottom", "borderColor", "fullWidth", "placeholder", "value", "onChange", "e", "target", "InputProps", "startAdornment", "position", "flexGrow", "overflow", "textAlign", "color", "map", "onClick", "cursor", "bgcolor", "id", "badgeContent", "invisible", "src", "startsWith", "undefined", "primary", "gap", "noWrap", "fontSize", "secondary", "onClose", "bottom", "right", "open", "pt", "mb", "label", "multiline", "rows", "disabled", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/ドキュメント/Projects/StudyHub/studyhub-app/src/pages/Chat.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Container,\n  Grid,\n  Paper,\n  List,\n  ListItem,\n  ListItemAvatar,\n  ListItemText,\n  Avatar,\n  Typography,\n  Box,\n  Badge,\n  IconButton,\n  TextField,\n  InputAdornment,\n  Fab,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  Button,\n  FormControl,\n  InputLabel,\n  Select,\n  MenuItem,\n  Chip,\n  CircularProgress,\n} from '@mui/material';\nimport {\n  Search,\n  Add,\n  Message,\n  Group,\n  Person,\n  Close,\n} from '@mui/icons-material';\nimport { formatDistanceToNow } from 'date-fns';\nimport { ChatService } from '../services/chatService';\nimport { StudyGroupService } from '../services/studyGroupService';\nimport { Chat as ChatType, CreatePrivateChatData } from '../types/chat';\nimport { StudyGroup } from '../types/studyGroup';\nimport { useAuth } from '../contexts/AuthContext';\nimport GroupChat from '../components/Chat/GroupChat';\n\nconst Chat: React.FC = () => {\n  const { user } = useAuth();\n  const [chats, setChats] = useState<ChatType[]>([]);\n  const [selectedChat, setSelectedChat] = useState<ChatType | null>(null);\n  const [loading, setLoading] = useState(true);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [newChatOpen, setNewChatOpen] = useState(false);\n  const [chatType, setChatType] = useState<'private' | 'group'>('private');\n  const [studyGroups, setStudyGroups] = useState<StudyGroup[]>([]);\n  const [selectedUsers, setSelectedUsers] = useState<string[]>([]);\n  const [newChatData, setNewChatData] = useState({\n    name: '',\n    description: '',\n    participantEmail: '',\n  });\n\n  useEffect(() => {\n    if (!user) return;\n\n    // Subscribe to user's chats\n    const unsubscribe = ChatService.subscribeToUserChats(user.uid, (userChats) => {\n      setChats(userChats);\n      setLoading(false);\n    });\n\n    // Load user's study groups for group chat creation\n    loadStudyGroups();\n\n    return unsubscribe;\n  }, [user]);\n\n  const loadStudyGroups = async () => {\n    if (!user) return;\n\n    try {\n      const groups = await StudyGroupService.getUserStudyGroups(user.uid);\n      setStudyGroups(groups);\n    } catch (error) {\n      console.error('Error loading study groups:', error);\n    }\n  };\n\n  const filteredChats = chats.filter(chat => {\n    if (!searchTerm) return true;\n    \n    const searchLower = searchTerm.toLowerCase();\n    \n    if (chat.type === 'group') {\n      return chat.name?.toLowerCase().includes(searchLower);\n    } else {\n      // For private chats, search by participant names\n      return chat.participantDetails?.some(p => \n        p.displayName.toLowerCase().includes(searchLower) ||\n        p.email.toLowerCase().includes(searchLower)\n      );\n    }\n  });\n\n  const handleCreatePrivateChat = async () => {\n    if (!user || !newChatData.participantEmail.trim()) return;\n\n    try {\n      setLoading(true);\n      \n      // TODO: Find user by email and get their profile\n      // For now, we'll create a mock profile\n      const otherUserProfile = {\n        displayName: newChatData.participantEmail.split('@')[0],\n        email: newChatData.participantEmail,\n        profilePicture: '',\n      };\n\n      const currentUserProfile = {\n        displayName: user.displayName || user.email?.split('@')[0] || 'User',\n        email: user.email || '',\n        profilePicture: user.photoURL || '',\n      };\n\n      const chatData: CreatePrivateChatData = {\n        participantId: 'temp-user-id', // TODO: Get actual user ID\n        initialMessage: `Hi! I'd like to connect with you on StudyHub.`,\n      };\n\n      const chatId = await ChatService.createPrivateChat(\n        user.uid,\n        chatData,\n        currentUserProfile,\n        otherUserProfile\n      );\n\n      setNewChatOpen(false);\n      setNewChatData({ name: '', description: '', participantEmail: '' });\n    } catch (error) {\n      console.error('Error creating private chat:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleCreateGroupChat = async () => {\n    if (!user || !newChatData.name.trim()) return;\n\n    try {\n      setLoading(true);\n      \n      // TODO: Implement group chat creation\n      console.log('Creating group chat:', newChatData);\n      \n      setNewChatOpen(false);\n      setNewChatData({ name: '', description: '', participantEmail: '' });\n    } catch (error) {\n      console.error('Error creating group chat:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const getChatDisplayName = (chat: ChatType) => {\n    if (chat.type === 'group') {\n      return chat.name || 'Group Chat';\n    } else {\n      // For private chats, show the other participant's name\n      const otherParticipant = chat.participantDetails?.find(p => p.uid !== user?.uid);\n      return otherParticipant?.displayName || 'Private Chat';\n    }\n  };\n\n  const getChatAvatar = (chat: ChatType) => {\n    if (chat.type === 'group') {\n      return chat.name?.[0] || 'G';\n    } else {\n      const otherParticipant = chat.participantDetails?.find(p => p.uid !== user?.uid);\n      return otherParticipant?.profilePicture || otherParticipant?.displayName?.[0] || 'U';\n    }\n  };\n\n  const getLastMessagePreview = (chat: ChatType) => {\n    if (!chat.lastMessage) return 'No messages yet';\n    \n    const content = chat.lastMessage.content;\n    const isOwnMessage = chat.lastMessage.senderId === user?.uid;\n    const prefix = isOwnMessage ? 'You: ' : '';\n    \n    return `${prefix}${content.length > 50 ? content.substring(0, 50) + '...' : content}`;\n  };\n\n  const formatLastMessageTime = (chat: ChatType) => {\n    if (!chat.lastMessageAt) return '';\n    \n    const date = chat.lastMessageAt.toDate ? chat.lastMessageAt.toDate() : new Date(chat.lastMessageAt);\n    return formatDistanceToNow(date, { addSuffix: true });\n  };\n\n  const getUnreadCount = (chat: ChatType) => {\n    return chat.unreadCount?.[user?.uid || ''] || 0;\n  };\n\n  if (loading) {\n    return (\n      <Container maxWidth=\"lg\">\n        <Box display=\"flex\" justifyContent=\"center\" alignItems=\"center\" height=\"400px\">\n          <CircularProgress />\n        </Box>\n      </Container>\n    );\n  }\n\n  return (\n    <Container maxWidth=\"lg\" sx={{ py: 3 }}>\n      <Typography variant=\"h4\" component=\"h1\" gutterBottom>\n        Messages\n      </Typography>\n\n      <Grid container spacing={3} sx={{ height: 'calc(100vh - 200px)' }}>\n        {/* Chat List */}\n        <Grid size={{ xs: 12, md: 4 }}>\n          <Paper sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>\n            {/* Search */}\n            <Box sx={{ p: 2, borderBottom: 1, borderColor: 'divider' }}>\n              <TextField\n                fullWidth\n                placeholder=\"Search conversations...\"\n                value={searchTerm}\n                onChange={(e) => setSearchTerm(e.target.value)}\n                InputProps={{\n                  startAdornment: (\n                    <InputAdornment position=\"start\">\n                      <Search />\n                    </InputAdornment>\n                  ),\n                }}\n              />\n            </Box>\n\n            {/* Chat List */}\n            <List sx={{ flexGrow: 1, overflow: 'auto', py: 0 }}>\n              {filteredChats.length === 0 ? (\n                <Box sx={{ p: 3, textAlign: 'center' }}>\n                  <Typography variant=\"body2\" color=\"text.secondary\">\n                    No conversations yet\n                  </Typography>\n                  <Typography variant=\"caption\" color=\"text.secondary\">\n                    Start a new conversation to get started\n                  </Typography>\n                </Box>\n              ) : (\n                filteredChats.map((chat) => (\n                  <ListItem\n                    key={chat.id}\n                    onClick={() => setSelectedChat(chat)}\n                    sx={{\n                      cursor: 'pointer',\n                      borderBottom: 1,\n                      borderColor: 'divider',\n                      bgcolor: selectedChat?.id === chat.id ? 'action.selected' : 'transparent',\n                      '&:hover': {\n                        bgcolor: 'action.hover',\n                      },\n                    }}\n                  >\n                    <ListItemAvatar>\n                      <Badge\n                        badgeContent={getUnreadCount(chat)}\n                        color=\"primary\"\n                        invisible={getUnreadCount(chat) === 0}\n                      >\n                        <Avatar src={typeof getChatAvatar(chat) === 'string' && getChatAvatar(chat).startsWith('http') ? getChatAvatar(chat) : undefined}>\n                          {typeof getChatAvatar(chat) === 'string' && !getChatAvatar(chat).startsWith('http') ? getChatAvatar(chat) : ''}\n                        </Avatar>\n                      </Badge>\n                    </ListItemAvatar>\n                    <ListItemText\n                      primary={\n                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\n                          <Typography variant=\"subtitle2\" noWrap>\n                            {getChatDisplayName(chat)}\n                          </Typography>\n                          {chat.type === 'group' && (\n                            <Group fontSize=\"small\" color=\"action\" />\n                          )}\n                        </Box>\n                      }\n                      secondary={\n                        <Box>\n                          <Typography variant=\"body2\" color=\"text.secondary\" noWrap>\n                            {getLastMessagePreview(chat)}\n                          </Typography>\n                          <Typography variant=\"caption\" color=\"text.secondary\">\n                            {formatLastMessageTime(chat)}\n                          </Typography>\n                        </Box>\n                      }\n                    />\n                  </ListItem>\n                ))\n              )}\n            </List>\n          </Paper>\n        </Grid>\n\n        {/* Chat Area */}\n        <Grid size={{ xs: 12, md: 8 }}>\n          <Paper sx={{ height: '100%' }}>\n            {selectedChat ? (\n              <GroupChat\n                chatId={selectedChat.id}\n                chat={selectedChat}\n                onClose={() => setSelectedChat(null)}\n              />\n            ) : (\n              <Box\n                sx={{\n                  height: '100%',\n                  display: 'flex',\n                  flexDirection: 'column',\n                  alignItems: 'center',\n                  justifyContent: 'center',\n                  gap: 2,\n                }}\n              >\n                <Message sx={{ fontSize: 64, color: 'text.secondary' }} />\n                <Typography variant=\"h6\" color=\"text.secondary\">\n                  Select a conversation to start messaging\n                </Typography>\n                <Typography variant=\"body2\" color=\"text.secondary\">\n                  Choose from your existing conversations or start a new one\n                </Typography>\n              </Box>\n            )}\n          </Paper>\n        </Grid>\n      </Grid>\n\n      {/* Floating Action Button */}\n      <Fab\n        color=\"primary\"\n        aria-label=\"new chat\"\n        sx={{ position: 'fixed', bottom: 16, right: 16 }}\n        onClick={() => setNewChatOpen(true)}\n      >\n        <Add />\n      </Fab>\n\n      {/* New Chat Dialog */}\n      <Dialog open={newChatOpen} onClose={() => setNewChatOpen(false)} maxWidth=\"sm\" fullWidth>\n        <DialogTitle>Start New Conversation</DialogTitle>\n        <DialogContent>\n          <Box sx={{ pt: 1 }}>\n            <FormControl fullWidth sx={{ mb: 2 }}>\n              <InputLabel>Chat Type</InputLabel>\n              <Select\n                value={chatType}\n                label=\"Chat Type\"\n                onChange={(e) => setChatType(e.target.value as 'private' | 'group')}\n              >\n                <MenuItem value=\"private\">\n                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\n                    <Person />\n                    Private Chat\n                  </Box>\n                </MenuItem>\n                <MenuItem value=\"group\">\n                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\n                    <Group />\n                    Group Chat\n                  </Box>\n                </MenuItem>\n              </Select>\n            </FormControl>\n\n            {chatType === 'private' ? (\n              <TextField\n                fullWidth\n                label=\"User Email\"\n                value={newChatData.participantEmail}\n                onChange={(e) => setNewChatData({ ...newChatData, participantEmail: e.target.value })}\n                placeholder=\"Enter email address of the person you want to chat with\"\n              />\n            ) : (\n              <>\n                <TextField\n                  fullWidth\n                  label=\"Group Name\"\n                  value={newChatData.name}\n                  onChange={(e) => setNewChatData({ ...newChatData, name: e.target.value })}\n                  sx={{ mb: 2 }}\n                />\n                <TextField\n                  fullWidth\n                  label=\"Description (optional)\"\n                  multiline\n                  rows={2}\n                  value={newChatData.description}\n                  onChange={(e) => setNewChatData({ ...newChatData, description: e.target.value })}\n                />\n              </>\n            )}\n          </Box>\n        </DialogContent>\n        <DialogActions>\n          <Button onClick={() => setNewChatOpen(false)}>Cancel</Button>\n          <Button\n            onClick={chatType === 'private' ? handleCreatePrivateChat : handleCreateGroupChat}\n            variant=\"contained\"\n            disabled={\n              loading ||\n              (chatType === 'private' && !newChatData.participantEmail.trim()) ||\n              (chatType === 'group' && !newChatData.name.trim())\n            }\n          >\n            {loading ? <CircularProgress size={20} /> : 'Start Chat'}\n          </Button>\n        </DialogActions>\n      </Dialog>\n    </Container>\n  );\n};\n\nexport default Chat;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,SAAS,EACTC,IAAI,EACJC,KAAK,EACLC,IAAI,EACJC,QAAQ,EACRC,cAAc,EACdC,YAAY,EACZC,MAAM,EACNC,UAAU,EACVC,GAAG,EACHC,KAAK,EAELC,SAAS,EACTC,cAAc,EACdC,GAAG,EACHC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,MAAM,EACNC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,QAAQ,EAERC,gBAAgB,QACX,eAAe;AACtB,SACEC,MAAM,EACNC,GAAG,EACHC,OAAO,EACPC,KAAK,EACLC,MAAM,QAED,qBAAqB;AAC5B,SAASC,mBAAmB,QAAQ,UAAU;AAC9C,SAASC,WAAW,QAAQ,yBAAyB;AACrD,SAASC,iBAAiB,QAAQ,+BAA+B;AAGjE,SAASC,OAAO,QAAQ,yBAAyB;AACjD,OAAOC,SAAS,MAAM,8BAA8B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAErD,MAAMC,IAAc,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC3B,MAAM;IAAEC;EAAK,CAAC,GAAGR,OAAO,CAAC,CAAC;EAC1B,MAAM,CAACS,KAAK,EAAEC,QAAQ,CAAC,GAAG5C,QAAQ,CAAa,EAAE,CAAC;EAClD,MAAM,CAAC6C,YAAY,EAAEC,eAAe,CAAC,GAAG9C,QAAQ,CAAkB,IAAI,CAAC;EACvE,MAAM,CAAC+C,OAAO,EAAEC,UAAU,CAAC,GAAGhD,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACiD,UAAU,EAAEC,aAAa,CAAC,GAAGlD,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACmD,WAAW,EAAEC,cAAc,CAAC,GAAGpD,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACqD,QAAQ,EAAEC,WAAW,CAAC,GAAGtD,QAAQ,CAAsB,SAAS,CAAC;EACxE,MAAM,CAACuD,WAAW,EAAEC,cAAc,CAAC,GAAGxD,QAAQ,CAAe,EAAE,CAAC;EAChE,MAAM,CAACyD,aAAa,EAAEC,gBAAgB,CAAC,GAAG1D,QAAQ,CAAW,EAAE,CAAC;EAChE,MAAM,CAAC2D,WAAW,EAAEC,cAAc,CAAC,GAAG5D,QAAQ,CAAC;IAC7C6D,IAAI,EAAE,EAAE;IACRC,WAAW,EAAE,EAAE;IACfC,gBAAgB,EAAE;EACpB,CAAC,CAAC;EAEF9D,SAAS,CAAC,MAAM;IACd,IAAI,CAACyC,IAAI,EAAE;;IAEX;IACA,MAAMsB,WAAW,GAAGhC,WAAW,CAACiC,oBAAoB,CAACvB,IAAI,CAACwB,GAAG,EAAGC,SAAS,IAAK;MAC5EvB,QAAQ,CAACuB,SAAS,CAAC;MACnBnB,UAAU,CAAC,KAAK,CAAC;IACnB,CAAC,CAAC;;IAEF;IACAoB,eAAe,CAAC,CAAC;IAEjB,OAAOJ,WAAW;EACpB,CAAC,EAAE,CAACtB,IAAI,CAAC,CAAC;EAEV,MAAM0B,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI,CAAC1B,IAAI,EAAE;IAEX,IAAI;MACF,MAAM2B,MAAM,GAAG,MAAMpC,iBAAiB,CAACqC,kBAAkB,CAAC5B,IAAI,CAACwB,GAAG,CAAC;MACnEV,cAAc,CAACa,MAAM,CAAC;IACxB,CAAC,CAAC,OAAOE,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;IACrD;EACF,CAAC;EAED,MAAME,aAAa,GAAG9B,KAAK,CAAC+B,MAAM,CAACC,IAAI,IAAI;IACzC,IAAI,CAAC1B,UAAU,EAAE,OAAO,IAAI;IAE5B,MAAM2B,WAAW,GAAG3B,UAAU,CAAC4B,WAAW,CAAC,CAAC;IAE5C,IAAIF,IAAI,CAACG,IAAI,KAAK,OAAO,EAAE;MAAA,IAAAC,UAAA;MACzB,QAAAA,UAAA,GAAOJ,IAAI,CAACd,IAAI,cAAAkB,UAAA,uBAATA,UAAA,CAAWF,WAAW,CAAC,CAAC,CAACG,QAAQ,CAACJ,WAAW,CAAC;IACvD,CAAC,MAAM;MAAA,IAAAK,qBAAA;MACL;MACA,QAAAA,qBAAA,GAAON,IAAI,CAACO,kBAAkB,cAAAD,qBAAA,uBAAvBA,qBAAA,CAAyBE,IAAI,CAACC,CAAC,IACpCA,CAAC,CAACC,WAAW,CAACR,WAAW,CAAC,CAAC,CAACG,QAAQ,CAACJ,WAAW,CAAC,IACjDQ,CAAC,CAACE,KAAK,CAACT,WAAW,CAAC,CAAC,CAACG,QAAQ,CAACJ,WAAW,CAC5C,CAAC;IACH;EACF,CAAC,CAAC;EAEF,MAAMW,uBAAuB,GAAG,MAAAA,CAAA,KAAY;IAC1C,IAAI,CAAC7C,IAAI,IAAI,CAACiB,WAAW,CAACI,gBAAgB,CAACyB,IAAI,CAAC,CAAC,EAAE;IAEnD,IAAI;MAAA,IAAAC,WAAA;MACFzC,UAAU,CAAC,IAAI,CAAC;;MAEhB;MACA;MACA,MAAM0C,gBAAgB,GAAG;QACvBL,WAAW,EAAE1B,WAAW,CAACI,gBAAgB,CAAC4B,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QACvDL,KAAK,EAAE3B,WAAW,CAACI,gBAAgB;QACnC6B,cAAc,EAAE;MAClB,CAAC;MAED,MAAMC,kBAAkB,GAAG;QACzBR,WAAW,EAAE3C,IAAI,CAAC2C,WAAW,MAAAI,WAAA,GAAI/C,IAAI,CAAC4C,KAAK,cAAAG,WAAA,uBAAVA,WAAA,CAAYE,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,KAAI,MAAM;QACpEL,KAAK,EAAE5C,IAAI,CAAC4C,KAAK,IAAI,EAAE;QACvBM,cAAc,EAAElD,IAAI,CAACoD,QAAQ,IAAI;MACnC,CAAC;MAED,MAAMC,QAA+B,GAAG;QACtCC,aAAa,EAAE,cAAc;QAAE;QAC/BC,cAAc,EAAE;MAClB,CAAC;MAED,MAAMC,MAAM,GAAG,MAAMlE,WAAW,CAACmE,iBAAiB,CAChDzD,IAAI,CAACwB,GAAG,EACR6B,QAAQ,EACRF,kBAAkB,EAClBH,gBACF,CAAC;MAEDtC,cAAc,CAAC,KAAK,CAAC;MACrBQ,cAAc,CAAC;QAAEC,IAAI,EAAE,EAAE;QAAEC,WAAW,EAAE,EAAE;QAAEC,gBAAgB,EAAE;MAAG,CAAC,CAAC;IACrE,CAAC,CAAC,OAAOQ,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;IACtD,CAAC,SAAS;MACRvB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMoD,qBAAqB,GAAG,MAAAA,CAAA,KAAY;IACxC,IAAI,CAAC1D,IAAI,IAAI,CAACiB,WAAW,CAACE,IAAI,CAAC2B,IAAI,CAAC,CAAC,EAAE;IAEvC,IAAI;MACFxC,UAAU,CAAC,IAAI,CAAC;;MAEhB;MACAwB,OAAO,CAAC6B,GAAG,CAAC,sBAAsB,EAAE1C,WAAW,CAAC;MAEhDP,cAAc,CAAC,KAAK,CAAC;MACrBQ,cAAc,CAAC;QAAEC,IAAI,EAAE,EAAE;QAAEC,WAAW,EAAE,EAAE;QAAEC,gBAAgB,EAAE;MAAG,CAAC,CAAC;IACrE,CAAC,CAAC,OAAOQ,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;IACpD,CAAC,SAAS;MACRvB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMsD,kBAAkB,GAAI3B,IAAc,IAAK;IAC7C,IAAIA,IAAI,CAACG,IAAI,KAAK,OAAO,EAAE;MACzB,OAAOH,IAAI,CAACd,IAAI,IAAI,YAAY;IAClC,CAAC,MAAM;MAAA,IAAA0C,sBAAA;MACL;MACA,MAAMC,gBAAgB,IAAAD,sBAAA,GAAG5B,IAAI,CAACO,kBAAkB,cAAAqB,sBAAA,uBAAvBA,sBAAA,CAAyBE,IAAI,CAACrB,CAAC,IAAIA,CAAC,CAAClB,GAAG,MAAKxB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEwB,GAAG,EAAC;MAChF,OAAO,CAAAsC,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAEnB,WAAW,KAAI,cAAc;IACxD;EACF,CAAC;EAED,MAAMqB,aAAa,GAAI/B,IAAc,IAAK;IACxC,IAAIA,IAAI,CAACG,IAAI,KAAK,OAAO,EAAE;MAAA,IAAA6B,WAAA;MACzB,OAAO,EAAAA,WAAA,GAAAhC,IAAI,CAACd,IAAI,cAAA8C,WAAA,uBAATA,WAAA,CAAY,CAAC,CAAC,KAAI,GAAG;IAC9B,CAAC,MAAM;MAAA,IAAAC,sBAAA,EAAAC,qBAAA;MACL,MAAML,gBAAgB,IAAAI,sBAAA,GAAGjC,IAAI,CAACO,kBAAkB,cAAA0B,sBAAA,uBAAvBA,sBAAA,CAAyBH,IAAI,CAACrB,CAAC,IAAIA,CAAC,CAAClB,GAAG,MAAKxB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEwB,GAAG,EAAC;MAChF,OAAO,CAAAsC,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAEZ,cAAc,MAAIY,gBAAgB,aAAhBA,gBAAgB,wBAAAK,qBAAA,GAAhBL,gBAAgB,CAAEnB,WAAW,cAAAwB,qBAAA,uBAA7BA,qBAAA,CAAgC,CAAC,CAAC,KAAI,GAAG;IACtF;EACF,CAAC;EAED,MAAMC,qBAAqB,GAAInC,IAAc,IAAK;IAChD,IAAI,CAACA,IAAI,CAACoC,WAAW,EAAE,OAAO,iBAAiB;IAE/C,MAAMC,OAAO,GAAGrC,IAAI,CAACoC,WAAW,CAACC,OAAO;IACxC,MAAMC,YAAY,GAAGtC,IAAI,CAACoC,WAAW,CAACG,QAAQ,MAAKxE,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEwB,GAAG;IAC5D,MAAMiD,MAAM,GAAGF,YAAY,GAAG,OAAO,GAAG,EAAE;IAE1C,OAAO,GAAGE,MAAM,GAAGH,OAAO,CAACI,MAAM,GAAG,EAAE,GAAGJ,OAAO,CAACK,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,KAAK,GAAGL,OAAO,EAAE;EACvF,CAAC;EAED,MAAMM,qBAAqB,GAAI3C,IAAc,IAAK;IAChD,IAAI,CAACA,IAAI,CAAC4C,aAAa,EAAE,OAAO,EAAE;IAElC,MAAMC,IAAI,GAAG7C,IAAI,CAAC4C,aAAa,CAACE,MAAM,GAAG9C,IAAI,CAAC4C,aAAa,CAACE,MAAM,CAAC,CAAC,GAAG,IAAIC,IAAI,CAAC/C,IAAI,CAAC4C,aAAa,CAAC;IACnG,OAAOxF,mBAAmB,CAACyF,IAAI,EAAE;MAAEG,SAAS,EAAE;IAAK,CAAC,CAAC;EACvD,CAAC;EAED,MAAMC,cAAc,GAAIjD,IAAc,IAAK;IAAA,IAAAkD,iBAAA;IACzC,OAAO,EAAAA,iBAAA,GAAAlD,IAAI,CAACmD,WAAW,cAAAD,iBAAA,uBAAhBA,iBAAA,CAAmB,CAAAnF,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEwB,GAAG,KAAI,EAAE,CAAC,KAAI,CAAC;EACjD,CAAC;EAED,IAAInB,OAAO,EAAE;IACX,oBACEV,OAAA,CAACnC,SAAS;MAAC6H,QAAQ,EAAC,IAAI;MAAAC,QAAA,eACtB3F,OAAA,CAAC1B,GAAG;QAACsH,OAAO,EAAC,MAAM;QAACC,cAAc,EAAC,QAAQ;QAACC,UAAU,EAAC,QAAQ;QAACC,MAAM,EAAC,OAAO;QAAAJ,QAAA,eAC5E3F,OAAA,CAACZ,gBAAgB;UAAA4G,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjB;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC;EAEhB;EAEA,oBACEnG,OAAA,CAACnC,SAAS;IAAC6H,QAAQ,EAAC,IAAI;IAACU,EAAE,EAAE;MAAEC,EAAE,EAAE;IAAE,CAAE;IAAAV,QAAA,gBACrC3F,OAAA,CAAC3B,UAAU;MAACiI,OAAO,EAAC,IAAI;MAACC,SAAS,EAAC,IAAI;MAACC,YAAY;MAAAb,QAAA,EAAC;IAErD;MAAAK,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,eAEbnG,OAAA,CAAClC,IAAI;MAAC2I,SAAS;MAACC,OAAO,EAAE,CAAE;MAACN,EAAE,EAAE;QAAEL,MAAM,EAAE;MAAsB,CAAE;MAAAJ,QAAA,gBAEhE3F,OAAA,CAAClC,IAAI;QAAC6I,IAAI,EAAE;UAAEC,EAAE,EAAE,EAAE;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAlB,QAAA,eAC5B3F,OAAA,CAACjC,KAAK;UAACqI,EAAE,EAAE;YAAEL,MAAM,EAAE,MAAM;YAAEH,OAAO,EAAE,MAAM;YAAEkB,aAAa,EAAE;UAAS,CAAE;UAAAnB,QAAA,gBAEtE3F,OAAA,CAAC1B,GAAG;YAAC8H,EAAE,EAAE;cAAErD,CAAC,EAAE,CAAC;cAAEgE,YAAY,EAAE,CAAC;cAAEC,WAAW,EAAE;YAAU,CAAE;YAAArB,QAAA,eACzD3F,OAAA,CAACxB,SAAS;cACRyI,SAAS;cACTC,WAAW,EAAC,yBAAyB;cACrCC,KAAK,EAAEvG,UAAW;cAClBwG,QAAQ,EAAGC,CAAC,IAAKxG,aAAa,CAACwG,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;cAC/CI,UAAU,EAAE;gBACVC,cAAc,eACZxH,OAAA,CAACvB,cAAc;kBAACgJ,QAAQ,EAAC,OAAO;kBAAA9B,QAAA,eAC9B3F,OAAA,CAACX,MAAM;oBAAA2G,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI;cAEpB;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAGNnG,OAAA,CAAChC,IAAI;YAACoI,EAAE,EAAE;cAAEsB,QAAQ,EAAE,CAAC;cAAEC,QAAQ,EAAE,MAAM;cAAEtB,EAAE,EAAE;YAAE,CAAE;YAAAV,QAAA,EAChDvD,aAAa,CAAC2C,MAAM,KAAK,CAAC,gBACzB/E,OAAA,CAAC1B,GAAG;cAAC8H,EAAE,EAAE;gBAAErD,CAAC,EAAE,CAAC;gBAAE6E,SAAS,EAAE;cAAS,CAAE;cAAAjC,QAAA,gBACrC3F,OAAA,CAAC3B,UAAU;gBAACiI,OAAO,EAAC,OAAO;gBAACuB,KAAK,EAAC,gBAAgB;gBAAAlC,QAAA,EAAC;cAEnD;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACbnG,OAAA,CAAC3B,UAAU;gBAACiI,OAAO,EAAC,SAAS;gBAACuB,KAAK,EAAC,gBAAgB;gBAAAlC,QAAA,EAAC;cAErD;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,GAEN/D,aAAa,CAAC0F,GAAG,CAAExF,IAAI,iBACrBtC,OAAA,CAAC/B,QAAQ;cAEP8J,OAAO,EAAEA,CAAA,KAAMtH,eAAe,CAAC6B,IAAI,CAAE;cACrC8D,EAAE,EAAE;gBACF4B,MAAM,EAAE,SAAS;gBACjBjB,YAAY,EAAE,CAAC;gBACfC,WAAW,EAAE,SAAS;gBACtBiB,OAAO,EAAE,CAAAzH,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAE0H,EAAE,MAAK5F,IAAI,CAAC4F,EAAE,GAAG,iBAAiB,GAAG,aAAa;gBACzE,SAAS,EAAE;kBACTD,OAAO,EAAE;gBACX;cACF,CAAE;cAAAtC,QAAA,gBAEF3F,OAAA,CAAC9B,cAAc;gBAAAyH,QAAA,eACb3F,OAAA,CAACzB,KAAK;kBACJ4J,YAAY,EAAE5C,cAAc,CAACjD,IAAI,CAAE;kBACnCuF,KAAK,EAAC,SAAS;kBACfO,SAAS,EAAE7C,cAAc,CAACjD,IAAI,CAAC,KAAK,CAAE;kBAAAqD,QAAA,eAEtC3F,OAAA,CAAC5B,MAAM;oBAACiK,GAAG,EAAE,OAAOhE,aAAa,CAAC/B,IAAI,CAAC,KAAK,QAAQ,IAAI+B,aAAa,CAAC/B,IAAI,CAAC,CAACgG,UAAU,CAAC,MAAM,CAAC,GAAGjE,aAAa,CAAC/B,IAAI,CAAC,GAAGiG,SAAU;oBAAA5C,QAAA,EAC9H,OAAOtB,aAAa,CAAC/B,IAAI,CAAC,KAAK,QAAQ,IAAI,CAAC+B,aAAa,CAAC/B,IAAI,CAAC,CAACgG,UAAU,CAAC,MAAM,CAAC,GAAGjE,aAAa,CAAC/B,IAAI,CAAC,GAAG;kBAAE;oBAAA0D,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxG;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACM,CAAC,eACjBnG,OAAA,CAAC7B,YAAY;gBACXqK,OAAO,eACLxI,OAAA,CAAC1B,GAAG;kBAAC8H,EAAE,EAAE;oBAAER,OAAO,EAAE,MAAM;oBAAEE,UAAU,EAAE,QAAQ;oBAAE2C,GAAG,EAAE;kBAAE,CAAE;kBAAA9C,QAAA,gBACzD3F,OAAA,CAAC3B,UAAU;oBAACiI,OAAO,EAAC,WAAW;oBAACoC,MAAM;oBAAA/C,QAAA,EACnC1B,kBAAkB,CAAC3B,IAAI;kBAAC;oBAAA0D,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACf,CAAC,EACZ7D,IAAI,CAACG,IAAI,KAAK,OAAO,iBACpBzC,OAAA,CAACR,KAAK;oBAACmJ,QAAQ,EAAC,OAAO;oBAACd,KAAK,EAAC;kBAAQ;oBAAA7B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CACzC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CACN;gBACDyC,SAAS,eACP5I,OAAA,CAAC1B,GAAG;kBAAAqH,QAAA,gBACF3F,OAAA,CAAC3B,UAAU;oBAACiI,OAAO,EAAC,OAAO;oBAACuB,KAAK,EAAC,gBAAgB;oBAACa,MAAM;oBAAA/C,QAAA,EACtDlB,qBAAqB,CAACnC,IAAI;kBAAC;oBAAA0D,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClB,CAAC,eACbnG,OAAA,CAAC3B,UAAU;oBAACiI,OAAO,EAAC,SAAS;oBAACuB,KAAK,EAAC,gBAAgB;oBAAAlC,QAAA,EACjDV,qBAAqB,CAAC3C,IAAI;kBAAC;oBAAA0D,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV;cACN;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC;YAAA,GA5CG7D,IAAI,CAAC4F,EAAE;cAAAlC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OA6CJ,CACX;UACF;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAGPnG,OAAA,CAAClC,IAAI;QAAC6I,IAAI,EAAE;UAAEC,EAAE,EAAE,EAAE;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAlB,QAAA,eAC5B3F,OAAA,CAACjC,KAAK;UAACqI,EAAE,EAAE;YAAEL,MAAM,EAAE;UAAO,CAAE;UAAAJ,QAAA,EAC3BnF,YAAY,gBACXR,OAAA,CAACF,SAAS;YACR+D,MAAM,EAAErD,YAAY,CAAC0H,EAAG;YACxB5F,IAAI,EAAE9B,YAAa;YACnBqI,OAAO,EAAEA,CAAA,KAAMpI,eAAe,CAAC,IAAI;UAAE;YAAAuF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtC,CAAC,gBAEFnG,OAAA,CAAC1B,GAAG;YACF8H,EAAE,EAAE;cACFL,MAAM,EAAE,MAAM;cACdH,OAAO,EAAE,MAAM;cACfkB,aAAa,EAAE,QAAQ;cACvBhB,UAAU,EAAE,QAAQ;cACpBD,cAAc,EAAE,QAAQ;cACxB4C,GAAG,EAAE;YACP,CAAE;YAAA9C,QAAA,gBAEF3F,OAAA,CAACT,OAAO;cAAC6G,EAAE,EAAE;gBAAEuC,QAAQ,EAAE,EAAE;gBAAEd,KAAK,EAAE;cAAiB;YAAE;cAAA7B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC1DnG,OAAA,CAAC3B,UAAU;cAACiI,OAAO,EAAC,IAAI;cAACuB,KAAK,EAAC,gBAAgB;cAAAlC,QAAA,EAAC;YAEhD;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbnG,OAAA,CAAC3B,UAAU;cAACiI,OAAO,EAAC,OAAO;cAACuB,KAAK,EAAC,gBAAgB;cAAAlC,QAAA,EAAC;YAEnD;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QACN;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGPnG,OAAA,CAACtB,GAAG;MACFmJ,KAAK,EAAC,SAAS;MACf,cAAW,UAAU;MACrBzB,EAAE,EAAE;QAAEqB,QAAQ,EAAE,OAAO;QAAEqB,MAAM,EAAE,EAAE;QAAEC,KAAK,EAAE;MAAG,CAAE;MACjDhB,OAAO,EAAEA,CAAA,KAAMhH,cAAc,CAAC,IAAI,CAAE;MAAA4E,QAAA,eAEpC3F,OAAA,CAACV,GAAG;QAAA0G,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eAGNnG,OAAA,CAACrB,MAAM;MAACqK,IAAI,EAAElI,WAAY;MAAC+H,OAAO,EAAEA,CAAA,KAAM9H,cAAc,CAAC,KAAK,CAAE;MAAC2E,QAAQ,EAAC,IAAI;MAACuB,SAAS;MAAAtB,QAAA,gBACtF3F,OAAA,CAACpB,WAAW;QAAA+G,QAAA,EAAC;MAAsB;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAa,CAAC,eACjDnG,OAAA,CAACnB,aAAa;QAAA8G,QAAA,eACZ3F,OAAA,CAAC1B,GAAG;UAAC8H,EAAE,EAAE;YAAE6C,EAAE,EAAE;UAAE,CAAE;UAAAtD,QAAA,gBACjB3F,OAAA,CAAChB,WAAW;YAACiI,SAAS;YAACb,EAAE,EAAE;cAAE8C,EAAE,EAAE;YAAE,CAAE;YAAAvD,QAAA,gBACnC3F,OAAA,CAACf,UAAU;cAAA0G,QAAA,EAAC;YAAS;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAClCnG,OAAA,CAACd,MAAM;cACLiI,KAAK,EAAEnG,QAAS;cAChBmI,KAAK,EAAC,WAAW;cACjB/B,QAAQ,EAAGC,CAAC,IAAKpG,WAAW,CAACoG,CAAC,CAACC,MAAM,CAACH,KAA4B,CAAE;cAAAxB,QAAA,gBAEpE3F,OAAA,CAACb,QAAQ;gBAACgI,KAAK,EAAC,SAAS;gBAAAxB,QAAA,eACvB3F,OAAA,CAAC1B,GAAG;kBAAC8H,EAAE,EAAE;oBAAER,OAAO,EAAE,MAAM;oBAAEE,UAAU,EAAE,QAAQ;oBAAE2C,GAAG,EAAE;kBAAE,CAAE;kBAAA9C,QAAA,gBACzD3F,OAAA,CAACP,MAAM;oBAAAuG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,gBAEZ;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eACXnG,OAAA,CAACb,QAAQ;gBAACgI,KAAK,EAAC,OAAO;gBAAAxB,QAAA,eACrB3F,OAAA,CAAC1B,GAAG;kBAAC8H,EAAE,EAAE;oBAAER,OAAO,EAAE,MAAM;oBAAEE,UAAU,EAAE,QAAQ;oBAAE2C,GAAG,EAAE;kBAAE,CAAE;kBAAA9C,QAAA,gBACzD3F,OAAA,CAACR,KAAK;oBAAAwG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,cAEX;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,EAEbnF,QAAQ,KAAK,SAAS,gBACrBhB,OAAA,CAACxB,SAAS;YACRyI,SAAS;YACTkC,KAAK,EAAC,YAAY;YAClBhC,KAAK,EAAE7F,WAAW,CAACI,gBAAiB;YACpC0F,QAAQ,EAAGC,CAAC,IAAK9F,cAAc,CAAC;cAAE,GAAGD,WAAW;cAAEI,gBAAgB,EAAE2F,CAAC,CAACC,MAAM,CAACH;YAAM,CAAC,CAAE;YACtFD,WAAW,EAAC;UAAyD;YAAAlB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtE,CAAC,gBAEFnG,OAAA,CAAAE,SAAA;YAAAyF,QAAA,gBACE3F,OAAA,CAACxB,SAAS;cACRyI,SAAS;cACTkC,KAAK,EAAC,YAAY;cAClBhC,KAAK,EAAE7F,WAAW,CAACE,IAAK;cACxB4F,QAAQ,EAAGC,CAAC,IAAK9F,cAAc,CAAC;gBAAE,GAAGD,WAAW;gBAAEE,IAAI,EAAE6F,CAAC,CAACC,MAAM,CAACH;cAAM,CAAC,CAAE;cAC1Ef,EAAE,EAAE;gBAAE8C,EAAE,EAAE;cAAE;YAAE;cAAAlD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACf,CAAC,eACFnG,OAAA,CAACxB,SAAS;cACRyI,SAAS;cACTkC,KAAK,EAAC,wBAAwB;cAC9BC,SAAS;cACTC,IAAI,EAAE,CAAE;cACRlC,KAAK,EAAE7F,WAAW,CAACG,WAAY;cAC/B2F,QAAQ,EAAGC,CAAC,IAAK9F,cAAc,CAAC;gBAAE,GAAGD,WAAW;gBAAEG,WAAW,EAAE4F,CAAC,CAACC,MAAM,CAACH;cAAM,CAAC;YAAE;cAAAnB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClF,CAAC;UAAA,eACF,CACH;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACO,CAAC,eAChBnG,OAAA,CAAClB,aAAa;QAAA6G,QAAA,gBACZ3F,OAAA,CAACjB,MAAM;UAACgJ,OAAO,EAAEA,CAAA,KAAMhH,cAAc,CAAC,KAAK,CAAE;UAAA4E,QAAA,EAAC;QAAM;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAC7DnG,OAAA,CAACjB,MAAM;UACLgJ,OAAO,EAAE/G,QAAQ,KAAK,SAAS,GAAGkC,uBAAuB,GAAGa,qBAAsB;UAClFuC,OAAO,EAAC,WAAW;UACnBgD,QAAQ,EACN5I,OAAO,IACNM,QAAQ,KAAK,SAAS,IAAI,CAACM,WAAW,CAACI,gBAAgB,CAACyB,IAAI,CAAC,CAAE,IAC/DnC,QAAQ,KAAK,OAAO,IAAI,CAACM,WAAW,CAACE,IAAI,CAAC2B,IAAI,CAAC,CACjD;UAAAwC,QAAA,EAEAjF,OAAO,gBAAGV,OAAA,CAACZ,gBAAgB;YAACuH,IAAI,EAAE;UAAG;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,GAAG;QAAY;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEhB,CAAC;AAAC/F,EAAA,CAxXID,IAAc;EAAA,QACDN,OAAO;AAAA;AAAA0J,EAAA,GADpBpJ,IAAc;AA0XpB,eAAeA,IAAI;AAAC,IAAAoJ,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}