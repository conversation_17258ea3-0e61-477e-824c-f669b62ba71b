{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\\\u30C9\\u30AD\\u30E5\\u30E1\\u30F3\\u30C8\\\\Projects\\\\StudyHub\\\\studyhub-app\\\\src\\\\pages\\\\Feed.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Container, Grid, Card, CardContent, CardActions, Typography, Button, TextField, Box, Avatar, IconButton, Fab, Dialog, DialogTitle, DialogContent, DialogActions, Chip, Paper, Divider, CircularProgress, Alert } from '@mui/material';\nimport { Add, Favorite, FavoriteBorder, Comment, Share, MoreVert, Send } from '@mui/icons-material';\nimport { useAuth } from '../contexts/AuthContext';\nimport { PostService } from '../services/postService';\n\n// Post interface is now imported from PostService\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Feed = () => {\n  _s();\n  var _userProfile$firstNam, _userProfile$lastName;\n  const {\n    currentUser,\n    userProfile\n  } = useAuth();\n  const [posts, setPosts] = useState([]);\n  const [newPostContent, setNewPostContent] = useState('');\n  const [createPostOpen, setCreatePostOpen] = useState(false);\n  const [selectedTags, setSelectedTags] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [posting, setPosting] = useState(false);\n\n  // Load posts from Firebase\n  useEffect(() => {\n    setLoading(true);\n    setError(null);\n\n    // Subscribe to real-time posts updates\n    const unsubscribe = PostService.subscribeToPosts(fetchedPosts => {\n      setPosts(fetchedPosts);\n      setLoading(false);\n    });\n\n    // Cleanup subscription on unmount\n    return () => unsubscribe();\n  }, []);\n  const handleCreatePost = async () => {\n    if (!newPostContent.trim() || !currentUser) return;\n    setPosting(true);\n    setError(null);\n    try {\n      const postData = {\n        authorId: currentUser.uid,\n        authorName: `${(userProfile === null || userProfile === void 0 ? void 0 : userProfile.firstName) || ''} ${(userProfile === null || userProfile === void 0 ? void 0 : userProfile.lastName) || ''}`.trim() || 'Anonymous',\n        authorAvatar: (userProfile === null || userProfile === void 0 ? void 0 : userProfile.profilePicture) || '',\n        content: newPostContent.trim(),\n        tags: selectedTags\n      };\n      await PostService.createPost(postData);\n\n      // Clear form\n      setNewPostContent('');\n      setSelectedTags([]);\n      setCreatePostOpen(false);\n    } catch (error) {\n      console.error('Error creating post:', error);\n      setError('Failed to create post. Please try again.');\n    } finally {\n      setPosting(false);\n    }\n  };\n  const handleLike = async postId => {\n    if (!currentUser) return;\n    try {\n      const post = posts.find(p => p.id === postId);\n      if (!post) return;\n      const isLiked = post.likes.includes(currentUser.uid);\n      if (isLiked) {\n        await PostService.unlikePost(postId, currentUser.uid);\n      } else {\n        await PostService.likePost(postId, currentUser.uid);\n      }\n      // The UI will update automatically through the real-time subscription\n    } catch (error) {\n      console.error('Error toggling like:', error);\n      setError('Failed to update like. Please try again.');\n    }\n  };\n  const formatTimeAgo = timestamp => {\n    const now = new Date();\n    const diffInHours = Math.floor((now.getTime() - timestamp.getTime()) / (1000 * 60 * 60));\n    if (diffInHours < 1) return 'Just now';\n    if (diffInHours < 24) return `${diffInHours}h ago`;\n    return `${Math.floor(diffInHours / 24)}d ago`;\n  };\n  const availableTags = ['technology', 'AI', 'climate', 'environment', 'wellness', 'work', 'mentalhealth', 'discussion', 'future', 'innovation'];\n  return /*#__PURE__*/_jsxDEV(Container, {\n    maxWidth: \"md\",\n    children: /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        py: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h4\",\n        gutterBottom: true,\n        children: \"Your Feed\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 128,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"body1\",\n        color: \"text.secondary\",\n        sx: {\n          mb: 4\n        },\n        children: \"Share your thoughts and discover what others are talking about\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 131,\n        columnNumber: 9\n      }, this), error && /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"error\",\n        sx: {\n          mb: 3\n        },\n        onClose: () => setError(null),\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 137,\n        columnNumber: 11\n      }, this), currentUser && /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: 3,\n          mb: 3\n        },\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center',\n            gap: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(Avatar, {\n            src: userProfile === null || userProfile === void 0 ? void 0 : userProfile.profilePicture,\n            children: [userProfile === null || userProfile === void 0 ? void 0 : (_userProfile$firstNam = userProfile.firstName) === null || _userProfile$firstNam === void 0 ? void 0 : _userProfile$firstNam[0], userProfile === null || userProfile === void 0 ? void 0 : (_userProfile$lastName = userProfile.lastName) === null || _userProfile$lastName === void 0 ? void 0 : _userProfile$lastName[0]]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 146,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            fullWidth: true,\n            placeholder: \"What's on your mind?\",\n            variant: \"outlined\",\n            onClick: () => setCreatePostOpen(true),\n            sx: {\n              cursor: 'pointer'\n            },\n            InputProps: {\n              readOnly: true\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 149,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 145,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 144,\n        columnNumber: 11\n      }, this), loading && /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          justifyContent: 'center',\n          py: 4\n        },\n        children: /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 166,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 165,\n        columnNumber: 11\n      }, this), !loading && /*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: 3,\n        children: posts.length === 0 ? /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          children: /*#__PURE__*/_jsxDEV(Paper, {\n            sx: {\n              p: 4,\n              textAlign: 'center'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              color: \"text.secondary\",\n              children: \"No posts yet\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 176,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"text.secondary\",\n              children: \"Be the first to share your thoughts!\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 179,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 175,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 174,\n          columnNumber: 15\n        }, this) : posts.map(post => {\n          const isLiked = currentUser ? post.likes.includes(currentUser.uid) : false;\n          const likesCount = post.likes.length;\n          return /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              children: [/*#__PURE__*/_jsxDEV(CardContent, {\n                children: [/*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    display: 'flex',\n                    alignItems: 'center',\n                    mb: 2\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Avatar, {\n                    src: post.authorAvatar,\n                    sx: {\n                      mr: 2\n                    },\n                    children: post.authorName.split(' ').map(n => n[0]).join('')\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 194,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      flexGrow: 1\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"subtitle1\",\n                      fontWeight: \"bold\",\n                      children: post.authorName\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 198,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"caption\",\n                      color: \"text.secondary\",\n                      children: formatTimeAgo(post.timestamp)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 201,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 197,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n                    children: /*#__PURE__*/_jsxDEV(MoreVert, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 206,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 205,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 193,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body1\",\n                  sx: {\n                    mb: 2\n                  },\n                  children: post.content\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 210,\n                  columnNumber: 25\n                }, this), post.tags.length > 0 && /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    mb: 2\n                  },\n                  children: post.tags.map(tag => /*#__PURE__*/_jsxDEV(Chip, {\n                    label: `#${tag}`,\n                    size: \"small\",\n                    sx: {\n                      mr: 1,\n                      mb: 1\n                    },\n                    color: \"primary\",\n                    variant: \"outlined\"\n                  }, tag, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 217,\n                    columnNumber: 31\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 215,\n                  columnNumber: 27\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 192,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 230,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(CardActions, {\n                sx: {\n                  justifyContent: 'space-between',\n                  px: 2\n                },\n                children: /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    display: 'flex',\n                    gap: 1\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Button, {\n                    startIcon: isLiked ? /*#__PURE__*/_jsxDEV(Favorite, {\n                      color: \"error\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 235,\n                      columnNumber: 50\n                    }, this) : /*#__PURE__*/_jsxDEV(FavoriteBorder, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 235,\n                      columnNumber: 79\n                    }, this),\n                    onClick: () => handleLike(post.id),\n                    color: isLiked ? \"error\" : \"inherit\",\n                    disabled: !currentUser,\n                    children: likesCount\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 234,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(Button, {\n                    startIcon: /*#__PURE__*/_jsxDEV(Comment, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 242,\n                      columnNumber: 46\n                    }, this),\n                    children: post.comments\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 242,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(Button, {\n                    startIcon: /*#__PURE__*/_jsxDEV(Share, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 245,\n                      columnNumber: 46\n                    }, this),\n                    children: \"Share\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 245,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 233,\n                  columnNumber: 25\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 232,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 191,\n              columnNumber: 21\n            }, this)\n          }, post.id, false, {\n            fileName: _jsxFileName,\n            lineNumber: 190,\n            columnNumber: 19\n          }, this);\n        })\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 172,\n        columnNumber: 11\n      }, this), currentUser && /*#__PURE__*/_jsxDEV(Fab, {\n        color: \"primary\",\n        \"aria-label\": \"create post\",\n        sx: {\n          position: 'fixed',\n          bottom: 16,\n          right: 16\n        },\n        onClick: () => setCreatePostOpen(true),\n        children: /*#__PURE__*/_jsxDEV(Add, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 266,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 260,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n        open: createPostOpen,\n        onClose: () => setCreatePostOpen(false),\n        maxWidth: \"sm\",\n        fullWidth: true,\n        children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n          children: \"Create New Post\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 272,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n          children: [/*#__PURE__*/_jsxDEV(TextField, {\n            autoFocus: true,\n            margin: \"dense\",\n            label: \"What's on your mind?\",\n            fullWidth: true,\n            multiline: true,\n            rows: 4,\n            variant: \"outlined\",\n            value: newPostContent,\n            onChange: e => setNewPostContent(e.target.value),\n            sx: {\n              mb: 2\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 274,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle2\",\n            gutterBottom: true,\n            children: \"Add Tags:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 287,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              flexWrap: 'wrap',\n              gap: 1,\n              mb: 2\n            },\n            children: availableTags.map(tag => /*#__PURE__*/_jsxDEV(Chip, {\n              label: `#${tag}`,\n              clickable: true,\n              color: selectedTags.includes(tag) ? \"primary\" : \"default\",\n              onClick: () => {\n                setSelectedTags(prev => prev.includes(tag) ? prev.filter(t => t !== tag) : [...prev, tag]);\n              }\n            }, tag, false, {\n              fileName: _jsxFileName,\n              lineNumber: 292,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 290,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 273,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            onClick: () => setCreatePostOpen(false),\n            children: \"Cancel\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 309,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleCreatePost,\n            variant: \"contained\",\n            disabled: !newPostContent.trim() || posting,\n            startIcon: posting ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n              size: 20\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 314,\n              columnNumber: 36\n            }, this) : /*#__PURE__*/_jsxDEV(Send, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 314,\n              columnNumber: 69\n            }, this),\n            children: posting ? 'Posting...' : 'Post'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 310,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 308,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 271,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 127,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 126,\n    columnNumber: 5\n  }, this);\n};\n_s(Feed, \"fyV8SW+VqMXs1VmIhI9+eho/bGI=\", false, function () {\n  return [useAuth];\n});\n_c = Feed;\nexport default Feed;\nvar _c;\n$RefreshReg$(_c, \"Feed\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Container", "Grid", "Card", "<PERSON><PERSON><PERSON><PERSON>", "CardActions", "Typography", "<PERSON><PERSON>", "TextField", "Box", "Avatar", "IconButton", "Fab", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "Chip", "Paper", "Divider", "CircularProgress", "<PERSON><PERSON>", "Add", "Favorite", "FavoriteBorder", "Comment", "Share", "<PERSON><PERSON><PERSON>", "Send", "useAuth", "PostService", "jsxDEV", "_jsxDEV", "Feed", "_s", "_userProfile$firstNam", "_userProfile$lastName", "currentUser", "userProfile", "posts", "setPosts", "newPostContent", "set<PERSON>ew<PERSON>ost<PERSON><PERSON>nt", "createPostOpen", "setCreatePostOpen", "selectedTags", "setSelectedTags", "loading", "setLoading", "error", "setError", "posting", "setPosting", "unsubscribe", "subscribeToPosts", "fetchedPosts", "handleCreatePost", "trim", "postData", "authorId", "uid", "<PERSON><PERSON><PERSON>", "firstName", "lastName", "<PERSON><PERSON><PERSON><PERSON>", "profilePicture", "content", "tags", "createPost", "console", "handleLike", "postId", "post", "find", "p", "id", "isLiked", "likes", "includes", "unlikePost", "likePost", "formatTimeAgo", "timestamp", "now", "Date", "diffInHours", "Math", "floor", "getTime", "availableTags", "max<PERSON><PERSON><PERSON>", "children", "sx", "py", "variant", "gutterBottom", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "color", "mb", "severity", "onClose", "display", "alignItems", "gap", "src", "fullWidth", "placeholder", "onClick", "cursor", "InputProps", "readOnly", "justifyContent", "container", "spacing", "length", "item", "xs", "textAlign", "map", "likesCount", "mr", "split", "n", "join", "flexGrow", "fontWeight", "tag", "label", "size", "px", "startIcon", "disabled", "comments", "position", "bottom", "right", "open", "autoFocus", "margin", "multiline", "rows", "value", "onChange", "e", "target", "flexWrap", "clickable", "prev", "filter", "t", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/ドキュメント/Projects/StudyHub/studyhub-app/src/pages/Feed.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  <PERSON><PERSON><PERSON>,\n  Grid,\n  Card,\n  CardContent,\n  CardActions,\n  Typo<PERSON>,\n  Button,\n  TextField,\n  Box,\n  Avatar,\n  IconButton,\n  Fab,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  Chip,\n  Paper,\n  Divider,\n  CircularProgress,\n  Alert,\n} from '@mui/material';\nimport {\n  Add,\n  Favorite,\n  FavoriteBorder,\n  Comment,\n  Share,\n  MoreVert,\n  Send,\n} from '@mui/icons-material';\nimport { useAuth } from '../contexts/AuthContext';\nimport { PostService, Post } from '../services/postService';\n\n// Post interface is now imported from PostService\n\nconst Feed: React.FC = () => {\n  const { currentUser, userProfile } = useAuth();\n  const [posts, setPosts] = useState<Post[]>([]);\n  const [newPostContent, setNewPostContent] = useState('');\n  const [createPostOpen, setCreatePostOpen] = useState(false);\n  const [selectedTags, setSelectedTags] = useState<string[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n  const [posting, setPosting] = useState(false);\n\n  // Load posts from Firebase\n  useEffect(() => {\n    setLoading(true);\n    setError(null);\n\n    // Subscribe to real-time posts updates\n    const unsubscribe = PostService.subscribeToPosts((fetchedPosts) => {\n      setPosts(fetchedPosts);\n      setLoading(false);\n    });\n\n    // Cleanup subscription on unmount\n    return () => unsubscribe();\n  }, []);\n\n  const handleCreatePost = async () => {\n    if (!newPostContent.trim() || !currentUser) return;\n\n    setPosting(true);\n    setError(null);\n\n    try {\n      const postData = {\n        authorId: currentUser.uid,\n        authorName: `${userProfile?.firstName || ''} ${userProfile?.lastName || ''}`.trim() || 'Anonymous',\n        authorAvatar: userProfile?.profilePicture || '',\n        content: newPostContent.trim(),\n        tags: selectedTags,\n      };\n\n      await PostService.createPost(postData);\n\n      // Clear form\n      setNewPostContent('');\n      setSelectedTags([]);\n      setCreatePostOpen(false);\n    } catch (error) {\n      console.error('Error creating post:', error);\n      setError('Failed to create post. Please try again.');\n    } finally {\n      setPosting(false);\n    }\n  };\n\n  const handleLike = async (postId: string) => {\n    if (!currentUser) return;\n\n    try {\n      const post = posts.find(p => p.id === postId);\n      if (!post) return;\n\n      const isLiked = post.likes.includes(currentUser.uid);\n\n      if (isLiked) {\n        await PostService.unlikePost(postId, currentUser.uid);\n      } else {\n        await PostService.likePost(postId, currentUser.uid);\n      }\n      // The UI will update automatically through the real-time subscription\n    } catch (error) {\n      console.error('Error toggling like:', error);\n      setError('Failed to update like. Please try again.');\n    }\n  };\n\n  const formatTimeAgo = (timestamp: Date) => {\n    const now = new Date();\n    const diffInHours = Math.floor((now.getTime() - timestamp.getTime()) / (1000 * 60 * 60));\n    \n    if (diffInHours < 1) return 'Just now';\n    if (diffInHours < 24) return `${diffInHours}h ago`;\n    return `${Math.floor(diffInHours / 24)}d ago`;\n  };\n\n  const availableTags = ['technology', 'AI', 'climate', 'environment', 'wellness', 'work', 'mentalhealth', 'discussion', 'future', 'innovation'];\n\n  return (\n    <Container maxWidth=\"md\">\n      <Box sx={{ py: 3 }}>\n        <Typography variant=\"h4\" gutterBottom>\n          Your Feed\n        </Typography>\n        <Typography variant=\"body1\" color=\"text.secondary\" sx={{ mb: 4 }}>\n          Share your thoughts and discover what others are talking about\n        </Typography>\n\n        {/* Error Alert */}\n        {error && (\n          <Alert severity=\"error\" sx={{ mb: 3 }} onClose={() => setError(null)}>\n            {error}\n          </Alert>\n        )}\n\n        {/* Create Post Section */}\n        {currentUser && (\n          <Paper sx={{ p: 3, mb: 3 }}>\n            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>\n              <Avatar src={userProfile?.profilePicture}>\n                {userProfile?.firstName?.[0]}{userProfile?.lastName?.[0]}\n              </Avatar>\n              <TextField\n                fullWidth\n                placeholder=\"What's on your mind?\"\n                variant=\"outlined\"\n                onClick={() => setCreatePostOpen(true)}\n                sx={{ cursor: 'pointer' }}\n                InputProps={{\n                  readOnly: true,\n                }}\n              />\n            </Box>\n          </Paper>\n        )}\n\n        {/* Loading State */}\n        {loading && (\n          <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>\n            <CircularProgress />\n          </Box>\n        )}\n\n        {/* Posts */}\n        {!loading && (\n          <Grid container spacing={3}>\n            {posts.length === 0 ? (\n              <Grid item xs={12}>\n                <Paper sx={{ p: 4, textAlign: 'center' }}>\n                  <Typography variant=\"h6\" color=\"text.secondary\">\n                    No posts yet\n                  </Typography>\n                  <Typography variant=\"body2\" color=\"text.secondary\">\n                    Be the first to share your thoughts!\n                  </Typography>\n                </Paper>\n              </Grid>\n            ) : (\n              posts.map((post) => {\n                const isLiked = currentUser ? post.likes.includes(currentUser.uid) : false;\n                const likesCount = post.likes.length;\n\n                return (\n                  <Grid item xs={12} key={post.id}>\n                    <Card>\n                      <CardContent>\n                        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>\n                          <Avatar src={post.authorAvatar} sx={{ mr: 2 }}>\n                            {post.authorName.split(' ').map(n => n[0]).join('')}\n                          </Avatar>\n                          <Box sx={{ flexGrow: 1 }}>\n                            <Typography variant=\"subtitle1\" fontWeight=\"bold\">\n                              {post.authorName}\n                            </Typography>\n                            <Typography variant=\"caption\" color=\"text.secondary\">\n                              {formatTimeAgo(post.timestamp)}\n                            </Typography>\n                          </Box>\n                          <IconButton>\n                            <MoreVert />\n                          </IconButton>\n                        </Box>\n\n                        <Typography variant=\"body1\" sx={{ mb: 2 }}>\n                          {post.content}\n                        </Typography>\n\n                        {post.tags.length > 0 && (\n                          <Box sx={{ mb: 2 }}>\n                            {post.tags.map((tag) => (\n                              <Chip\n                                key={tag}\n                                label={`#${tag}`}\n                                size=\"small\"\n                                sx={{ mr: 1, mb: 1 }}\n                                color=\"primary\"\n                                variant=\"outlined\"\n                              />\n                            ))}\n                          </Box>\n                        )}\n                      </CardContent>\n\n                      <Divider />\n\n                      <CardActions sx={{ justifyContent: 'space-between', px: 2 }}>\n                        <Box sx={{ display: 'flex', gap: 1 }}>\n                          <Button\n                            startIcon={isLiked ? <Favorite color=\"error\" /> : <FavoriteBorder />}\n                            onClick={() => handleLike(post.id)}\n                            color={isLiked ? \"error\" : \"inherit\"}\n                            disabled={!currentUser}\n                          >\n                            {likesCount}\n                          </Button>\n                          <Button startIcon={<Comment />}>\n                            {post.comments}\n                          </Button>\n                          <Button startIcon={<Share />}>\n                            Share\n                          </Button>\n                        </Box>\n                      </CardActions>\n                    </Card>\n                  </Grid>\n                );\n              })\n            )}\n          </Grid>\n        )}\n\n        {/* Floating Action Button */}\n        {currentUser && (\n          <Fab\n            color=\"primary\"\n            aria-label=\"create post\"\n            sx={{ position: 'fixed', bottom: 16, right: 16 }}\n            onClick={() => setCreatePostOpen(true)}\n          >\n            <Add />\n          </Fab>\n        )}\n\n        {/* Create Post Dialog */}\n        <Dialog open={createPostOpen} onClose={() => setCreatePostOpen(false)} maxWidth=\"sm\" fullWidth>\n          <DialogTitle>Create New Post</DialogTitle>\n          <DialogContent>\n            <TextField\n              autoFocus\n              margin=\"dense\"\n              label=\"What's on your mind?\"\n              fullWidth\n              multiline\n              rows={4}\n              variant=\"outlined\"\n              value={newPostContent}\n              onChange={(e) => setNewPostContent(e.target.value)}\n              sx={{ mb: 2 }}\n            />\n            \n            <Typography variant=\"subtitle2\" gutterBottom>\n              Add Tags:\n            </Typography>\n            <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mb: 2 }}>\n              {availableTags.map((tag) => (\n                <Chip\n                  key={tag}\n                  label={`#${tag}`}\n                  clickable\n                  color={selectedTags.includes(tag) ? \"primary\" : \"default\"}\n                  onClick={() => {\n                    setSelectedTags(prev => \n                      prev.includes(tag) \n                        ? prev.filter(t => t !== tag)\n                        : [...prev, tag]\n                    );\n                  }}\n                />\n              ))}\n            </Box>\n          </DialogContent>\n          <DialogActions>\n            <Button onClick={() => setCreatePostOpen(false)}>Cancel</Button>\n            <Button\n              onClick={handleCreatePost}\n              variant=\"contained\"\n              disabled={!newPostContent.trim() || posting}\n              startIcon={posting ? <CircularProgress size={20} /> : <Send />}\n            >\n              {posting ? 'Posting...' : 'Post'}\n            </Button>\n          </DialogActions>\n        </Dialog>\n      </Box>\n    </Container>\n  );\n};\n\nexport default Feed;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,SAAS,EACTC,IAAI,EACJC,IAAI,EACJC,WAAW,EACXC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,SAAS,EACTC,GAAG,EACHC,MAAM,EACNC,UAAU,EACVC,GAAG,EACHC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,IAAI,EACJC,KAAK,EACLC,OAAO,EACPC,gBAAgB,EAChBC,KAAK,QACA,eAAe;AACtB,SACEC,GAAG,EACHC,QAAQ,EACRC,cAAc,EACdC,OAAO,EACPC,KAAK,EACLC,QAAQ,EACRC,IAAI,QACC,qBAAqB;AAC5B,SAASC,OAAO,QAAQ,yBAAyB;AACjD,SAASC,WAAW,QAAc,yBAAyB;;AAE3D;AAAA,SAAAC,MAAA,IAAAC,OAAA;AAEA,MAAMC,IAAc,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,qBAAA,EAAAC,qBAAA;EAC3B,MAAM;IAAEC,WAAW;IAAEC;EAAY,CAAC,GAAGT,OAAO,CAAC,CAAC;EAC9C,MAAM,CAACU,KAAK,EAAEC,QAAQ,CAAC,GAAGzC,QAAQ,CAAS,EAAE,CAAC;EAC9C,MAAM,CAAC0C,cAAc,EAAEC,iBAAiB,CAAC,GAAG3C,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAAC4C,cAAc,EAAEC,iBAAiB,CAAC,GAAG7C,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAAC8C,YAAY,EAAEC,eAAe,CAAC,GAAG/C,QAAQ,CAAW,EAAE,CAAC;EAC9D,MAAM,CAACgD,OAAO,EAAEC,UAAU,CAAC,GAAGjD,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACkD,KAAK,EAAEC,QAAQ,CAAC,GAAGnD,QAAQ,CAAgB,IAAI,CAAC;EACvD,MAAM,CAACoD,OAAO,EAAEC,UAAU,CAAC,GAAGrD,QAAQ,CAAC,KAAK,CAAC;;EAE7C;EACAC,SAAS,CAAC,MAAM;IACdgD,UAAU,CAAC,IAAI,CAAC;IAChBE,QAAQ,CAAC,IAAI,CAAC;;IAEd;IACA,MAAMG,WAAW,GAAGvB,WAAW,CAACwB,gBAAgB,CAAEC,YAAY,IAAK;MACjEf,QAAQ,CAACe,YAAY,CAAC;MACtBP,UAAU,CAAC,KAAK,CAAC;IACnB,CAAC,CAAC;;IAEF;IACA,OAAO,MAAMK,WAAW,CAAC,CAAC;EAC5B,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMG,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI,CAACf,cAAc,CAACgB,IAAI,CAAC,CAAC,IAAI,CAACpB,WAAW,EAAE;IAE5Ce,UAAU,CAAC,IAAI,CAAC;IAChBF,QAAQ,CAAC,IAAI,CAAC;IAEd,IAAI;MACF,MAAMQ,QAAQ,GAAG;QACfC,QAAQ,EAAEtB,WAAW,CAACuB,GAAG;QACzBC,UAAU,EAAE,GAAG,CAAAvB,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEwB,SAAS,KAAI,EAAE,IAAI,CAAAxB,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEyB,QAAQ,KAAI,EAAE,EAAE,CAACN,IAAI,CAAC,CAAC,IAAI,WAAW;QAClGO,YAAY,EAAE,CAAA1B,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAE2B,cAAc,KAAI,EAAE;QAC/CC,OAAO,EAAEzB,cAAc,CAACgB,IAAI,CAAC,CAAC;QAC9BU,IAAI,EAAEtB;MACR,CAAC;MAED,MAAMf,WAAW,CAACsC,UAAU,CAACV,QAAQ,CAAC;;MAEtC;MACAhB,iBAAiB,CAAC,EAAE,CAAC;MACrBI,eAAe,CAAC,EAAE,CAAC;MACnBF,iBAAiB,CAAC,KAAK,CAAC;IAC1B,CAAC,CAAC,OAAOK,KAAK,EAAE;MACdoB,OAAO,CAACpB,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC5CC,QAAQ,CAAC,0CAA0C,CAAC;IACtD,CAAC,SAAS;MACRE,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMkB,UAAU,GAAG,MAAOC,MAAc,IAAK;IAC3C,IAAI,CAAClC,WAAW,EAAE;IAElB,IAAI;MACF,MAAMmC,IAAI,GAAGjC,KAAK,CAACkC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,EAAE,KAAKJ,MAAM,CAAC;MAC7C,IAAI,CAACC,IAAI,EAAE;MAEX,MAAMI,OAAO,GAAGJ,IAAI,CAACK,KAAK,CAACC,QAAQ,CAACzC,WAAW,CAACuB,GAAG,CAAC;MAEpD,IAAIgB,OAAO,EAAE;QACX,MAAM9C,WAAW,CAACiD,UAAU,CAACR,MAAM,EAAElC,WAAW,CAACuB,GAAG,CAAC;MACvD,CAAC,MAAM;QACL,MAAM9B,WAAW,CAACkD,QAAQ,CAACT,MAAM,EAAElC,WAAW,CAACuB,GAAG,CAAC;MACrD;MACA;IACF,CAAC,CAAC,OAAOX,KAAK,EAAE;MACdoB,OAAO,CAACpB,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC5CC,QAAQ,CAAC,0CAA0C,CAAC;IACtD;EACF,CAAC;EAED,MAAM+B,aAAa,GAAIC,SAAe,IAAK;IACzC,MAAMC,GAAG,GAAG,IAAIC,IAAI,CAAC,CAAC;IACtB,MAAMC,WAAW,GAAGC,IAAI,CAACC,KAAK,CAAC,CAACJ,GAAG,CAACK,OAAO,CAAC,CAAC,GAAGN,SAAS,CAACM,OAAO,CAAC,CAAC,KAAK,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;IAExF,IAAIH,WAAW,GAAG,CAAC,EAAE,OAAO,UAAU;IACtC,IAAIA,WAAW,GAAG,EAAE,EAAE,OAAO,GAAGA,WAAW,OAAO;IAClD,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACF,WAAW,GAAG,EAAE,CAAC,OAAO;EAC/C,CAAC;EAED,MAAMI,aAAa,GAAG,CAAC,YAAY,EAAE,IAAI,EAAE,SAAS,EAAE,aAAa,EAAE,UAAU,EAAE,MAAM,EAAE,cAAc,EAAE,YAAY,EAAE,QAAQ,EAAE,YAAY,CAAC;EAE9I,oBACEzD,OAAA,CAAC/B,SAAS;IAACyF,QAAQ,EAAC,IAAI;IAAAC,QAAA,eACtB3D,OAAA,CAACvB,GAAG;MAACmF,EAAE,EAAE;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAF,QAAA,gBACjB3D,OAAA,CAAC1B,UAAU;QAACwF,OAAO,EAAC,IAAI;QAACC,YAAY;QAAAJ,QAAA,EAAC;MAEtC;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACbnE,OAAA,CAAC1B,UAAU;QAACwF,OAAO,EAAC,OAAO;QAACM,KAAK,EAAC,gBAAgB;QAACR,EAAE,EAAE;UAAES,EAAE,EAAE;QAAE,CAAE;QAAAV,QAAA,EAAC;MAElE;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,EAGZlD,KAAK,iBACJjB,OAAA,CAACX,KAAK;QAACiF,QAAQ,EAAC,OAAO;QAACV,EAAE,EAAE;UAAES,EAAE,EAAE;QAAE,CAAE;QAACE,OAAO,EAAEA,CAAA,KAAMrD,QAAQ,CAAC,IAAI,CAAE;QAAAyC,QAAA,EAClE1C;MAAK;QAAA+C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CACR,EAGA9D,WAAW,iBACVL,OAAA,CAACd,KAAK;QAAC0E,EAAE,EAAE;UAAElB,CAAC,EAAE,CAAC;UAAE2B,EAAE,EAAE;QAAE,CAAE;QAAAV,QAAA,eACzB3D,OAAA,CAACvB,GAAG;UAACmF,EAAE,EAAE;YAAEY,OAAO,EAAE,MAAM;YAAEC,UAAU,EAAE,QAAQ;YAAEC,GAAG,EAAE;UAAE,CAAE;UAAAf,QAAA,gBACzD3D,OAAA,CAACtB,MAAM;YAACiG,GAAG,EAAErE,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAE2B,cAAe;YAAA0B,QAAA,GACtCrD,WAAW,aAAXA,WAAW,wBAAAH,qBAAA,GAAXG,WAAW,CAAEwB,SAAS,cAAA3B,qBAAA,uBAAtBA,qBAAA,CAAyB,CAAC,CAAC,EAAEG,WAAW,aAAXA,WAAW,wBAAAF,qBAAA,GAAXE,WAAW,CAAEyB,QAAQ,cAAA3B,qBAAA,uBAArBA,qBAAA,CAAwB,CAAC,CAAC;UAAA;YAAA4D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClD,CAAC,eACTnE,OAAA,CAACxB,SAAS;YACRoG,SAAS;YACTC,WAAW,EAAC,sBAAsB;YAClCf,OAAO,EAAC,UAAU;YAClBgB,OAAO,EAAEA,CAAA,KAAMlE,iBAAiB,CAAC,IAAI,CAAE;YACvCgD,EAAE,EAAE;cAAEmB,MAAM,EAAE;YAAU,CAAE;YAC1BC,UAAU,EAAE;cACVC,QAAQ,EAAE;YACZ;UAAE;YAAAjB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CACR,EAGApD,OAAO,iBACNf,OAAA,CAACvB,GAAG;QAACmF,EAAE,EAAE;UAAEY,OAAO,EAAE,MAAM;UAAEU,cAAc,EAAE,QAAQ;UAAErB,EAAE,EAAE;QAAE,CAAE;QAAAF,QAAA,eAC5D3D,OAAA,CAACZ,gBAAgB;UAAA4E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjB,CACN,EAGA,CAACpD,OAAO,iBACPf,OAAA,CAAC9B,IAAI;QAACiH,SAAS;QAACC,OAAO,EAAE,CAAE;QAAAzB,QAAA,EACxBpD,KAAK,CAAC8E,MAAM,KAAK,CAAC,gBACjBrF,OAAA,CAAC9B,IAAI;UAACoH,IAAI;UAACC,EAAE,EAAE,EAAG;UAAA5B,QAAA,eAChB3D,OAAA,CAACd,KAAK;YAAC0E,EAAE,EAAE;cAAElB,CAAC,EAAE,CAAC;cAAE8C,SAAS,EAAE;YAAS,CAAE;YAAA7B,QAAA,gBACvC3D,OAAA,CAAC1B,UAAU;cAACwF,OAAO,EAAC,IAAI;cAACM,KAAK,EAAC,gBAAgB;cAAAT,QAAA,EAAC;YAEhD;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbnE,OAAA,CAAC1B,UAAU;cAACwF,OAAO,EAAC,OAAO;cAACM,KAAK,EAAC,gBAAgB;cAAAT,QAAA,EAAC;YAEnD;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,GAEP5D,KAAK,CAACkF,GAAG,CAAEjD,IAAI,IAAK;UAClB,MAAMI,OAAO,GAAGvC,WAAW,GAAGmC,IAAI,CAACK,KAAK,CAACC,QAAQ,CAACzC,WAAW,CAACuB,GAAG,CAAC,GAAG,KAAK;UAC1E,MAAM8D,UAAU,GAAGlD,IAAI,CAACK,KAAK,CAACwC,MAAM;UAEpC,oBACErF,OAAA,CAAC9B,IAAI;YAACoH,IAAI;YAACC,EAAE,EAAE,EAAG;YAAA5B,QAAA,eAChB3D,OAAA,CAAC7B,IAAI;cAAAwF,QAAA,gBACH3D,OAAA,CAAC5B,WAAW;gBAAAuF,QAAA,gBACV3D,OAAA,CAACvB,GAAG;kBAACmF,EAAE,EAAE;oBAAEY,OAAO,EAAE,MAAM;oBAAEC,UAAU,EAAE,QAAQ;oBAAEJ,EAAE,EAAE;kBAAE,CAAE;kBAAAV,QAAA,gBACxD3D,OAAA,CAACtB,MAAM;oBAACiG,GAAG,EAAEnC,IAAI,CAACR,YAAa;oBAAC4B,EAAE,EAAE;sBAAE+B,EAAE,EAAE;oBAAE,CAAE;oBAAAhC,QAAA,EAC3CnB,IAAI,CAACX,UAAU,CAAC+D,KAAK,CAAC,GAAG,CAAC,CAACH,GAAG,CAACI,CAAC,IAAIA,CAAC,CAAC,CAAC,CAAC,CAAC,CAACC,IAAI,CAAC,EAAE;kBAAC;oBAAA9B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7C,CAAC,eACTnE,OAAA,CAACvB,GAAG;oBAACmF,EAAE,EAAE;sBAAEmC,QAAQ,EAAE;oBAAE,CAAE;oBAAApC,QAAA,gBACvB3D,OAAA,CAAC1B,UAAU;sBAACwF,OAAO,EAAC,WAAW;sBAACkC,UAAU,EAAC,MAAM;sBAAArC,QAAA,EAC9CnB,IAAI,CAACX;oBAAU;sBAAAmC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACN,CAAC,eACbnE,OAAA,CAAC1B,UAAU;sBAACwF,OAAO,EAAC,SAAS;sBAACM,KAAK,EAAC,gBAAgB;sBAAAT,QAAA,EACjDV,aAAa,CAACT,IAAI,CAACU,SAAS;oBAAC;sBAAAc,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACpB,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV,CAAC,eACNnE,OAAA,CAACrB,UAAU;oBAAAgF,QAAA,eACT3D,OAAA,CAACL,QAAQ;sBAAAqE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV,CAAC,eAENnE,OAAA,CAAC1B,UAAU;kBAACwF,OAAO,EAAC,OAAO;kBAACF,EAAE,EAAE;oBAAES,EAAE,EAAE;kBAAE,CAAE;kBAAAV,QAAA,EACvCnB,IAAI,CAACN;gBAAO;kBAAA8B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,EAEZ3B,IAAI,CAACL,IAAI,CAACkD,MAAM,GAAG,CAAC,iBACnBrF,OAAA,CAACvB,GAAG;kBAACmF,EAAE,EAAE;oBAAES,EAAE,EAAE;kBAAE,CAAE;kBAAAV,QAAA,EAChBnB,IAAI,CAACL,IAAI,CAACsD,GAAG,CAAEQ,GAAG,iBACjBjG,OAAA,CAACf,IAAI;oBAEHiH,KAAK,EAAE,IAAID,GAAG,EAAG;oBACjBE,IAAI,EAAC,OAAO;oBACZvC,EAAE,EAAE;sBAAE+B,EAAE,EAAE,CAAC;sBAAEtB,EAAE,EAAE;oBAAE,CAAE;oBACrBD,KAAK,EAAC,SAAS;oBACfN,OAAO,EAAC;kBAAU,GALbmC,GAAG;oBAAAjC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAMT,CACF;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACU,CAAC,eAEdnE,OAAA,CAACb,OAAO;gBAAA6E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAEXnE,OAAA,CAAC3B,WAAW;gBAACuF,EAAE,EAAE;kBAAEsB,cAAc,EAAE,eAAe;kBAAEkB,EAAE,EAAE;gBAAE,CAAE;gBAAAzC,QAAA,eAC1D3D,OAAA,CAACvB,GAAG;kBAACmF,EAAE,EAAE;oBAAEY,OAAO,EAAE,MAAM;oBAAEE,GAAG,EAAE;kBAAE,CAAE;kBAAAf,QAAA,gBACnC3D,OAAA,CAACzB,MAAM;oBACL8H,SAAS,EAAEzD,OAAO,gBAAG5C,OAAA,CAACT,QAAQ;sBAAC6E,KAAK,EAAC;oBAAO;sBAAAJ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,gBAAGnE,OAAA,CAACR,cAAc;sBAAAwE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAE;oBACrEW,OAAO,EAAEA,CAAA,KAAMxC,UAAU,CAACE,IAAI,CAACG,EAAE,CAAE;oBACnCyB,KAAK,EAAExB,OAAO,GAAG,OAAO,GAAG,SAAU;oBACrC0D,QAAQ,EAAE,CAACjG,WAAY;oBAAAsD,QAAA,EAEtB+B;kBAAU;oBAAA1B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL,CAAC,eACTnE,OAAA,CAACzB,MAAM;oBAAC8H,SAAS,eAAErG,OAAA,CAACP,OAAO;sBAAAuE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAE;oBAAAR,QAAA,EAC5BnB,IAAI,CAAC+D;kBAAQ;oBAAAvC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACR,CAAC,eACTnE,OAAA,CAACzB,MAAM;oBAAC8H,SAAS,eAAErG,OAAA,CAACN,KAAK;sBAAAsE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAE;oBAAAR,QAAA,EAAC;kBAE9B;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC,GA5De3B,IAAI,CAACG,EAAE;YAAAqB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OA6DzB,CAAC;QAEX,CAAC;MACF;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CACP,EAGA9D,WAAW,iBACVL,OAAA,CAACpB,GAAG;QACFwF,KAAK,EAAC,SAAS;QACf,cAAW,aAAa;QACxBR,EAAE,EAAE;UAAE4C,QAAQ,EAAE,OAAO;UAAEC,MAAM,EAAE,EAAE;UAAEC,KAAK,EAAE;QAAG,CAAE;QACjD5B,OAAO,EAAEA,CAAA,KAAMlE,iBAAiB,CAAC,IAAI,CAAE;QAAA+C,QAAA,eAEvC3D,OAAA,CAACV,GAAG;UAAA0E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CACN,eAGDnE,OAAA,CAACnB,MAAM;QAAC8H,IAAI,EAAEhG,cAAe;QAAC4D,OAAO,EAAEA,CAAA,KAAM3D,iBAAiB,CAAC,KAAK,CAAE;QAAC8C,QAAQ,EAAC,IAAI;QAACkB,SAAS;QAAAjB,QAAA,gBAC5F3D,OAAA,CAAClB,WAAW;UAAA6E,QAAA,EAAC;QAAe;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa,CAAC,eAC1CnE,OAAA,CAACjB,aAAa;UAAA4E,QAAA,gBACZ3D,OAAA,CAACxB,SAAS;YACRoI,SAAS;YACTC,MAAM,EAAC,OAAO;YACdX,KAAK,EAAC,sBAAsB;YAC5BtB,SAAS;YACTkC,SAAS;YACTC,IAAI,EAAE,CAAE;YACRjD,OAAO,EAAC,UAAU;YAClBkD,KAAK,EAAEvG,cAAe;YACtBwG,QAAQ,EAAGC,CAAC,IAAKxG,iBAAiB,CAACwG,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;YACnDpD,EAAE,EAAE;cAAES,EAAE,EAAE;YAAE;UAAE;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACf,CAAC,eAEFnE,OAAA,CAAC1B,UAAU;YAACwF,OAAO,EAAC,WAAW;YAACC,YAAY;YAAAJ,QAAA,EAAC;UAE7C;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbnE,OAAA,CAACvB,GAAG;YAACmF,EAAE,EAAE;cAAEY,OAAO,EAAE,MAAM;cAAE4C,QAAQ,EAAE,MAAM;cAAE1C,GAAG,EAAE,CAAC;cAAEL,EAAE,EAAE;YAAE,CAAE;YAAAV,QAAA,EAC3DF,aAAa,CAACgC,GAAG,CAAEQ,GAAG,iBACrBjG,OAAA,CAACf,IAAI;cAEHiH,KAAK,EAAE,IAAID,GAAG,EAAG;cACjBoB,SAAS;cACTjD,KAAK,EAAEvD,YAAY,CAACiC,QAAQ,CAACmD,GAAG,CAAC,GAAG,SAAS,GAAG,SAAU;cAC1DnB,OAAO,EAAEA,CAAA,KAAM;gBACbhE,eAAe,CAACwG,IAAI,IAClBA,IAAI,CAACxE,QAAQ,CAACmD,GAAG,CAAC,GACdqB,IAAI,CAACC,MAAM,CAACC,CAAC,IAAIA,CAAC,KAAKvB,GAAG,CAAC,GAC3B,CAAC,GAAGqB,IAAI,EAAErB,GAAG,CACnB,CAAC;cACH;YAAE,GAVGA,GAAG;cAAAjC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAWT,CACF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC,eAChBnE,OAAA,CAAChB,aAAa;UAAA2E,QAAA,gBACZ3D,OAAA,CAACzB,MAAM;YAACuG,OAAO,EAAEA,CAAA,KAAMlE,iBAAiB,CAAC,KAAK,CAAE;YAAA+C,QAAA,EAAC;UAAM;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAChEnE,OAAA,CAACzB,MAAM;YACLuG,OAAO,EAAEtD,gBAAiB;YAC1BsC,OAAO,EAAC,WAAW;YACnBwC,QAAQ,EAAE,CAAC7F,cAAc,CAACgB,IAAI,CAAC,CAAC,IAAIN,OAAQ;YAC5CkF,SAAS,EAAElF,OAAO,gBAAGnB,OAAA,CAACZ,gBAAgB;cAAC+G,IAAI,EAAE;YAAG;cAAAnC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAAGnE,OAAA,CAACJ,IAAI;cAAAoE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAAAR,QAAA,EAE9DxC,OAAO,GAAG,YAAY,GAAG;UAAM;YAAA6C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACG,CAAC;AAEhB,CAAC;AAACjE,EAAA,CA5RID,IAAc;EAAA,QACmBJ,OAAO;AAAA;AAAA4H,EAAA,GADxCxH,IAAc;AA8RpB,eAAeA,IAAI;AAAC,IAAAwH,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}