# Firebase Setup Guide for StudyHub

## Current Status
✅ Firebase project created: `studyhub-7900c`
✅ Firebase configuration added to app
✅ Firebase SDK installed and configured
❓ Authentication setup needs verification

## Required Firebase Console Setup

### 1. Enable Authentication
1. Go to [Firebase Console](https://console.firebase.google.com/)
2. Select your project: `studyhub-7900c`
3. Navigate to **Authentication** → **Sign-in method**
4. Enable **Email/Password** provider
5. Click **Save**

### 2. Configure Authorized Domains
1. In Authentication → **Settings** → **Authorized domains**
2. Ensure these domains are listed:
   - `localhost` (for development)
   - Your production domain (when deploying)

### 3. Set Up Firestore Database
1. Navigate to **Firestore Database**
2. Click **Create database**
3. Choose **Start in test mode** (for development)
4. Select a location close to your users
5. Click **Done**

### 4. Configure Firestore Security Rules
1. In Firestore Database → **Rules**
2. Replace the default rules with the content from `firestore.rules`
3. Click **Publish**

### 5. Enable Storage (Optional)
1. Navigate to **Storage**
2. Click **Get started**
3. Use default security rules for now
4. Choose same location as Firestore

## Troubleshooting Common Issues

### "Firebase: Error (auth/configuration-not-found)"
- Ensure Email/Password provider is enabled in Firebase Console
- Check that your Firebase project is active

### "Firebase: Error (auth/unauthorized-domain)"
- Add `localhost` to authorized domains in Firebase Console
- For production, add your actual domain

### "Firebase: Error (auth/network-request-failed)"
- Check internet connection
- Verify Firebase project exists and is accessible

### "Firebase: Error (auth/invalid-api-key)"
- Verify Firebase configuration in `src/firebase/config.ts`
- Ensure API key is correct and project is active

## Testing Authentication

### Using the Debug Button
1. Go to the Register page
2. Click "Test Firebase Connection (Debug)"
3. Check browser console for detailed error messages
4. This will help identify specific Firebase configuration issues

### Manual Testing
1. Fill out the registration form
2. Click "Sign Up"
3. Check browser console (F12) for any errors
4. Look for network requests to Firebase in Network tab

## Current Firebase Configuration
```javascript
const firebaseConfig = {
  apiKey: "AIzaSyDzM9JWNZtJPLoqWuuoxYjoHv0rlRS0w4I",
  authDomain: "studyhub-7900c.firebaseapp.com",
  projectId: "studyhub-7900c",
  storageBucket: "studyhub-7900c.firebasestorage.app",
  messagingSenderId: "1029123176295",
  appId: "1:1029123176295:web:338de9764464a0ee1f21c2",
  measurementId: "G-YMP3M6XH1P"
};
```

## Next Steps
1. ✅ Verify Firebase Console setup (Authentication enabled)
2. ✅ Test user registration with debug tools
3. ✅ Deploy Firestore security rules
4. ✅ Test all authentication flows (login, logout, profile creation)
