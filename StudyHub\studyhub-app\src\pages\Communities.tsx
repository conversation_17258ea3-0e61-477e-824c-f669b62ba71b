import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>rid,
  Card,
  CardContent,
  CardActions,
  Typography,
  Button,
  TextField,
  Box,
  Avatar,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Paper,
  Divider,
  AvatarGroup,
} from '@mui/material';
import {
  Add,
  People,
  Public,
  Lock,
  Search,
} from '@mui/icons-material';
import { useAuth } from '../contexts/AuthContext';

interface Community {
  id: string;
  name: string;
  description: string;
  category: string;
  memberCount: number;
  postCount: number;
  isPublic: boolean;
  isMember: boolean;
  avatar?: string;
  recentMembers: string[];
  tags: string[];
  createdAt: Date;
}

const Communities: React.FC = () => {
  const { currentUser } = useAuth();
  const [communities, setCommunities] = useState<Community[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [createDialogOpen, setCreateDialogOpen] = useState(false);
  const [newCommunity, setNewCommunity] = useState({
    name: '',
    description: '',
    category: '',
    isPublic: true,
    tags: [] as string[],
  });

  const categories = [
    'Technology',
    'Environment',
    'Lifestyle',
    'Wellness',
    'Finance',
    'Education',
    'Arts',
    'Sports',
    'Politics',
    'Science',
  ];

  useEffect(() => {
    // Sample communities data
    const sampleCommunities: Community[] = [
      {
        id: '1',
        name: 'Climate Action Network',
        description: 'Discussing climate change solutions and environmental activism',
        category: 'Environment',
        memberCount: 1247,
        postCount: 89,
        isPublic: true,
        isMember: false,
        recentMembers: ['user1', 'user2', 'user3'],
        tags: ['climate', 'environment', 'activism'],
        createdAt: new Date('2024-01-15'),
      },
      {
        id: '2',
        name: 'AI & Future Tech',
        description: 'Exploring artificial intelligence and emerging technologies',
        category: 'Technology',
        memberCount: 892,
        postCount: 156,
        isPublic: true,
        isMember: true,
        recentMembers: ['user4', 'user5', 'user6'],
        tags: ['AI', 'technology', 'innovation'],
        createdAt: new Date('2024-02-01'),
      },
      {
        id: '3',
        name: 'Mental Wellness Hub',
        description: 'Supporting mental health and sharing wellness strategies',
        category: 'Wellness',
        memberCount: 634,
        postCount: 78,
        isPublic: true,
        isMember: false,
        recentMembers: ['user7', 'user8', 'user9'],
        tags: ['mentalhealth', 'wellness', 'support'],
        createdAt: new Date('2024-01-20'),
      },
      {
        id: '4',
        name: 'Crypto Discussions',
        description: 'Analyzing cryptocurrency trends and blockchain technology',
        category: 'Finance',
        memberCount: 445,
        postCount: 203,
        isPublic: true,
        isMember: true,
        recentMembers: ['user10', 'user11', 'user12'],
        tags: ['crypto', 'blockchain', 'finance'],
        createdAt: new Date('2024-02-10'),
      },
      {
        id: '5',
        name: 'Sustainable Living',
        description: 'Tips and discussions about eco-friendly lifestyle choices',
        category: 'Lifestyle',
        memberCount: 389,
        postCount: 67,
        isPublic: true,
        isMember: false,
        recentMembers: ['user13', 'user14', 'user15'],
        tags: ['sustainability', 'lifestyle', 'eco'],
        createdAt: new Date('2024-01-25'),
      },
    ];
    setCommunities(sampleCommunities);
  }, []);

  const filteredCommunities = communities.filter(community => {
    const matchesSearch = community.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         community.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         community.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()));
    const matchesCategory = selectedCategory === 'all' || community.category === selectedCategory;
    return matchesSearch && matchesCategory;
  });

  const handleJoinCommunity = (communityId: string) => {
    setCommunities(communities.map(community =>
      community.id === communityId
        ? { 
            ...community, 
            isMember: !community.isMember,
            memberCount: community.isMember ? community.memberCount - 1 : community.memberCount + 1
          }
        : community
    ));
  };

  const handleCreateCommunity = () => {
    if (!newCommunity.name.trim() || !newCommunity.description.trim()) return;

    const community: Community = {
      id: Date.now().toString(),
      name: newCommunity.name,
      description: newCommunity.description,
      category: newCommunity.category,
      memberCount: 1,
      postCount: 0,
      isPublic: newCommunity.isPublic,
      isMember: true,
      recentMembers: [currentUser?.uid || ''],
      tags: newCommunity.tags,
      createdAt: new Date(),
    };

    setCommunities([community, ...communities]);
    setNewCommunity({
      name: '',
      description: '',
      category: '',
      isPublic: true,
      tags: [],
    });
    setCreateDialogOpen(false);
  };

  const formatNumber = (num: number) => {
    if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K';
    }
    return num.toString();
  };

  return (
    <Container maxWidth="lg">
      <Box sx={{ py: 3 }}>
        <Typography variant="h4" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <People />
          Communities
        </Typography>
        <Typography variant="body1" color="text.secondary" sx={{ mb: 4 }}>
          Join communities to share opinions and connect with like-minded people
        </Typography>

        {/* Search and Filter */}
        <Paper sx={{ p: 3, mb: 4 }}>
          <Grid container spacing={2} alignItems="center">
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                placeholder="Search communities..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                InputProps={{
                  startAdornment: <Search sx={{ mr: 1, color: 'text.secondary' }} />,
                }}
              />
            </Grid>
            <Grid item xs={12} md={4}>
              <FormControl fullWidth>
                <InputLabel>Category</InputLabel>
                <Select
                  value={selectedCategory}
                  label="Category"
                  onChange={(e) => setSelectedCategory(e.target.value)}
                >
                  <MenuItem value="all">All Categories</MenuItem>
                  {categories.map((category) => (
                    <MenuItem key={category} value={category}>
                      {category}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={2}>
              <Button
                fullWidth
                variant="contained"
                startIcon={<Add />}
                onClick={() => setCreateDialogOpen(true)}
                disabled={!currentUser}
              >
                Create
              </Button>
            </Grid>
          </Grid>
        </Paper>

        {/* Communities Grid */}
        <Grid container spacing={3}>
          {filteredCommunities.map((community) => (
            <Grid item xs={12} md={6} lg={4} key={community.id}>
              <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
                <CardContent sx={{ flexGrow: 1 }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <Avatar sx={{ mr: 2, bgcolor: 'primary.main' }}>
                      {community.name[0]}
                    </Avatar>
                    <Box sx={{ flexGrow: 1 }}>
                      <Typography variant="h6" component="h2">
                        {community.name}
                      </Typography>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        {community.isPublic ? <Public fontSize="small" /> : <Lock fontSize="small" />}
                        <Typography variant="caption" color="text.secondary">
                          {community.isPublic ? 'Public' : 'Private'}
                        </Typography>
                      </Box>
                    </Box>
                  </Box>

                  <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                    {community.description}
                  </Typography>

                  <Box sx={{ mb: 2 }}>
                    {community.tags.map((tag) => (
                      <Chip
                        key={tag}
                        label={`#${tag}`}
                        size="small"
                        sx={{ mr: 1, mb: 1 }}
                        color="primary"
                        variant="outlined"
                      />
                    ))}
                  </Box>

                  <Divider sx={{ my: 2 }} />

                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                    <Box sx={{ display: 'flex', gap: 2 }}>
                      <Box sx={{ textAlign: 'center' }}>
                        <Typography variant="h6" color="primary">
                          {formatNumber(community.memberCount)}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          Members
                        </Typography>
                      </Box>
                      <Box sx={{ textAlign: 'center' }}>
                        <Typography variant="h6" color="primary">
                          {community.postCount}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          Posts
                        </Typography>
                      </Box>
                    </Box>
                    <AvatarGroup max={3} sx={{ '& .MuiAvatar-root': { width: 24, height: 24 } }}>
                      {community.recentMembers.map((member, index) => (
                        <Avatar key={member} sx={{ fontSize: '0.75rem' }}>
                          {String.fromCharCode(65 + index)}
                        </Avatar>
                      ))}
                    </AvatarGroup>
                  </Box>
                </CardContent>

                <CardActions sx={{ p: 2, pt: 0 }}>
                  <Button
                    fullWidth
                    variant={community.isMember ? "outlined" : "contained"}
                    onClick={() => handleJoinCommunity(community.id)}
                    disabled={!currentUser}
                  >
                    {community.isMember ? 'Leave' : 'Join'}
                  </Button>
                </CardActions>
              </Card>
            </Grid>
          ))}
        </Grid>

        {/* Create Community Dialog */}
        <Dialog open={createDialogOpen} onClose={() => setCreateDialogOpen(false)} maxWidth="sm" fullWidth>
          <DialogTitle>Create New Community</DialogTitle>
          <DialogContent>
            <TextField
              autoFocus
              margin="dense"
              label="Community Name"
              fullWidth
              variant="outlined"
              value={newCommunity.name}
              onChange={(e) => setNewCommunity({ ...newCommunity, name: e.target.value })}
              sx={{ mb: 2 }}
            />
            
            <TextField
              margin="dense"
              label="Description"
              fullWidth
              multiline
              rows={3}
              variant="outlined"
              value={newCommunity.description}
              onChange={(e) => setNewCommunity({ ...newCommunity, description: e.target.value })}
              sx={{ mb: 2 }}
            />

            <FormControl fullWidth sx={{ mb: 2 }}>
              <InputLabel>Category</InputLabel>
              <Select
                value={newCommunity.category}
                label="Category"
                onChange={(e) => setNewCommunity({ ...newCommunity, category: e.target.value })}
              >
                {categories.map((category) => (
                  <MenuItem key={category} value={category}>
                    {category}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>

            <FormControl fullWidth>
              <InputLabel>Privacy</InputLabel>
              <Select
                value={newCommunity.isPublic ? 'public' : 'private'}
                label="Privacy"
                onChange={(e) => setNewCommunity({ ...newCommunity, isPublic: e.target.value === 'public' })}
              >
                <MenuItem value="public">Public - Anyone can join</MenuItem>
                <MenuItem value="private">Private - Invite only</MenuItem>
              </Select>
            </FormControl>
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setCreateDialogOpen(false)}>Cancel</Button>
            <Button 
              onClick={handleCreateCommunity} 
              variant="contained"
              disabled={!newCommunity.name.trim() || !newCommunity.description.trim() || !newCommunity.category}
            >
              Create Community
            </Button>
          </DialogActions>
        </Dialog>
      </Box>
    </Container>
  );
};

export default Communities;
