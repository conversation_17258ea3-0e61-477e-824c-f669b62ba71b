{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\\\u30C9\\u30AD\\u30E5\\u30E1\\u30F3\\u30C8\\\\Projects\\\\StudyHub\\\\studyhub-app\\\\src\\\\pages\\\\Feed.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Container, Grid, Card, CardContent, CardActions, Typography, Button, TextField, Box, Avatar, IconButton, Fab, Dialog, DialogTitle, DialogContent, DialogActions, Chip, Paper, Divider } from '@mui/material';\nimport { Add, Favorite, FavoriteBorder, Comment, Share, MoreVert, Send } from '@mui/icons-material';\nimport { useAuth } from '../contexts/AuthContext';\nimport { PostService } from '../services/postService';\n\n// Post interface is now imported from PostService\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Feed = () => {\n  _s();\n  var _userProfile$firstNam, _userProfile$lastName;\n  const {\n    currentUser,\n    userProfile\n  } = useAuth();\n  const [posts, setPosts] = useState([]);\n  const [newPostContent, setNewPostContent] = useState('');\n  const [createPostOpen, setCreatePostOpen] = useState(false);\n  const [selectedTags, setSelectedTags] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [posting, setPosting] = useState(false);\n\n  // Load posts from Firebase\n  useEffect(() => {\n    setLoading(true);\n    setError(null);\n\n    // Subscribe to real-time posts updates\n    const unsubscribe = PostService.subscribeToPosts(fetchedPosts => {\n      setPosts(fetchedPosts);\n      setLoading(false);\n    });\n\n    // Cleanup subscription on unmount\n    return () => unsubscribe();\n  }, []);\n  const handleCreatePost = async () => {\n    if (!newPostContent.trim() || !currentUser) return;\n    setPosting(true);\n    setError(null);\n    try {\n      const postData = {\n        authorId: currentUser.uid,\n        authorName: `${(userProfile === null || userProfile === void 0 ? void 0 : userProfile.firstName) || ''} ${(userProfile === null || userProfile === void 0 ? void 0 : userProfile.lastName) || ''}`.trim() || 'Anonymous',\n        authorAvatar: (userProfile === null || userProfile === void 0 ? void 0 : userProfile.profilePicture) || '',\n        content: newPostContent.trim(),\n        tags: selectedTags\n      };\n      await PostService.createPost(postData);\n\n      // Clear form\n      setNewPostContent('');\n      setSelectedTags([]);\n      setCreatePostOpen(false);\n    } catch (error) {\n      console.error('Error creating post:', error);\n      setError('Failed to create post. Please try again.');\n    } finally {\n      setPosting(false);\n    }\n  };\n  const handleLike = async postId => {\n    if (!currentUser) return;\n    try {\n      const post = posts.find(p => p.id === postId);\n      if (!post) return;\n      const isLiked = post.likes.includes(currentUser.uid);\n      if (isLiked) {\n        await PostService.unlikePost(postId, currentUser.uid);\n      } else {\n        await PostService.likePost(postId, currentUser.uid);\n      }\n      // The UI will update automatically through the real-time subscription\n    } catch (error) {\n      console.error('Error toggling like:', error);\n      setError('Failed to update like. Please try again.');\n    }\n  };\n  const formatTimeAgo = timestamp => {\n    const now = new Date();\n    const diffInHours = Math.floor((now.getTime() - timestamp.getTime()) / (1000 * 60 * 60));\n    if (diffInHours < 1) return 'Just now';\n    if (diffInHours < 24) return `${diffInHours}h ago`;\n    return `${Math.floor(diffInHours / 24)}d ago`;\n  };\n  const availableTags = ['technology', 'AI', 'climate', 'environment', 'wellness', 'work', 'mentalhealth', 'discussion', 'future', 'innovation'];\n  return /*#__PURE__*/_jsxDEV(Container, {\n    maxWidth: \"md\",\n    children: /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        py: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h4\",\n        gutterBottom: true,\n        children: \"Your Feed\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 128,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"body1\",\n        color: \"text.secondary\",\n        sx: {\n          mb: 4\n        },\n        children: \"Share your thoughts and discover what others are talking about\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 131,\n        columnNumber: 9\n      }, this), currentUser && /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: 3,\n          mb: 3\n        },\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center',\n            gap: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(Avatar, {\n            src: userProfile === null || userProfile === void 0 ? void 0 : userProfile.profilePicture,\n            children: [userProfile === null || userProfile === void 0 ? void 0 : (_userProfile$firstNam = userProfile.firstName) === null || _userProfile$firstNam === void 0 ? void 0 : _userProfile$firstNam[0], userProfile === null || userProfile === void 0 ? void 0 : (_userProfile$lastName = userProfile.lastName) === null || _userProfile$lastName === void 0 ? void 0 : _userProfile$lastName[0]]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 139,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            fullWidth: true,\n            placeholder: \"What's on your mind?\",\n            variant: \"outlined\",\n            onClick: () => setCreatePostOpen(true),\n            sx: {\n              cursor: 'pointer'\n            },\n            InputProps: {\n              readOnly: true\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 142,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 138,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 137,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: 3,\n        children: posts.map(post => /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            children: [/*#__PURE__*/_jsxDEV(CardContent, {\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  alignItems: 'center',\n                  mb: 2\n                },\n                children: [/*#__PURE__*/_jsxDEV(Avatar, {\n                  src: post.authorAvatar,\n                  sx: {\n                    mr: 2\n                  },\n                  children: post.authorName.split(' ').map(n => n[0]).join('')\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 163,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    flexGrow: 1\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"subtitle1\",\n                    fontWeight: \"bold\",\n                    children: post.authorName\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 167,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"caption\",\n                    color: \"text.secondary\",\n                    children: formatTimeAgo(post.timestamp)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 170,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 166,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n                  children: /*#__PURE__*/_jsxDEV(MoreVert, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 175,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 174,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 162,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body1\",\n                sx: {\n                  mb: 2\n                },\n                children: post.content\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 179,\n                columnNumber: 19\n              }, this), post.tags.length > 0 && /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  mb: 2\n                },\n                children: post.tags.map(tag => /*#__PURE__*/_jsxDEV(Chip, {\n                  label: `#${tag}`,\n                  size: \"small\",\n                  sx: {\n                    mr: 1,\n                    mb: 1\n                  },\n                  color: \"primary\",\n                  variant: \"outlined\"\n                }, tag, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 186,\n                  columnNumber: 25\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 184,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 161,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 199,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(CardActions, {\n              sx: {\n                justifyContent: 'space-between',\n                px: 2\n              },\n              children: /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  gap: 1\n                },\n                children: [/*#__PURE__*/_jsxDEV(Button, {\n                  startIcon: post.isLiked ? /*#__PURE__*/_jsxDEV(Favorite, {\n                    color: \"error\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 204,\n                    columnNumber: 49\n                  }, this) : /*#__PURE__*/_jsxDEV(FavoriteBorder, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 204,\n                    columnNumber: 78\n                  }, this),\n                  onClick: () => handleLike(post.id),\n                  color: post.isLiked ? \"error\" : \"inherit\",\n                  children: post.likes\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 203,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Button, {\n                  startIcon: /*#__PURE__*/_jsxDEV(Comment, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 210,\n                    columnNumber: 40\n                  }, this),\n                  children: post.comments\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 210,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Button, {\n                  startIcon: /*#__PURE__*/_jsxDEV(Share, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 213,\n                    columnNumber: 40\n                  }, this),\n                  children: \"Share\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 213,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 202,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 201,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 160,\n            columnNumber: 15\n          }, this)\n        }, post.id, false, {\n          fileName: _jsxFileName,\n          lineNumber: 159,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 157,\n        columnNumber: 9\n      }, this), currentUser && /*#__PURE__*/_jsxDEV(Fab, {\n        color: \"primary\",\n        \"aria-label\": \"create post\",\n        sx: {\n          position: 'fixed',\n          bottom: 16,\n          right: 16\n        },\n        onClick: () => setCreatePostOpen(true),\n        children: /*#__PURE__*/_jsxDEV(Add, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 231,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 225,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n        open: createPostOpen,\n        onClose: () => setCreatePostOpen(false),\n        maxWidth: \"sm\",\n        fullWidth: true,\n        children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n          children: \"Create New Post\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 237,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n          children: [/*#__PURE__*/_jsxDEV(TextField, {\n            autoFocus: true,\n            margin: \"dense\",\n            label: \"What's on your mind?\",\n            fullWidth: true,\n            multiline: true,\n            rows: 4,\n            variant: \"outlined\",\n            value: newPostContent,\n            onChange: e => setNewPostContent(e.target.value),\n            sx: {\n              mb: 2\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 239,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle2\",\n            gutterBottom: true,\n            children: \"Add Tags:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 252,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              flexWrap: 'wrap',\n              gap: 1,\n              mb: 2\n            },\n            children: availableTags.map(tag => /*#__PURE__*/_jsxDEV(Chip, {\n              label: `#${tag}`,\n              clickable: true,\n              color: selectedTags.includes(tag) ? \"primary\" : \"default\",\n              onClick: () => {\n                setSelectedTags(prev => prev.includes(tag) ? prev.filter(t => t !== tag) : [...prev, tag]);\n              }\n            }, tag, false, {\n              fileName: _jsxFileName,\n              lineNumber: 257,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 255,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 238,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            onClick: () => setCreatePostOpen(false),\n            children: \"Cancel\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 274,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleCreatePost,\n            variant: \"contained\",\n            disabled: !newPostContent.trim(),\n            startIcon: /*#__PURE__*/_jsxDEV(Send, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 279,\n              columnNumber: 26\n            }, this),\n            children: \"Post\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 275,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 273,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 236,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 127,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 126,\n    columnNumber: 5\n  }, this);\n};\n_s(Feed, \"fyV8SW+VqMXs1VmIhI9+eho/bGI=\", false, function () {\n  return [useAuth];\n});\n_c = Feed;\nexport default Feed;\nvar _c;\n$RefreshReg$(_c, \"Feed\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Container", "Grid", "Card", "<PERSON><PERSON><PERSON><PERSON>", "CardActions", "Typography", "<PERSON><PERSON>", "TextField", "Box", "Avatar", "IconButton", "Fab", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "Chip", "Paper", "Divider", "Add", "Favorite", "FavoriteBorder", "Comment", "Share", "<PERSON><PERSON><PERSON>", "Send", "useAuth", "PostService", "jsxDEV", "_jsxDEV", "Feed", "_s", "_userProfile$firstNam", "_userProfile$lastName", "currentUser", "userProfile", "posts", "setPosts", "newPostContent", "set<PERSON>ew<PERSON>ost<PERSON><PERSON>nt", "createPostOpen", "setCreatePostOpen", "selectedTags", "setSelectedTags", "loading", "setLoading", "error", "setError", "posting", "setPosting", "unsubscribe", "subscribeToPosts", "fetchedPosts", "handleCreatePost", "trim", "postData", "authorId", "uid", "<PERSON><PERSON><PERSON>", "firstName", "lastName", "<PERSON><PERSON><PERSON><PERSON>", "profilePicture", "content", "tags", "createPost", "console", "handleLike", "postId", "post", "find", "p", "id", "isLiked", "likes", "includes", "unlikePost", "likePost", "formatTimeAgo", "timestamp", "now", "Date", "diffInHours", "Math", "floor", "getTime", "availableTags", "max<PERSON><PERSON><PERSON>", "children", "sx", "py", "variant", "gutterBottom", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "color", "mb", "display", "alignItems", "gap", "src", "fullWidth", "placeholder", "onClick", "cursor", "InputProps", "readOnly", "container", "spacing", "map", "item", "xs", "mr", "split", "n", "join", "flexGrow", "fontWeight", "length", "tag", "label", "size", "justifyContent", "px", "startIcon", "comments", "position", "bottom", "right", "open", "onClose", "autoFocus", "margin", "multiline", "rows", "value", "onChange", "e", "target", "flexWrap", "clickable", "prev", "filter", "t", "disabled", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/ドキュメント/Projects/StudyHub/studyhub-app/src/pages/Feed.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  <PERSON><PERSON><PERSON>,\n  Grid,\n  Card,\n  CardContent,\n  CardActions,\n  Typo<PERSON>,\n  Button,\n  TextField,\n  Box,\n  Avatar,\n  IconButton,\n  Fab,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  Chip,\n  Paper,\n  Divider,\n  CircularProgress,\n  Alert,\n} from '@mui/material';\nimport {\n  Add,\n  Favorite,\n  FavoriteBorder,\n  Comment,\n  Share,\n  MoreVert,\n  Send,\n} from '@mui/icons-material';\nimport { useAuth } from '../contexts/AuthContext';\nimport { PostService, Post } from '../services/postService';\n\n// Post interface is now imported from PostService\n\nconst Feed: React.FC = () => {\n  const { currentUser, userProfile } = useAuth();\n  const [posts, setPosts] = useState<Post[]>([]);\n  const [newPostContent, setNewPostContent] = useState('');\n  const [createPostOpen, setCreatePostOpen] = useState(false);\n  const [selectedTags, setSelectedTags] = useState<string[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n  const [posting, setPosting] = useState(false);\n\n  // Load posts from Firebase\n  useEffect(() => {\n    setLoading(true);\n    setError(null);\n\n    // Subscribe to real-time posts updates\n    const unsubscribe = PostService.subscribeToPosts((fetchedPosts) => {\n      setPosts(fetchedPosts);\n      setLoading(false);\n    });\n\n    // Cleanup subscription on unmount\n    return () => unsubscribe();\n  }, []);\n\n  const handleCreatePost = async () => {\n    if (!newPostContent.trim() || !currentUser) return;\n\n    setPosting(true);\n    setError(null);\n\n    try {\n      const postData = {\n        authorId: currentUser.uid,\n        authorName: `${userProfile?.firstName || ''} ${userProfile?.lastName || ''}`.trim() || 'Anonymous',\n        authorAvatar: userProfile?.profilePicture || '',\n        content: newPostContent.trim(),\n        tags: selectedTags,\n      };\n\n      await PostService.createPost(postData);\n\n      // Clear form\n      setNewPostContent('');\n      setSelectedTags([]);\n      setCreatePostOpen(false);\n    } catch (error) {\n      console.error('Error creating post:', error);\n      setError('Failed to create post. Please try again.');\n    } finally {\n      setPosting(false);\n    }\n  };\n\n  const handleLike = async (postId: string) => {\n    if (!currentUser) return;\n\n    try {\n      const post = posts.find(p => p.id === postId);\n      if (!post) return;\n\n      const isLiked = post.likes.includes(currentUser.uid);\n\n      if (isLiked) {\n        await PostService.unlikePost(postId, currentUser.uid);\n      } else {\n        await PostService.likePost(postId, currentUser.uid);\n      }\n      // The UI will update automatically through the real-time subscription\n    } catch (error) {\n      console.error('Error toggling like:', error);\n      setError('Failed to update like. Please try again.');\n    }\n  };\n\n  const formatTimeAgo = (timestamp: Date) => {\n    const now = new Date();\n    const diffInHours = Math.floor((now.getTime() - timestamp.getTime()) / (1000 * 60 * 60));\n    \n    if (diffInHours < 1) return 'Just now';\n    if (diffInHours < 24) return `${diffInHours}h ago`;\n    return `${Math.floor(diffInHours / 24)}d ago`;\n  };\n\n  const availableTags = ['technology', 'AI', 'climate', 'environment', 'wellness', 'work', 'mentalhealth', 'discussion', 'future', 'innovation'];\n\n  return (\n    <Container maxWidth=\"md\">\n      <Box sx={{ py: 3 }}>\n        <Typography variant=\"h4\" gutterBottom>\n          Your Feed\n        </Typography>\n        <Typography variant=\"body1\" color=\"text.secondary\" sx={{ mb: 4 }}>\n          Share your thoughts and discover what others are talking about\n        </Typography>\n\n        {/* Create Post Section */}\n        {currentUser && (\n          <Paper sx={{ p: 3, mb: 3 }}>\n            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>\n              <Avatar src={userProfile?.profilePicture}>\n                {userProfile?.firstName?.[0]}{userProfile?.lastName?.[0]}\n              </Avatar>\n              <TextField\n                fullWidth\n                placeholder=\"What's on your mind?\"\n                variant=\"outlined\"\n                onClick={() => setCreatePostOpen(true)}\n                sx={{ cursor: 'pointer' }}\n                InputProps={{\n                  readOnly: true,\n                }}\n              />\n            </Box>\n          </Paper>\n        )}\n\n        {/* Posts */}\n        <Grid container spacing={3}>\n          {posts.map((post) => (\n            <Grid item xs={12} key={post.id}>\n              <Card>\n                <CardContent>\n                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>\n                    <Avatar src={post.authorAvatar} sx={{ mr: 2 }}>\n                      {post.authorName.split(' ').map(n => n[0]).join('')}\n                    </Avatar>\n                    <Box sx={{ flexGrow: 1 }}>\n                      <Typography variant=\"subtitle1\" fontWeight=\"bold\">\n                        {post.authorName}\n                      </Typography>\n                      <Typography variant=\"caption\" color=\"text.secondary\">\n                        {formatTimeAgo(post.timestamp)}\n                      </Typography>\n                    </Box>\n                    <IconButton>\n                      <MoreVert />\n                    </IconButton>\n                  </Box>\n\n                  <Typography variant=\"body1\" sx={{ mb: 2 }}>\n                    {post.content}\n                  </Typography>\n\n                  {post.tags.length > 0 && (\n                    <Box sx={{ mb: 2 }}>\n                      {post.tags.map((tag) => (\n                        <Chip\n                          key={tag}\n                          label={`#${tag}`}\n                          size=\"small\"\n                          sx={{ mr: 1, mb: 1 }}\n                          color=\"primary\"\n                          variant=\"outlined\"\n                        />\n                      ))}\n                    </Box>\n                  )}\n                </CardContent>\n\n                <Divider />\n\n                <CardActions sx={{ justifyContent: 'space-between', px: 2 }}>\n                  <Box sx={{ display: 'flex', gap: 1 }}>\n                    <Button\n                      startIcon={post.isLiked ? <Favorite color=\"error\" /> : <FavoriteBorder />}\n                      onClick={() => handleLike(post.id)}\n                      color={post.isLiked ? \"error\" : \"inherit\"}\n                    >\n                      {post.likes}\n                    </Button>\n                    <Button startIcon={<Comment />}>\n                      {post.comments}\n                    </Button>\n                    <Button startIcon={<Share />}>\n                      Share\n                    </Button>\n                  </Box>\n                </CardActions>\n              </Card>\n            </Grid>\n          ))}\n        </Grid>\n\n        {/* Floating Action Button */}\n        {currentUser && (\n          <Fab\n            color=\"primary\"\n            aria-label=\"create post\"\n            sx={{ position: 'fixed', bottom: 16, right: 16 }}\n            onClick={() => setCreatePostOpen(true)}\n          >\n            <Add />\n          </Fab>\n        )}\n\n        {/* Create Post Dialog */}\n        <Dialog open={createPostOpen} onClose={() => setCreatePostOpen(false)} maxWidth=\"sm\" fullWidth>\n          <DialogTitle>Create New Post</DialogTitle>\n          <DialogContent>\n            <TextField\n              autoFocus\n              margin=\"dense\"\n              label=\"What's on your mind?\"\n              fullWidth\n              multiline\n              rows={4}\n              variant=\"outlined\"\n              value={newPostContent}\n              onChange={(e) => setNewPostContent(e.target.value)}\n              sx={{ mb: 2 }}\n            />\n            \n            <Typography variant=\"subtitle2\" gutterBottom>\n              Add Tags:\n            </Typography>\n            <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mb: 2 }}>\n              {availableTags.map((tag) => (\n                <Chip\n                  key={tag}\n                  label={`#${tag}`}\n                  clickable\n                  color={selectedTags.includes(tag) ? \"primary\" : \"default\"}\n                  onClick={() => {\n                    setSelectedTags(prev => \n                      prev.includes(tag) \n                        ? prev.filter(t => t !== tag)\n                        : [...prev, tag]\n                    );\n                  }}\n                />\n              ))}\n            </Box>\n          </DialogContent>\n          <DialogActions>\n            <Button onClick={() => setCreatePostOpen(false)}>Cancel</Button>\n            <Button \n              onClick={handleCreatePost} \n              variant=\"contained\"\n              disabled={!newPostContent.trim()}\n              startIcon={<Send />}\n            >\n              Post\n            </Button>\n          </DialogActions>\n        </Dialog>\n      </Box>\n    </Container>\n  );\n};\n\nexport default Feed;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,SAAS,EACTC,IAAI,EACJC,IAAI,EACJC,WAAW,EACXC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,SAAS,EACTC,GAAG,EACHC,MAAM,EACNC,UAAU,EACVC,GAAG,EACHC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,IAAI,EACJC,KAAK,EACLC,OAAO,QAGF,eAAe;AACtB,SACEC,GAAG,EACHC,QAAQ,EACRC,cAAc,EACdC,OAAO,EACPC,KAAK,EACLC,QAAQ,EACRC,IAAI,QACC,qBAAqB;AAC5B,SAASC,OAAO,QAAQ,yBAAyB;AACjD,SAASC,WAAW,QAAc,yBAAyB;;AAE3D;AAAA,SAAAC,MAAA,IAAAC,OAAA;AAEA,MAAMC,IAAc,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,qBAAA,EAAAC,qBAAA;EAC3B,MAAM;IAAEC,WAAW;IAAEC;EAAY,CAAC,GAAGT,OAAO,CAAC,CAAC;EAC9C,MAAM,CAACU,KAAK,EAAEC,QAAQ,CAAC,GAAGvC,QAAQ,CAAS,EAAE,CAAC;EAC9C,MAAM,CAACwC,cAAc,EAAEC,iBAAiB,CAAC,GAAGzC,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAAC0C,cAAc,EAAEC,iBAAiB,CAAC,GAAG3C,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAAC4C,YAAY,EAAEC,eAAe,CAAC,GAAG7C,QAAQ,CAAW,EAAE,CAAC;EAC9D,MAAM,CAAC8C,OAAO,EAAEC,UAAU,CAAC,GAAG/C,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACgD,KAAK,EAAEC,QAAQ,CAAC,GAAGjD,QAAQ,CAAgB,IAAI,CAAC;EACvD,MAAM,CAACkD,OAAO,EAAEC,UAAU,CAAC,GAAGnD,QAAQ,CAAC,KAAK,CAAC;;EAE7C;EACAC,SAAS,CAAC,MAAM;IACd8C,UAAU,CAAC,IAAI,CAAC;IAChBE,QAAQ,CAAC,IAAI,CAAC;;IAEd;IACA,MAAMG,WAAW,GAAGvB,WAAW,CAACwB,gBAAgB,CAAEC,YAAY,IAAK;MACjEf,QAAQ,CAACe,YAAY,CAAC;MACtBP,UAAU,CAAC,KAAK,CAAC;IACnB,CAAC,CAAC;;IAEF;IACA,OAAO,MAAMK,WAAW,CAAC,CAAC;EAC5B,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMG,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI,CAACf,cAAc,CAACgB,IAAI,CAAC,CAAC,IAAI,CAACpB,WAAW,EAAE;IAE5Ce,UAAU,CAAC,IAAI,CAAC;IAChBF,QAAQ,CAAC,IAAI,CAAC;IAEd,IAAI;MACF,MAAMQ,QAAQ,GAAG;QACfC,QAAQ,EAAEtB,WAAW,CAACuB,GAAG;QACzBC,UAAU,EAAE,GAAG,CAAAvB,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEwB,SAAS,KAAI,EAAE,IAAI,CAAAxB,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEyB,QAAQ,KAAI,EAAE,EAAE,CAACN,IAAI,CAAC,CAAC,IAAI,WAAW;QAClGO,YAAY,EAAE,CAAA1B,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAE2B,cAAc,KAAI,EAAE;QAC/CC,OAAO,EAAEzB,cAAc,CAACgB,IAAI,CAAC,CAAC;QAC9BU,IAAI,EAAEtB;MACR,CAAC;MAED,MAAMf,WAAW,CAACsC,UAAU,CAACV,QAAQ,CAAC;;MAEtC;MACAhB,iBAAiB,CAAC,EAAE,CAAC;MACrBI,eAAe,CAAC,EAAE,CAAC;MACnBF,iBAAiB,CAAC,KAAK,CAAC;IAC1B,CAAC,CAAC,OAAOK,KAAK,EAAE;MACdoB,OAAO,CAACpB,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC5CC,QAAQ,CAAC,0CAA0C,CAAC;IACtD,CAAC,SAAS;MACRE,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMkB,UAAU,GAAG,MAAOC,MAAc,IAAK;IAC3C,IAAI,CAAClC,WAAW,EAAE;IAElB,IAAI;MACF,MAAMmC,IAAI,GAAGjC,KAAK,CAACkC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,EAAE,KAAKJ,MAAM,CAAC;MAC7C,IAAI,CAACC,IAAI,EAAE;MAEX,MAAMI,OAAO,GAAGJ,IAAI,CAACK,KAAK,CAACC,QAAQ,CAACzC,WAAW,CAACuB,GAAG,CAAC;MAEpD,IAAIgB,OAAO,EAAE;QACX,MAAM9C,WAAW,CAACiD,UAAU,CAACR,MAAM,EAAElC,WAAW,CAACuB,GAAG,CAAC;MACvD,CAAC,MAAM;QACL,MAAM9B,WAAW,CAACkD,QAAQ,CAACT,MAAM,EAAElC,WAAW,CAACuB,GAAG,CAAC;MACrD;MACA;IACF,CAAC,CAAC,OAAOX,KAAK,EAAE;MACdoB,OAAO,CAACpB,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC5CC,QAAQ,CAAC,0CAA0C,CAAC;IACtD;EACF,CAAC;EAED,MAAM+B,aAAa,GAAIC,SAAe,IAAK;IACzC,MAAMC,GAAG,GAAG,IAAIC,IAAI,CAAC,CAAC;IACtB,MAAMC,WAAW,GAAGC,IAAI,CAACC,KAAK,CAAC,CAACJ,GAAG,CAACK,OAAO,CAAC,CAAC,GAAGN,SAAS,CAACM,OAAO,CAAC,CAAC,KAAK,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;IAExF,IAAIH,WAAW,GAAG,CAAC,EAAE,OAAO,UAAU;IACtC,IAAIA,WAAW,GAAG,EAAE,EAAE,OAAO,GAAGA,WAAW,OAAO;IAClD,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACF,WAAW,GAAG,EAAE,CAAC,OAAO;EAC/C,CAAC;EAED,MAAMI,aAAa,GAAG,CAAC,YAAY,EAAE,IAAI,EAAE,SAAS,EAAE,aAAa,EAAE,UAAU,EAAE,MAAM,EAAE,cAAc,EAAE,YAAY,EAAE,QAAQ,EAAE,YAAY,CAAC;EAE9I,oBACEzD,OAAA,CAAC7B,SAAS;IAACuF,QAAQ,EAAC,IAAI;IAAAC,QAAA,eACtB3D,OAAA,CAACrB,GAAG;MAACiF,EAAE,EAAE;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAF,QAAA,gBACjB3D,OAAA,CAACxB,UAAU;QAACsF,OAAO,EAAC,IAAI;QAACC,YAAY;QAAAJ,QAAA,EAAC;MAEtC;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACbnE,OAAA,CAACxB,UAAU;QAACsF,OAAO,EAAC,OAAO;QAACM,KAAK,EAAC,gBAAgB;QAACR,EAAE,EAAE;UAAES,EAAE,EAAE;QAAE,CAAE;QAAAV,QAAA,EAAC;MAElE;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,EAGZ9D,WAAW,iBACVL,OAAA,CAACZ,KAAK;QAACwE,EAAE,EAAE;UAAElB,CAAC,EAAE,CAAC;UAAE2B,EAAE,EAAE;QAAE,CAAE;QAAAV,QAAA,eACzB3D,OAAA,CAACrB,GAAG;UAACiF,EAAE,EAAE;YAAEU,OAAO,EAAE,MAAM;YAAEC,UAAU,EAAE,QAAQ;YAAEC,GAAG,EAAE;UAAE,CAAE;UAAAb,QAAA,gBACzD3D,OAAA,CAACpB,MAAM;YAAC6F,GAAG,EAAEnE,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAE2B,cAAe;YAAA0B,QAAA,GACtCrD,WAAW,aAAXA,WAAW,wBAAAH,qBAAA,GAAXG,WAAW,CAAEwB,SAAS,cAAA3B,qBAAA,uBAAtBA,qBAAA,CAAyB,CAAC,CAAC,EAAEG,WAAW,aAAXA,WAAW,wBAAAF,qBAAA,GAAXE,WAAW,CAAEyB,QAAQ,cAAA3B,qBAAA,uBAArBA,qBAAA,CAAwB,CAAC,CAAC;UAAA;YAAA4D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClD,CAAC,eACTnE,OAAA,CAACtB,SAAS;YACRgG,SAAS;YACTC,WAAW,EAAC,sBAAsB;YAClCb,OAAO,EAAC,UAAU;YAClBc,OAAO,EAAEA,CAAA,KAAMhE,iBAAiB,CAAC,IAAI,CAAE;YACvCgD,EAAE,EAAE;cAAEiB,MAAM,EAAE;YAAU,CAAE;YAC1BC,UAAU,EAAE;cACVC,QAAQ,EAAE;YACZ;UAAE;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CACR,eAGDnE,OAAA,CAAC5B,IAAI;QAAC4G,SAAS;QAACC,OAAO,EAAE,CAAE;QAAAtB,QAAA,EACxBpD,KAAK,CAAC2E,GAAG,CAAE1C,IAAI,iBACdxC,OAAA,CAAC5B,IAAI;UAAC+G,IAAI;UAACC,EAAE,EAAE,EAAG;UAAAzB,QAAA,eAChB3D,OAAA,CAAC3B,IAAI;YAAAsF,QAAA,gBACH3D,OAAA,CAAC1B,WAAW;cAAAqF,QAAA,gBACV3D,OAAA,CAACrB,GAAG;gBAACiF,EAAE,EAAE;kBAAEU,OAAO,EAAE,MAAM;kBAAEC,UAAU,EAAE,QAAQ;kBAAEF,EAAE,EAAE;gBAAE,CAAE;gBAAAV,QAAA,gBACxD3D,OAAA,CAACpB,MAAM;kBAAC6F,GAAG,EAAEjC,IAAI,CAACR,YAAa;kBAAC4B,EAAE,EAAE;oBAAEyB,EAAE,EAAE;kBAAE,CAAE;kBAAA1B,QAAA,EAC3CnB,IAAI,CAACX,UAAU,CAACyD,KAAK,CAAC,GAAG,CAAC,CAACJ,GAAG,CAACK,CAAC,IAAIA,CAAC,CAAC,CAAC,CAAC,CAAC,CAACC,IAAI,CAAC,EAAE;gBAAC;kBAAAxB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7C,CAAC,eACTnE,OAAA,CAACrB,GAAG;kBAACiF,EAAE,EAAE;oBAAE6B,QAAQ,EAAE;kBAAE,CAAE;kBAAA9B,QAAA,gBACvB3D,OAAA,CAACxB,UAAU;oBAACsF,OAAO,EAAC,WAAW;oBAAC4B,UAAU,EAAC,MAAM;oBAAA/B,QAAA,EAC9CnB,IAAI,CAACX;kBAAU;oBAAAmC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC,eACbnE,OAAA,CAACxB,UAAU;oBAACsF,OAAO,EAAC,SAAS;oBAACM,KAAK,EAAC,gBAAgB;oBAAAT,QAAA,EACjDV,aAAa,CAACT,IAAI,CAACU,SAAS;kBAAC;oBAAAc,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV,CAAC,eACNnE,OAAA,CAACnB,UAAU;kBAAA8E,QAAA,eACT3D,OAAA,CAACL,QAAQ;oBAAAqE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC,eAENnE,OAAA,CAACxB,UAAU;gBAACsF,OAAO,EAAC,OAAO;gBAACF,EAAE,EAAE;kBAAES,EAAE,EAAE;gBAAE,CAAE;gBAAAV,QAAA,EACvCnB,IAAI,CAACN;cAAO;gBAAA8B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,EAEZ3B,IAAI,CAACL,IAAI,CAACwD,MAAM,GAAG,CAAC,iBACnB3F,OAAA,CAACrB,GAAG;gBAACiF,EAAE,EAAE;kBAAES,EAAE,EAAE;gBAAE,CAAE;gBAAAV,QAAA,EAChBnB,IAAI,CAACL,IAAI,CAAC+C,GAAG,CAAEU,GAAG,iBACjB5F,OAAA,CAACb,IAAI;kBAEH0G,KAAK,EAAE,IAAID,GAAG,EAAG;kBACjBE,IAAI,EAAC,OAAO;kBACZlC,EAAE,EAAE;oBAAEyB,EAAE,EAAE,CAAC;oBAAEhB,EAAE,EAAE;kBAAE,CAAE;kBACrBD,KAAK,EAAC,SAAS;kBACfN,OAAO,EAAC;gBAAU,GALb8B,GAAG;kBAAA5B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAMT,CACF;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACU,CAAC,eAEdnE,OAAA,CAACX,OAAO;cAAA2E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAEXnE,OAAA,CAACzB,WAAW;cAACqF,EAAE,EAAE;gBAAEmC,cAAc,EAAE,eAAe;gBAAEC,EAAE,EAAE;cAAE,CAAE;cAAArC,QAAA,eAC1D3D,OAAA,CAACrB,GAAG;gBAACiF,EAAE,EAAE;kBAAEU,OAAO,EAAE,MAAM;kBAAEE,GAAG,EAAE;gBAAE,CAAE;gBAAAb,QAAA,gBACnC3D,OAAA,CAACvB,MAAM;kBACLwH,SAAS,EAAEzD,IAAI,CAACI,OAAO,gBAAG5C,OAAA,CAACT,QAAQ;oBAAC6E,KAAK,EAAC;kBAAO;oBAAAJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,gBAAGnE,OAAA,CAACR,cAAc;oBAAAwE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAE;kBAC1ES,OAAO,EAAEA,CAAA,KAAMtC,UAAU,CAACE,IAAI,CAACG,EAAE,CAAE;kBACnCyB,KAAK,EAAE5B,IAAI,CAACI,OAAO,GAAG,OAAO,GAAG,SAAU;kBAAAe,QAAA,EAEzCnB,IAAI,CAACK;gBAAK;kBAAAmB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC,eACTnE,OAAA,CAACvB,MAAM;kBAACwH,SAAS,eAAEjG,OAAA,CAACP,OAAO;oBAAAuE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAE;kBAAAR,QAAA,EAC5BnB,IAAI,CAAC0D;gBAAQ;kBAAAlC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACR,CAAC,eACTnE,OAAA,CAACvB,MAAM;kBAACwH,SAAS,eAAEjG,OAAA,CAACN,KAAK;oBAAAsE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAE;kBAAAR,QAAA,EAAC;gBAE9B;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC,GA3De3B,IAAI,CAACG,EAAE;UAAAqB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OA4DzB,CACP;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,EAGN9D,WAAW,iBACVL,OAAA,CAAClB,GAAG;QACFsF,KAAK,EAAC,SAAS;QACf,cAAW,aAAa;QACxBR,EAAE,EAAE;UAAEuC,QAAQ,EAAE,OAAO;UAAEC,MAAM,EAAE,EAAE;UAAEC,KAAK,EAAE;QAAG,CAAE;QACjDzB,OAAO,EAAEA,CAAA,KAAMhE,iBAAiB,CAAC,IAAI,CAAE;QAAA+C,QAAA,eAEvC3D,OAAA,CAACV,GAAG;UAAA0E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CACN,eAGDnE,OAAA,CAACjB,MAAM;QAACuH,IAAI,EAAE3F,cAAe;QAAC4F,OAAO,EAAEA,CAAA,KAAM3F,iBAAiB,CAAC,KAAK,CAAE;QAAC8C,QAAQ,EAAC,IAAI;QAACgB,SAAS;QAAAf,QAAA,gBAC5F3D,OAAA,CAAChB,WAAW;UAAA2E,QAAA,EAAC;QAAe;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa,CAAC,eAC1CnE,OAAA,CAACf,aAAa;UAAA0E,QAAA,gBACZ3D,OAAA,CAACtB,SAAS;YACR8H,SAAS;YACTC,MAAM,EAAC,OAAO;YACdZ,KAAK,EAAC,sBAAsB;YAC5BnB,SAAS;YACTgC,SAAS;YACTC,IAAI,EAAE,CAAE;YACR7C,OAAO,EAAC,UAAU;YAClB8C,KAAK,EAAEnG,cAAe;YACtBoG,QAAQ,EAAGC,CAAC,IAAKpG,iBAAiB,CAACoG,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;YACnDhD,EAAE,EAAE;cAAES,EAAE,EAAE;YAAE;UAAE;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACf,CAAC,eAEFnE,OAAA,CAACxB,UAAU;YAACsF,OAAO,EAAC,WAAW;YAACC,YAAY;YAAAJ,QAAA,EAAC;UAE7C;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbnE,OAAA,CAACrB,GAAG;YAACiF,EAAE,EAAE;cAAEU,OAAO,EAAE,MAAM;cAAE0C,QAAQ,EAAE,MAAM;cAAExC,GAAG,EAAE,CAAC;cAAEH,EAAE,EAAE;YAAE,CAAE;YAAAV,QAAA,EAC3DF,aAAa,CAACyB,GAAG,CAAEU,GAAG,iBACrB5F,OAAA,CAACb,IAAI;cAEH0G,KAAK,EAAE,IAAID,GAAG,EAAG;cACjBqB,SAAS;cACT7C,KAAK,EAAEvD,YAAY,CAACiC,QAAQ,CAAC8C,GAAG,CAAC,GAAG,SAAS,GAAG,SAAU;cAC1DhB,OAAO,EAAEA,CAAA,KAAM;gBACb9D,eAAe,CAACoG,IAAI,IAClBA,IAAI,CAACpE,QAAQ,CAAC8C,GAAG,CAAC,GACdsB,IAAI,CAACC,MAAM,CAACC,CAAC,IAAIA,CAAC,KAAKxB,GAAG,CAAC,GAC3B,CAAC,GAAGsB,IAAI,EAAEtB,GAAG,CACnB,CAAC;cACH;YAAE,GAVGA,GAAG;cAAA5B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAWT,CACF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC,eAChBnE,OAAA,CAACd,aAAa;UAAAyE,QAAA,gBACZ3D,OAAA,CAACvB,MAAM;YAACmG,OAAO,EAAEA,CAAA,KAAMhE,iBAAiB,CAAC,KAAK,CAAE;YAAA+C,QAAA,EAAC;UAAM;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAChEnE,OAAA,CAACvB,MAAM;YACLmG,OAAO,EAAEpD,gBAAiB;YAC1BsC,OAAO,EAAC,WAAW;YACnBuD,QAAQ,EAAE,CAAC5G,cAAc,CAACgB,IAAI,CAAC,CAAE;YACjCwE,SAAS,eAAEjG,OAAA,CAACJ,IAAI;cAAAoE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAAAR,QAAA,EACrB;UAED;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACG,CAAC;AAEhB,CAAC;AAACjE,EAAA,CAzPID,IAAc;EAAA,QACmBJ,OAAO;AAAA;AAAAyH,EAAA,GADxCrH,IAAc;AA2PpB,eAAeA,IAAI;AAAC,IAAAqH,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}