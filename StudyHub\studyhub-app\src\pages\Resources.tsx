import React, { useState } from 'react';
import {
  Container,
  <PERSON><PERSON><PERSON>,
  <PERSON>rid,
  Card,
  Card<PERSON>ontent,
  CardActions,
  Button,
  Box,
  Chip,
  TextField,
  InputAdornment,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Fab,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
} from '@mui/material';
import {
  Search,
  Add,
  PictureAsPdf,
  VideoLibrary,
  Article,
  Download,
  Favorite,
  FavoriteBorder,
} from '@mui/icons-material';

const Resources: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [filterSubject, setFilterSubject] = useState('');
  const [filterType, setFilterType] = useState('');
  const [open, setOpen] = useState(false);

  // Mock data - replace with actual data from Firebase
  const resources = [
    {
      id: 1,
      title: 'React Hooks Complete Guide',
      type: 'PDF',
      subject: 'Web Development',
      description: 'Comprehensive guide covering all React hooks with examples',
      author: '<PERSON>',
      uploadDate: '2024-01-15',
      downloads: 245,
      isFavorite: true,
      fileSize: '2.5 MB',
    },
    {
      id: 2,
      title: 'Data Structures Video Series',
      type: 'Video',
      subject: 'Computer Science',
      description: 'Complete video series on data structures and algorithms',
      author: 'Jane <PERSON>',
      uploadDate: '2024-01-10',
      downloads: 189,
      isFavorite: false,
      fileSize: '1.2 GB',
    },
    {
      id: 3,
      title: 'Machine Learning Cheat Sheet',
      type: 'Document',
      subject: 'Data Science',
      description: 'Quick reference for common ML algorithms and formulas',
      author: 'Dr. Wilson',
      uploadDate: '2024-01-08',
      downloads: 156,
      isFavorite: true,
      fileSize: '850 KB',
    },
    {
      id: 4,
      title: 'JavaScript ES6+ Features',
      type: 'PDF',
      subject: 'Programming',
      description: 'Modern JavaScript features with practical examples',
      author: 'Alex Johnson',
      uploadDate: '2024-01-05',
      downloads: 203,
      isFavorite: false,
      fileSize: '1.8 MB',
    },
    {
      id: 5,
      title: 'Database Design Principles',
      type: 'Document',
      subject: 'Database',
      description: 'Best practices for designing efficient databases',
      author: 'Sarah Brown',
      uploadDate: '2024-01-03',
      downloads: 134,
      isFavorite: false,
      fileSize: '1.1 MB',
    },
    {
      id: 6,
      title: 'Python Programming Tutorial',
      type: 'Video',
      subject: 'Programming',
      description: 'Beginner-friendly Python programming course',
      author: 'Mike Davis',
      uploadDate: '2024-01-01',
      downloads: 298,
      isFavorite: true,
      fileSize: '2.8 GB',
    },
  ];

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'PDF':
        return <PictureAsPdf color="error" />;
      case 'Video':
        return <VideoLibrary color="primary" />;
      case 'Document':
        return <Article color="success" />;
      default:
        return <Article />;
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'PDF':
        return 'error';
      case 'Video':
        return 'primary';
      case 'Document':
        return 'success';
      default:
        return 'default';
    }
  };

  const filteredResources = resources.filter((resource) => {
    const matchesSearch = resource.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         resource.description.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesSubject = !filterSubject || resource.subject === filterSubject;
    const matchesType = !filterType || resource.type === filterType;
    
    return matchesSearch && matchesSubject && matchesType;
  });

  const subjects = [...new Set(resources.map(r => r.subject))];
  const types = [...new Set(resources.map(r => r.type))];

  return (
    <Container maxWidth="lg">
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 4 }}>
        <div>
          <Typography variant="h4" component="h1" gutterBottom>
            Study Resources
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Access and share educational materials with the community
          </Typography>
        </div>
      </Box>

      {/* Search and Filters */}
      <Box sx={{ mb: 4 }}>
        <Grid container spacing={2} alignItems="center">
          <Grid xs={12} md={6}>
            <TextField
              fullWidth
              placeholder="Search resources..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <Search />
                  </InputAdornment>
                ),
              }}
            />
          </Grid>
          <Grid xs={12} sm={6} md={3}>
            <FormControl fullWidth>
              <InputLabel>Subject</InputLabel>
              <Select
                value={filterSubject}
                label="Subject"
                onChange={(e) => setFilterSubject(e.target.value)}
              >
                <MenuItem value="">All Subjects</MenuItem>
                {subjects.map((subject) => (
                  <MenuItem key={subject} value={subject}>
                    {subject}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>
          <Grid xs={12} sm={6} md={3}>
            <FormControl fullWidth>
              <InputLabel>Type</InputLabel>
              <Select
                value={filterType}
                label="Type"
                onChange={(e) => setFilterType(e.target.value)}
              >
                <MenuItem value="">All Types</MenuItem>
                {types.map((type) => (
                  <MenuItem key={type} value={type}>
                    {type}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>
        </Grid>
      </Box>

      {/* Resources Grid */}
      <Grid container spacing={3}>
        {filteredResources.map((resource) => (
          <Grid xs={12} md={6} lg={4} key={resource.id}>
            <Card
              sx={{
                height: '100%',
                display: 'flex',
                flexDirection: 'column',
                transition: 'transform 0.2s',
                '&:hover': {
                  transform: 'translateY(-4px)',
                  boxShadow: 3,
                },
              }}
            >
              <CardContent sx={{ flexGrow: 1 }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    {getTypeIcon(resource.type)}
                    <Typography variant="h6" component="h3">
                      {resource.title}
                    </Typography>
                  </Box>
                  <Button size="small" sx={{ minWidth: 'auto', p: 0.5 }}>
                    {resource.isFavorite ? <Favorite color="error" /> : <FavoriteBorder />}
                  </Button>
                </Box>

                <Box sx={{ display: 'flex', gap: 1, mb: 2 }}>
                  <Chip
                    label={resource.type}
                    size="small"
                    color={getTypeColor(resource.type) as any}
                    variant="outlined"
                  />
                  <Chip
                    label={resource.subject}
                    size="small"
                    color="primary"
                    variant="outlined"
                  />
                </Box>

                <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                  {resource.description}
                </Typography>

                <Typography variant="caption" color="text.secondary" display="block">
                  By {resource.author} • {resource.uploadDate}
                </Typography>
                <Typography variant="caption" color="text.secondary" display="block">
                  {resource.downloads} downloads • {resource.fileSize}
                </Typography>
              </CardContent>

              <CardActions>
                <Button size="small" startIcon={<Download />}>
                  Download
                </Button>
                <Button size="small">
                  Preview
                </Button>
              </CardActions>
            </Card>
          </Grid>
        ))}
      </Grid>

      {/* Floating Action Button */}
      <Fab
        color="primary"
        aria-label="upload resource"
        sx={{ position: 'fixed', bottom: 16, right: 16 }}
        onClick={() => setOpen(true)}
      >
        <Add />
      </Fab>

      {/* Upload Resource Dialog */}
      <Dialog open={open} onClose={() => setOpen(false)} maxWidth="sm" fullWidth>
        <DialogTitle>Upload New Resource</DialogTitle>
        <DialogContent>
          <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
            Share your study materials with the community
          </Typography>
          {/* TODO: Add upload form */}
          <Typography variant="body2">
            Upload form will be implemented here...
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpen(false)}>Cancel</Button>
          <Button variant="contained">Upload</Button>
        </DialogActions>
      </Dialog>
    </Container>
  );
};

export default Resources;
