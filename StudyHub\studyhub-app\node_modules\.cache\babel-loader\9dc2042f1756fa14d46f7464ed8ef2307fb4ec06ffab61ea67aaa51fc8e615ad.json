{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\\\u30C9\\u30AD\\u30E5\\u30E1\\u30F3\\u30C8\\\\Projects\\\\StudyHub\\\\studyhub-app\\\\src\\\\App.tsx\";\nimport React from 'react';\nimport { BrowserRouter as Router, Routes, Route } from 'react-router-dom';\nimport { ThemeProvider, createTheme } from '@mui/material/styles';\nimport CssBaseline from '@mui/material/CssBaseline';\nimport { Box } from '@mui/material';\n\n// Components\nimport { AuthProvider } from './contexts/AuthContext';\nimport Navbar from './components/Navbar';\nimport Home from './pages/Home';\nimport Profile from './pages/Profile';\nimport Chat from './pages/Chat';\nimport Login from './pages/Login';\nimport Register from './pages/Register';\n\n// Create Material UI theme\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst theme = createTheme({\n  palette: {\n    primary: {\n      main: '#1976d2'\n    },\n    secondary: {\n      main: '#dc004e'\n    },\n    background: {\n      default: '#f5f5f5'\n    }\n  },\n  typography: {\n    fontFamily: '\"Roboto\", \"Helvetica\", \"Arial\", sans-serif',\n    h1: {\n      fontSize: '2.5rem',\n      fontWeight: 600\n    },\n    h2: {\n      fontSize: '2rem',\n      fontWeight: 500\n    }\n  }\n});\nfunction App() {\n  return /*#__PURE__*/_jsxDEV(ThemeProvider, {\n    theme: theme,\n    children: [/*#__PURE__*/_jsxDEV(CssBaseline, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 49,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(AuthProvider, {\n      children: /*#__PURE__*/_jsxDEV(Router, {\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            flexDirection: 'column',\n            minHeight: '100vh'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Navbar, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 53,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            component: \"main\",\n            sx: {\n              flexGrow: 1,\n              p: 3\n            },\n            children: /*#__PURE__*/_jsxDEV(Routes, {\n              children: [/*#__PURE__*/_jsxDEV(Route, {\n                path: \"/\",\n                element: /*#__PURE__*/_jsxDEV(Home, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 56,\n                  columnNumber: 42\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 56,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Route, {\n                path: \"/dashboard\",\n                element: /*#__PURE__*/_jsxDEV(Dashboard, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 57,\n                  columnNumber: 51\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 57,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Route, {\n                path: \"/study-groups\",\n                element: /*#__PURE__*/_jsxDEV(StudyGroups, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 58,\n                  columnNumber: 54\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 58,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Route, {\n                path: \"/resources\",\n                element: /*#__PURE__*/_jsxDEV(Resources, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 59,\n                  columnNumber: 51\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 59,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Route, {\n                path: \"/profile\",\n                element: /*#__PURE__*/_jsxDEV(Profile, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 60,\n                  columnNumber: 49\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 60,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Route, {\n                path: \"/chat\",\n                element: /*#__PURE__*/_jsxDEV(Chat, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 61,\n                  columnNumber: 46\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 61,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Route, {\n                path: \"/login\",\n                element: /*#__PURE__*/_jsxDEV(Login, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 62,\n                  columnNumber: 47\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 62,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Route, {\n                path: \"/register\",\n                element: /*#__PURE__*/_jsxDEV(Register, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 63,\n                  columnNumber: 50\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 63,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 55,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 54,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 52,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 51,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 50,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 48,\n    columnNumber: 5\n  }, this);\n}\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Routes", "Route", "ThemeProvider", "createTheme", "CssBaseline", "Box", "<PERSON>th<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Home", "Profile", "Cha<PERSON>", "<PERSON><PERSON>", "Register", "jsxDEV", "_jsxDEV", "theme", "palette", "primary", "main", "secondary", "background", "default", "typography", "fontFamily", "h1", "fontSize", "fontWeight", "h2", "App", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "sx", "display", "flexDirection", "minHeight", "component", "flexGrow", "p", "path", "element", "Dashboard", "StudyGroups", "Resources", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/ドキュメント/Projects/StudyHub/studyhub-app/src/App.tsx"], "sourcesContent": ["import React from 'react';\nimport { BrowserRouter as Router, Routes, Route } from 'react-router-dom';\nimport { ThemeProvider, createTheme } from '@mui/material/styles';\nimport CssBaseline from '@mui/material/CssBaseline';\nimport { Box } from '@mui/material';\n\n// Components\nimport { AuthProvider } from './contexts/AuthContext';\nimport Navbar from './components/Navbar';\nimport Home from './pages/Home';\nimport Feed from './pages/Feed';\nimport Explore from './pages/Explore';\nimport Communities from './pages/Communities';\nimport Trending from './pages/Trending';\nimport Profile from './pages/Profile';\nimport Chat from './pages/Chat';\nimport Login from './pages/Login';\nimport Register from './pages/Register';\n\n// Create Material UI theme\nconst theme = createTheme({\n  palette: {\n    primary: {\n      main: '#1976d2',\n    },\n    secondary: {\n      main: '#dc004e',\n    },\n    background: {\n      default: '#f5f5f5',\n    },\n  },\n  typography: {\n    fontFamily: '\"Roboto\", \"Helvetica\", \"Arial\", sans-serif',\n    h1: {\n      fontSize: '2.5rem',\n      fontWeight: 600,\n    },\n    h2: {\n      fontSize: '2rem',\n      fontWeight: 500,\n    },\n  },\n});\n\nfunction App() {\n  return (\n    <ThemeProvider theme={theme}>\n      <CssBaseline />\n      <AuthProvider>\n        <Router>\n          <Box sx={{ display: 'flex', flexDirection: 'column', minHeight: '100vh' }}>\n            <Navbar />\n            <Box component=\"main\" sx={{ flexGrow: 1, p: 3 }}>\n              <Routes>\n                <Route path=\"/\" element={<Home />} />\n                <Route path=\"/dashboard\" element={<Dashboard />} />\n                <Route path=\"/study-groups\" element={<StudyGroups />} />\n                <Route path=\"/resources\" element={<Resources />} />\n                <Route path=\"/profile\" element={<Profile />} />\n                <Route path=\"/chat\" element={<Chat />} />\n                <Route path=\"/login\" element={<Login />} />\n                <Route path=\"/register\" element={<Register />} />\n              </Routes>\n            </Box>\n          </Box>\n        </Router>\n      </AuthProvider>\n    </ThemeProvider>\n  );\n}\n\nexport default App;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,aAAa,IAAIC,MAAM,EAAEC,MAAM,EAAEC,KAAK,QAAQ,kBAAkB;AACzE,SAASC,aAAa,EAAEC,WAAW,QAAQ,sBAAsB;AACjE,OAAOC,WAAW,MAAM,2BAA2B;AACnD,SAASC,GAAG,QAAQ,eAAe;;AAEnC;AACA,SAASC,YAAY,QAAQ,wBAAwB;AACrD,OAAOC,MAAM,MAAM,qBAAqB;AACxC,OAAOC,IAAI,MAAM,cAAc;AAK/B,OAAOC,OAAO,MAAM,iBAAiB;AACrC,OAAOC,IAAI,MAAM,cAAc;AAC/B,OAAOC,KAAK,MAAM,eAAe;AACjC,OAAOC,QAAQ,MAAM,kBAAkB;;AAEvC;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,KAAK,GAAGZ,WAAW,CAAC;EACxBa,OAAO,EAAE;IACPC,OAAO,EAAE;MACPC,IAAI,EAAE;IACR,CAAC;IACDC,SAAS,EAAE;MACTD,IAAI,EAAE;IACR,CAAC;IACDE,UAAU,EAAE;MACVC,OAAO,EAAE;IACX;EACF,CAAC;EACDC,UAAU,EAAE;IACVC,UAAU,EAAE,4CAA4C;IACxDC,EAAE,EAAE;MACFC,QAAQ,EAAE,QAAQ;MAClBC,UAAU,EAAE;IACd,CAAC;IACDC,EAAE,EAAE;MACFF,QAAQ,EAAE,MAAM;MAChBC,UAAU,EAAE;IACd;EACF;AACF,CAAC,CAAC;AAEF,SAASE,GAAGA,CAAA,EAAG;EACb,oBACEd,OAAA,CAACZ,aAAa;IAACa,KAAK,EAAEA,KAAM;IAAAc,QAAA,gBAC1Bf,OAAA,CAACV,WAAW;MAAA0B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACfnB,OAAA,CAACR,YAAY;MAAAuB,QAAA,eACXf,OAAA,CAACf,MAAM;QAAA8B,QAAA,eACLf,OAAA,CAACT,GAAG;UAAC6B,EAAE,EAAE;YAAEC,OAAO,EAAE,MAAM;YAAEC,aAAa,EAAE,QAAQ;YAAEC,SAAS,EAAE;UAAQ,CAAE;UAAAR,QAAA,gBACxEf,OAAA,CAACP,MAAM;YAAAuB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACVnB,OAAA,CAACT,GAAG;YAACiC,SAAS,EAAC,MAAM;YAACJ,EAAE,EAAE;cAAEK,QAAQ,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAE,CAAE;YAAAX,QAAA,eAC9Cf,OAAA,CAACd,MAAM;cAAA6B,QAAA,gBACLf,OAAA,CAACb,KAAK;gBAACwC,IAAI,EAAC,GAAG;gBAACC,OAAO,eAAE5B,OAAA,CAACN,IAAI;kBAAAsB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACrCnB,OAAA,CAACb,KAAK;gBAACwC,IAAI,EAAC,YAAY;gBAACC,OAAO,eAAE5B,OAAA,CAAC6B,SAAS;kBAAAb,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACnDnB,OAAA,CAACb,KAAK;gBAACwC,IAAI,EAAC,eAAe;gBAACC,OAAO,eAAE5B,OAAA,CAAC8B,WAAW;kBAAAd,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACxDnB,OAAA,CAACb,KAAK;gBAACwC,IAAI,EAAC,YAAY;gBAACC,OAAO,eAAE5B,OAAA,CAAC+B,SAAS;kBAAAf,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACnDnB,OAAA,CAACb,KAAK;gBAACwC,IAAI,EAAC,UAAU;gBAACC,OAAO,eAAE5B,OAAA,CAACL,OAAO;kBAAAqB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC/CnB,OAAA,CAACb,KAAK;gBAACwC,IAAI,EAAC,OAAO;gBAACC,OAAO,eAAE5B,OAAA,CAACJ,IAAI;kBAAAoB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACzCnB,OAAA,CAACb,KAAK;gBAACwC,IAAI,EAAC,QAAQ;gBAACC,OAAO,eAAE5B,OAAA,CAACH,KAAK;kBAAAmB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC3CnB,OAAA,CAACb,KAAK;gBAACwC,IAAI,EAAC,WAAW;gBAACC,OAAO,eAAE5B,OAAA,CAACF,QAAQ;kBAAAkB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3C;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACF,CAAC;AAEpB;AAACa,EAAA,GAzBQlB,GAAG;AA2BZ,eAAeA,GAAG;AAAC,IAAAkB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}