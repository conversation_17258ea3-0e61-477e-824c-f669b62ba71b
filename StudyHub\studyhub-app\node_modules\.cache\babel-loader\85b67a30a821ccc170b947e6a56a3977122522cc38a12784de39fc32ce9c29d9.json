{"ast": null, "code": "'use client';\n\nimport * as React from 'react';\nimport useEnhancedEffect from '../useEnhancedEffect';\n\n/**\n * Inspired by https://github.com/facebook/react/issues/14099#issuecomment-440013892\n * See RFC in https://github.com/reactjs/rfcs/pull/220\n */\n\nfunction useEventCallback(fn) {\n  const ref = React.useRef(fn);\n  useEnhancedEffect(() => {\n    ref.current = fn;\n  });\n  return React.useRef((...args) =>\n  // @ts-expect-error hide `this`\n  (0, ref.current)(...args)).current;\n}\nexport default useEventCallback;", "map": {"version": 3, "names": ["React", "useEnhancedEffect", "useEventCallback", "fn", "ref", "useRef", "current", "args"], "sources": ["C:/Users/<USER>/OneDrive/ドキュメント/Projects/StudyHub/studyhub-app/node_modules/@mui/utils/esm/useEventCallback/useEventCallback.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport useEnhancedEffect from '../useEnhancedEffect';\n\n/**\n * Inspired by https://github.com/facebook/react/issues/14099#issuecomment-440013892\n * See RFC in https://github.com/reactjs/rfcs/pull/220\n */\n\nfunction useEventCallback(fn) {\n  const ref = React.useRef(fn);\n  useEnhancedEffect(() => {\n    ref.current = fn;\n  });\n  return React.useRef((...args) =>\n  // @ts-expect-error hide `this`\n  (0, ref.current)(...args)).current;\n}\nexport default useEventCallback;"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,iBAAiB,MAAM,sBAAsB;;AAEpD;AACA;AACA;AACA;;AAEA,SAASC,gBAAgBA,CAACC,EAAE,EAAE;EAC5B,MAAMC,GAAG,GAAGJ,KAAK,CAACK,MAAM,CAACF,EAAE,CAAC;EAC5BF,iBAAiB,CAAC,MAAM;IACtBG,GAAG,CAACE,OAAO,GAAGH,EAAE;EAClB,CAAC,CAAC;EACF,OAAOH,KAAK,CAACK,MAAM,CAAC,CAAC,GAAGE,IAAI;EAC5B;EACA,CAAC,CAAC,EAAEH,GAAG,CAACE,OAAO,EAAE,GAAGC,IAAI,CAAC,CAAC,CAACD,OAAO;AACpC;AACA,eAAeJ,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}