{"ast": null, "code": "import _objectWithoutProperties from \"C:/Users/<USER>/OneDrive/\\u30C9\\u30AD\\u30E5\\u30E1\\u30F3\\u30C8/Projects/StudyHub/studyhub-app/node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js\";\nconst _excluded = [\"variants\"];\nimport { internal_serializeStyles } from '@mui/styled-engine';\nexport default function preprocessStyles(input) {\n  const {\n      variants\n    } = input,\n    style = _objectWithoutProperties(input, _excluded);\n  const result = {\n    variants,\n    style: internal_serializeStyles(style),\n    isProcessed: true\n  };\n\n  // Not supported on styled-components\n  if (result.style === style) {\n    return result;\n  }\n  if (variants) {\n    variants.forEach(variant => {\n      if (typeof variant.style !== 'function') {\n        variant.style = internal_serializeStyles(variant.style);\n      }\n    });\n  }\n  return result;\n}", "map": {"version": 3, "names": ["internal_serializeStyles", "preprocessStyles", "input", "variants", "style", "_objectWithoutProperties", "_excluded", "result", "isProcessed", "for<PERSON>ach", "variant"], "sources": ["C:/Users/<USER>/OneDrive/ドキュメント/Projects/StudyHub/studyhub-app/node_modules/@mui/system/esm/preprocessStyles.js"], "sourcesContent": ["import { internal_serializeStyles } from '@mui/styled-engine';\nexport default function preprocessStyles(input) {\n  const {\n    variants,\n    ...style\n  } = input;\n  const result = {\n    variants,\n    style: internal_serializeStyles(style),\n    isProcessed: true\n  };\n\n  // Not supported on styled-components\n  if (result.style === style) {\n    return result;\n  }\n  if (variants) {\n    variants.forEach(variant => {\n      if (typeof variant.style !== 'function') {\n        variant.style = internal_serializeStyles(variant.style);\n      }\n    });\n  }\n  return result;\n}"], "mappings": ";;AAAA,SAASA,wBAAwB,QAAQ,oBAAoB;AAC7D,eAAe,SAASC,gBAAgBA,CAACC,KAAK,EAAE;EAC9C,MAAM;MACJC;IAEF,CAAC,GAAGD,KAAK;IADJE,KAAK,GAAAC,wBAAA,CACNH,KAAK,EAAAI,SAAA;EACT,MAAMC,MAAM,GAAG;IACbJ,QAAQ;IACRC,KAAK,EAAEJ,wBAAwB,CAACI,KAAK,CAAC;IACtCI,WAAW,EAAE;EACf,CAAC;;EAED;EACA,IAAID,MAAM,CAACH,KAAK,KAAKA,KAAK,EAAE;IAC1B,OAAOG,MAAM;EACf;EACA,IAAIJ,QAAQ,EAAE;IACZA,QAAQ,CAACM,OAAO,CAACC,OAAO,IAAI;MAC1B,IAAI,OAAOA,OAAO,CAACN,KAAK,KAAK,UAAU,EAAE;QACvCM,OAAO,CAACN,KAAK,GAAGJ,wBAAwB,CAACU,OAAO,CAACN,KAAK,CAAC;MACzD;IACF,CAAC,CAAC;EACJ;EACA,OAAOG,MAAM;AACf", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}