{"ast": null, "code": "import ClassNameGenerator from \"../ClassNameGenerator/index.js\";\nexport const globalStateClasses = {\n  active: 'active',\n  checked: 'checked',\n  completed: 'completed',\n  disabled: 'disabled',\n  error: 'error',\n  expanded: 'expanded',\n  focused: 'focused',\n  focusVisible: 'focusVisible',\n  open: 'open',\n  readOnly: 'readOnly',\n  required: 'required',\n  selected: 'selected'\n};\nexport default function generateUtilityClass(componentName, slot) {\n  let globalStatePrefix = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 'Mui';\n  const globalStateClass = globalStateClasses[slot];\n  return globalStateClass ? \"\".concat(globalStatePrefix, \"-\").concat(globalStateClass) : \"\".concat(ClassNameGenerator.generate(componentName), \"-\").concat(slot);\n}\nexport function isGlobalState(slot) {\n  return globalStateClasses[slot] !== undefined;\n}", "map": {"version": 3, "names": ["ClassNameGenerator", "globalStateClasses", "active", "checked", "completed", "disabled", "error", "expanded", "focused", "focusVisible", "open", "readOnly", "required", "selected", "generateUtilityClass", "componentName", "slot", "globalStatePrefix", "arguments", "length", "undefined", "globalStateClass", "concat", "generate", "isGlobalState"], "sources": ["C:/Users/<USER>/OneDrive/ドキュメント/Projects/StudyHub/studyhub-app/node_modules/@mui/utils/esm/generateUtilityClass/generateUtilityClass.js"], "sourcesContent": ["import ClassNameGenerator from \"../ClassNameGenerator/index.js\";\nexport const globalStateClasses = {\n  active: 'active',\n  checked: 'checked',\n  completed: 'completed',\n  disabled: 'disabled',\n  error: 'error',\n  expanded: 'expanded',\n  focused: 'focused',\n  focusVisible: 'focusVisible',\n  open: 'open',\n  readOnly: 'readOnly',\n  required: 'required',\n  selected: 'selected'\n};\nexport default function generateUtilityClass(componentName, slot, globalStatePrefix = 'Mui') {\n  const globalStateClass = globalStateClasses[slot];\n  return globalStateClass ? `${globalStatePrefix}-${globalStateClass}` : `${ClassNameGenerator.generate(componentName)}-${slot}`;\n}\nexport function isGlobalState(slot) {\n  return globalStateClasses[slot] !== undefined;\n}"], "mappings": "AAAA,OAAOA,kBAAkB,MAAM,gCAAgC;AAC/D,OAAO,MAAMC,kBAAkB,GAAG;EAChCC,MAAM,EAAE,QAAQ;EAChBC,OAAO,EAAE,SAAS;EAClBC,SAAS,EAAE,WAAW;EACtBC,QAAQ,EAAE,UAAU;EACpBC,KAAK,EAAE,OAAO;EACdC,QAAQ,EAAE,UAAU;EACpBC,OAAO,EAAE,SAAS;EAClBC,YAAY,EAAE,cAAc;EAC5BC,IAAI,EAAE,MAAM;EACZC,QAAQ,EAAE,UAAU;EACpBC,QAAQ,EAAE,UAAU;EACpBC,QAAQ,EAAE;AACZ,CAAC;AACD,eAAe,SAASC,oBAAoBA,CAACC,aAAa,EAAEC,IAAI,EAA6B;EAAA,IAA3BC,iBAAiB,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,KAAK;EACzF,MAAMG,gBAAgB,GAAGpB,kBAAkB,CAACe,IAAI,CAAC;EACjD,OAAOK,gBAAgB,MAAAC,MAAA,CAAML,iBAAiB,OAAAK,MAAA,CAAID,gBAAgB,OAAAC,MAAA,CAAQtB,kBAAkB,CAACuB,QAAQ,CAACR,aAAa,CAAC,OAAAO,MAAA,CAAIN,IAAI,CAAE;AAChI;AACA,OAAO,SAASQ,aAAaA,CAACR,IAAI,EAAE;EAClC,OAAOf,kBAAkB,CAACe,IAAI,CAAC,KAAKI,SAAS;AAC/C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}