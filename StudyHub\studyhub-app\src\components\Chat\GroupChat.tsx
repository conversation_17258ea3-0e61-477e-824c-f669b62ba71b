import React, { useState, useEffect, useRef } from 'react';
import {
  Box,
  Paper,
  Typography,
  TextField,
  IconButton,
  Avatar,
  List,
  ListItem,
  Chip,
  Menu,
  MenuItem,
  Divider,
  CircularProgress,
  InputAdornment,
} from '@mui/material';
import {
  Send,
  AttachFile,
  EmojiEmotions,
  MoreVert,
  Reply,
  Delete,
  Edit,
  Close,
} from '@mui/icons-material';
import { formatDistanceToNow } from 'date-fns';
import { ChatService } from '../../services/chatService';
import { ChatMessage, Chat, ChatTypingIndicator } from '../../types/chat';
import { useAuth } from '../../contexts/AuthContext';

interface GroupChatProps {
  chatId: string;
  chat: Chat;
  onClose?: () => void;
}

const GroupChat: React.FC<GroupChatProps> = ({ chatId, chat, onClose }) => {
  const { user } = useAuth();
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [newMessage, setNewMessage] = useState('');
  const [loading, setLoading] = useState(true);
  const [sending, setSending] = useState(false);
  const [typingUsers, setTypingUsers] = useState<ChatTypingIndicator[]>([]);
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [selectedMessage, setSelectedMessage] = useState<ChatMessage | null>(null);
  const [replyTo, setReplyTo] = useState<ChatMessage | null>(null);
  const [editingMessage, setEditingMessage] = useState<ChatMessage | null>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const typingTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    if (!chatId || !user) return;

    // Subscribe to messages
    const unsubscribeMessages = ChatService.subscribeToMessages(
      chatId,
      (newMessages) => {
        setMessages(newMessages);
        setLoading(false);
        scrollToBottom();
      }
    );

    // Subscribe to typing indicators
    const unsubscribeTyping = ChatService.subscribeToTyping(
      chatId,
      user.uid,
      setTypingUsers
    );

    // Mark messages as read
    ChatService.markAsRead(chatId, user.uid);

    return () => {
      unsubscribeMessages();
      unsubscribeTyping();
    };
  }, [chatId, user]);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const handleSendMessage = async () => {
    if (!newMessage.trim() || !user || sending) return;

    setSending(true);
    try {
      await ChatService.sendMessage(
        chatId,
        user.uid,
        {
          content: newMessage.trim(),
          type: 'text',
          replyTo: replyTo?.id,
        },
        user.displayName || user.email?.split('@')[0] || 'User'
      );

      setNewMessage('');
      setReplyTo(null);

      // Stop typing indicator
      await ChatService.setTyping(chatId, user.uid, user.displayName || '', false);
    } catch (error) {
      console.error('Error sending message:', error);
    } finally {
      setSending(false);
    }
  };

  const handleTyping = async (value: string) => {
    setNewMessage(value);

    if (!user) return;

    // Clear existing timeout
    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current);
    }

    if (value.trim()) {
      // Set typing indicator
      await ChatService.setTyping(chatId, user.uid, user.displayName || '', true);

      // Clear typing after 3 seconds of inactivity
      typingTimeoutRef.current = setTimeout(async () => {
        await ChatService.setTyping(chatId, user.uid, user.displayName || '', false);
      }, 3000);
    } else {
      // Clear typing immediately if input is empty
      await ChatService.setTyping(chatId, user.uid, user.displayName || '', false);
    }
  };

  const handleMessageAction = (event: React.MouseEvent<HTMLElement>, message: ChatMessage) => {
    setAnchorEl(event.currentTarget);
    setSelectedMessage(message);
  };

  const handleCloseMenu = () => {
    setAnchorEl(null);
    setSelectedMessage(null);
  };

  const handleReply = () => {
    setReplyTo(selectedMessage);
    handleCloseMenu();
  };

  const handleAddReaction = async (emoji: string) => {
    if (!selectedMessage || !user) return;

    try {
      await ChatService.addReaction(chatId, selectedMessage.id, user.uid, emoji);
      handleCloseMenu();
    } catch (error) {
      console.error('Error adding reaction:', error);
    }
  };

  const formatMessageTime = (timestamp: any) => {
    if (!timestamp) return '';
    
    const date = timestamp.toDate ? timestamp.toDate() : new Date(timestamp);
    return formatDistanceToNow(date, { addSuffix: true });
  };

  const renderMessage = (message: ChatMessage, index: number) => {
    const isOwnMessage = message.senderId === user?.uid;
    const showAvatar = index === 0 || messages[index - 1]?.senderId !== message.senderId;
    const replyToMessage = message.replyTo ? messages.find(m => m.id === message.replyTo) : null;

    return (
      <ListItem
        key={message.id}
        sx={{
          flexDirection: 'column',
          alignItems: isOwnMessage ? 'flex-end' : 'flex-start',
          py: 0.5,
        }}
      >
        <Box
          sx={{
            display: 'flex',
            flexDirection: isOwnMessage ? 'row-reverse' : 'row',
            alignItems: 'flex-end',
            gap: 1,
            maxWidth: '70%',
          }}
        >
          {showAvatar && !isOwnMessage && (
            <Avatar
              src={message.senderAvatar}
              sx={{ width: 32, height: 32 }}
            >
              {message.senderName[0]}
            </Avatar>
          )}
          
          <Box
            sx={{
              display: 'flex',
              flexDirection: 'column',
              gap: 0.5,
            }}
          >
            {showAvatar && !isOwnMessage && (
              <Typography variant="caption" color="text.secondary" sx={{ px: 1 }}>
                {message.senderName}
              </Typography>
            )}
            
            {replyToMessage && (
              <Paper
                sx={{
                  p: 1,
                  bgcolor: 'action.hover',
                  borderLeft: 3,
                  borderColor: 'primary.main',
                  mb: 0.5,
                }}
              >
                <Typography variant="caption" color="text.secondary">
                  Replying to {replyToMessage.senderName}
                </Typography>
                <Typography variant="body2" sx={{ opacity: 0.7 }}>
                  {replyToMessage.content.length > 50
                    ? `${replyToMessage.content.substring(0, 50)}...`
                    : replyToMessage.content}
                </Typography>
              </Paper>
            )}
            
            <Paper
              sx={{
                p: 1.5,
                bgcolor: isOwnMessage ? 'primary.main' : 'background.paper',
                color: isOwnMessage ? 'primary.contrastText' : 'text.primary',
                borderRadius: 2,
                position: 'relative',
              }}
              onClick={(e) => handleMessageAction(e, message)}
            >
              <Typography variant="body2">{message.content}</Typography>
              
              {message.reactions && message.reactions.length > 0 && (
                <Box sx={{ display: 'flex', gap: 0.5, mt: 1, flexWrap: 'wrap' }}>
                  {message.reactions.map((reaction, idx) => (
                    <Chip
                      key={idx}
                      label={`${reaction.emoji} ${reaction.count}`}
                      size="small"
                      variant="outlined"
                      onClick={() => handleAddReaction(reaction.emoji)}
                      sx={{ height: 20, fontSize: '0.7rem' }}
                    />
                  ))}
                </Box>
              )}
              
              <Typography
                variant="caption"
                sx={{
                  display: 'block',
                  mt: 0.5,
                  opacity: 0.7,
                  fontSize: '0.7rem',
                }}
              >
                {formatMessageTime(message.timestamp)}
              </Typography>
            </Paper>
          </Box>
        </Box>
      </ListItem>
    );
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" height="400px">
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      {/* Chat Header */}
      <Paper
        sx={{
          p: 2,
          borderRadius: 0,
          borderBottom: 1,
          borderColor: 'divider',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
        }}
      >
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          <Avatar>{chat.name?.[0] || 'G'}</Avatar>
          <Box>
            <Typography variant="h6">{chat.name || 'Group Chat'}</Typography>
            <Typography variant="caption" color="text.secondary">
              {chat.participants.length} members
              {typingUsers.length > 0 && (
                <span> • {typingUsers.map(t => t.userName).join(', ')} typing...</span>
              )}
            </Typography>
          </Box>
        </Box>
        
        {onClose && (
          <IconButton onClick={onClose}>
            <Close />
          </IconButton>
        )}
      </Paper>

      {/* Messages */}
      <Box sx={{ flexGrow: 1, overflow: 'auto', p: 1 }}>
        <List sx={{ py: 0 }}>
          {messages.map((message, index) => renderMessage(message, index))}
        </List>
        <div ref={messagesEndRef} />
      </Box>

      {/* Reply Preview */}
      {replyTo && (
        <Paper sx={{ p: 1, m: 1, bgcolor: 'action.hover' }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Box>
              <Typography variant="caption" color="text.secondary">
                Replying to {replyTo.senderName}
              </Typography>
              <Typography variant="body2">
                {replyTo.content.length > 50
                  ? `${replyTo.content.substring(0, 50)}...`
                  : replyTo.content}
              </Typography>
            </Box>
            <IconButton size="small" onClick={() => setReplyTo(null)}>
              <Close />
            </IconButton>
          </Box>
        </Paper>
      )}

      {/* Message Input */}
      <Paper sx={{ p: 2, borderRadius: 0 }}>
        <Box sx={{ display: 'flex', gap: 1, alignItems: 'flex-end' }}>
          <TextField
            fullWidth
            multiline
            maxRows={4}
            placeholder="Type a message..."
            value={newMessage}
            onChange={(e) => handleTyping(e.target.value)}
            onKeyPress={(e) => {
              if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                handleSendMessage();
              }
            }}
            InputProps={{
              endAdornment: (
                <InputAdornment position="end">
                  <IconButton size="small">
                    <AttachFile />
                  </IconButton>
                  <IconButton size="small">
                    <EmojiEmotions />
                  </IconButton>
                </InputAdornment>
              ),
            }}
          />
          <IconButton
            color="primary"
            onClick={handleSendMessage}
            disabled={!newMessage.trim() || sending}
          >
            {sending ? <CircularProgress size={20} /> : <Send />}
          </IconButton>
        </Box>
      </Paper>

      {/* Message Actions Menu */}
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleCloseMenu}
      >
        <MenuItem onClick={handleReply}>
          <Reply sx={{ mr: 1 }} />
          Reply
        </MenuItem>
        <MenuItem onClick={() => handleAddReaction('👍')}>
          👍 Like
        </MenuItem>
        <MenuItem onClick={() => handleAddReaction('❤️')}>
          ❤️ Love
        </MenuItem>
        <MenuItem onClick={() => handleAddReaction('😂')}>
          😂 Laugh
        </MenuItem>
        {selectedMessage?.senderId === user?.uid && (
          <>
            <Divider />
            <MenuItem onClick={() => setEditingMessage(selectedMessage)}>
              <Edit sx={{ mr: 1 }} />
              Edit
            </MenuItem>
            <MenuItem sx={{ color: 'error.main' }}>
              <Delete sx={{ mr: 1 }} />
              Delete
            </MenuItem>
          </>
        )}
      </Menu>
    </Box>
  );
};

export default GroupChat;
