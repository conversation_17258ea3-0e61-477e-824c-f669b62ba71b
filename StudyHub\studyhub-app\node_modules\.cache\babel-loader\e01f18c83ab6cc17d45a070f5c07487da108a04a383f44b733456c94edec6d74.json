{"ast": null, "code": "'use client';\n\nimport _objectSpread from \"C:/Users/<USER>/OneDrive/\\u30C9\\u30AD\\u30E5\\u30E1\\u30F3\\u30C8/Projects/StudyHub/studyhub-app/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport { GlobalStyles } from '@mui/styled-engine';\nimport { useTheme as muiUseTheme } from '@mui/private-theming';\nimport useEnhancedEffect from '@mui/utils/useEnhancedEffect';\nimport ThemeProvider from \"../ThemeProvider/index.js\";\nimport InitColorSchemeScript, { DEFAULT_COLOR_SCHEME_STORAGE_KEY, DEFAULT_MODE_STORAGE_KEY } from \"../InitColorSchemeScript/InitColorSchemeScript.js\";\nimport useCurrentColorScheme from \"./useCurrentColorScheme.js\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nexport const DISABLE_CSS_TRANSITION = '*{-webkit-transition:none!important;-moz-transition:none!important;-o-transition:none!important;-ms-transition:none!important;transition:none!important}';\nexport default function createCssVarsProvider(options) {\n  const {\n    themeId,\n    /**\n     * This `theme` object needs to follow a certain structure to\n     * be used correctly by the finel `CssVarsProvider`. It should have a\n     * `colorSchemes` key with the light and dark (and any other) palette.\n     * It should also ideally have a vars object created using `prepareCssVars`.\n     */\n    theme: defaultTheme = {},\n    modeStorageKey: defaultModeStorageKey = DEFAULT_MODE_STORAGE_KEY,\n    colorSchemeStorageKey: defaultColorSchemeStorageKey = DEFAULT_COLOR_SCHEME_STORAGE_KEY,\n    disableTransitionOnChange: designSystemTransitionOnChange = false,\n    defaultColorScheme,\n    resolveTheme\n  } = options;\n  const defaultContext = {\n    allColorSchemes: [],\n    colorScheme: undefined,\n    darkColorScheme: undefined,\n    lightColorScheme: undefined,\n    mode: undefined,\n    setColorScheme: () => {},\n    setMode: () => {},\n    systemMode: undefined\n  };\n  const ColorSchemeContext = /*#__PURE__*/React.createContext(undefined);\n  if (process.env.NODE_ENV !== 'production') {\n    ColorSchemeContext.displayName = 'ColorSchemeContext';\n  }\n  const useColorScheme = () => React.useContext(ColorSchemeContext) || defaultContext;\n  const defaultColorSchemes = {};\n  const defaultComponents = {};\n  function CssVarsProvider(props) {\n    var _colorSchemes$restThe, _restThemeProp$palett, _memoTheme$generateSt;\n    const {\n      children,\n      theme: themeProp,\n      modeStorageKey = defaultModeStorageKey,\n      colorSchemeStorageKey = defaultColorSchemeStorageKey,\n      disableTransitionOnChange = designSystemTransitionOnChange,\n      storageManager,\n      storageWindow = typeof window === 'undefined' ? undefined : window,\n      documentNode = typeof document === 'undefined' ? undefined : document,\n      colorSchemeNode = typeof document === 'undefined' ? undefined : document.documentElement,\n      disableNestedContext = false,\n      disableStyleSheetGeneration = false,\n      defaultMode: initialMode = 'system',\n      forceThemeRerender = false,\n      noSsr\n    } = props;\n    const hasMounted = React.useRef(false);\n    const upperTheme = muiUseTheme();\n    const ctx = React.useContext(ColorSchemeContext);\n    const nested = !!ctx && !disableNestedContext;\n    const initialTheme = React.useMemo(() => {\n      if (themeProp) {\n        return themeProp;\n      }\n      return typeof defaultTheme === 'function' ? defaultTheme() : defaultTheme;\n    }, [themeProp]);\n    const scopedTheme = initialTheme[themeId];\n    const restThemeProp = scopedTheme || initialTheme;\n    const {\n      colorSchemes = defaultColorSchemes,\n      components = defaultComponents,\n      cssVarPrefix\n    } = restThemeProp;\n    const joinedColorSchemes = Object.keys(colorSchemes).filter(k => !!colorSchemes[k]).join(',');\n    const allColorSchemes = React.useMemo(() => joinedColorSchemes.split(','), [joinedColorSchemes]);\n    const defaultLightColorScheme = typeof defaultColorScheme === 'string' ? defaultColorScheme : defaultColorScheme.light;\n    const defaultDarkColorScheme = typeof defaultColorScheme === 'string' ? defaultColorScheme : defaultColorScheme.dark;\n    const defaultMode = colorSchemes[defaultLightColorScheme] && colorSchemes[defaultDarkColorScheme] ? initialMode : ((_colorSchemes$restThe = colorSchemes[restThemeProp.defaultColorScheme]) === null || _colorSchemes$restThe === void 0 || (_colorSchemes$restThe = _colorSchemes$restThe.palette) === null || _colorSchemes$restThe === void 0 ? void 0 : _colorSchemes$restThe.mode) || ((_restThemeProp$palett = restThemeProp.palette) === null || _restThemeProp$palett === void 0 ? void 0 : _restThemeProp$palett.mode);\n\n    // 1. Get the data about the `mode`, `colorScheme`, and setter functions.\n    const {\n      mode: stateMode,\n      setMode,\n      systemMode,\n      lightColorScheme,\n      darkColorScheme,\n      colorScheme: stateColorScheme,\n      setColorScheme\n    } = useCurrentColorScheme({\n      supportedColorSchemes: allColorSchemes,\n      defaultLightColorScheme,\n      defaultDarkColorScheme,\n      modeStorageKey,\n      colorSchemeStorageKey,\n      defaultMode,\n      storageManager,\n      storageWindow,\n      noSsr\n    });\n    let mode = stateMode;\n    let colorScheme = stateColorScheme;\n    if (nested) {\n      mode = ctx.mode;\n      colorScheme = ctx.colorScheme;\n    }\n    if (process.env.NODE_ENV !== 'production') {\n      if (forceThemeRerender && !restThemeProp.vars) {\n        console.warn(['MUI: The `forceThemeRerender` prop should only be used with CSS theme variables.', 'Note that it will slow down the app when changing between modes, so only do this when you cannot find a better solution.'].join('\\n'));\n      }\n    }\n\n    // `colorScheme` is undefined on the server and hydration phase\n    let calculatedColorScheme = colorScheme || restThemeProp.defaultColorScheme;\n    if (restThemeProp.vars && !forceThemeRerender) {\n      calculatedColorScheme = restThemeProp.defaultColorScheme;\n    }\n    const memoTheme = React.useMemo(() => {\n      var _restThemeProp$genera;\n      // 2. get the `vars` object that refers to the CSS custom properties\n      const themeVars = ((_restThemeProp$genera = restThemeProp.generateThemeVars) === null || _restThemeProp$genera === void 0 ? void 0 : _restThemeProp$genera.call(restThemeProp)) || restThemeProp.vars;\n\n      // 3. Start composing the theme object\n      const theme = _objectSpread(_objectSpread({}, restThemeProp), {}, {\n        components,\n        colorSchemes,\n        cssVarPrefix,\n        vars: themeVars\n      });\n      if (typeof theme.generateSpacing === 'function') {\n        theme.spacing = theme.generateSpacing();\n      }\n\n      // 4. Resolve the color scheme and merge it to the theme\n      if (calculatedColorScheme) {\n        const scheme = colorSchemes[calculatedColorScheme];\n        if (scheme && typeof scheme === 'object') {\n          // 4.1 Merge the selected color scheme to the theme\n          Object.keys(scheme).forEach(schemeKey => {\n            if (scheme[schemeKey] && typeof scheme[schemeKey] === 'object') {\n              // shallow merge the 1st level structure of the theme.\n              theme[schemeKey] = _objectSpread(_objectSpread({}, theme[schemeKey]), scheme[schemeKey]);\n            } else {\n              theme[schemeKey] = scheme[schemeKey];\n            }\n          });\n        }\n      }\n      return resolveTheme ? resolveTheme(theme) : theme;\n    }, [restThemeProp, calculatedColorScheme, components, colorSchemes, cssVarPrefix]);\n\n    // 5. Declaring effects\n    // 5.1 Updates the selector value to use the current color scheme which tells CSS to use the proper stylesheet.\n    const colorSchemeSelector = restThemeProp.colorSchemeSelector;\n    useEnhancedEffect(() => {\n      if (colorScheme && colorSchemeNode && colorSchemeSelector && colorSchemeSelector !== 'media') {\n        const selector = colorSchemeSelector;\n        let rule = colorSchemeSelector;\n        if (selector === 'class') {\n          rule = \".%s\";\n        }\n        if (selector === 'data') {\n          rule = \"[data-%s]\";\n        }\n        if (selector !== null && selector !== void 0 && selector.startsWith('data-') && !selector.includes('%s')) {\n          // 'data-mui-color-scheme' -> '[data-mui-color-scheme=\"%s\"]'\n          rule = \"[\".concat(selector, \"=\\\"%s\\\"]\");\n        }\n        if (rule.startsWith('.')) {\n          colorSchemeNode.classList.remove(...allColorSchemes.map(scheme => rule.substring(1).replace('%s', scheme)));\n          colorSchemeNode.classList.add(rule.substring(1).replace('%s', colorScheme));\n        } else {\n          const matches = rule.replace('%s', colorScheme).match(/\\[([^\\]]+)\\]/);\n          if (matches) {\n            const [attr, value] = matches[1].split('=');\n            if (!value) {\n              // for attributes like `data-theme-dark`, `data-theme-light`\n              // remove all the existing data attributes before setting the new one\n              allColorSchemes.forEach(scheme => {\n                colorSchemeNode.removeAttribute(attr.replace(colorScheme, scheme));\n              });\n            }\n            colorSchemeNode.setAttribute(attr, value ? value.replace(/\"|'/g, '') : '');\n          } else {\n            colorSchemeNode.setAttribute(rule, colorScheme);\n          }\n        }\n      }\n    }, [colorScheme, colorSchemeSelector, colorSchemeNode, allColorSchemes]);\n\n    // 5.2 Remove the CSS transition when color scheme changes to create instant experience.\n    // credit: https://github.com/pacocoursey/next-themes/blob/b5c2bad50de2d61ad7b52a9c5cdc801a78507d7a/index.tsx#L313\n    React.useEffect(() => {\n      let timer;\n      if (disableTransitionOnChange && hasMounted.current && documentNode) {\n        const css = documentNode.createElement('style');\n        css.appendChild(documentNode.createTextNode(DISABLE_CSS_TRANSITION));\n        documentNode.head.appendChild(css);\n\n        // Force browser repaint\n        (() => window.getComputedStyle(documentNode.body))();\n        timer = setTimeout(() => {\n          documentNode.head.removeChild(css);\n        }, 1);\n      }\n      return () => {\n        clearTimeout(timer);\n      };\n    }, [colorScheme, disableTransitionOnChange, documentNode]);\n    React.useEffect(() => {\n      hasMounted.current = true;\n      return () => {\n        hasMounted.current = false;\n      };\n    }, []);\n    const contextValue = React.useMemo(() => ({\n      allColorSchemes,\n      colorScheme,\n      darkColorScheme,\n      lightColorScheme,\n      mode,\n      setColorScheme,\n      setMode: process.env.NODE_ENV === 'production' ? setMode : newMode => {\n        if (memoTheme.colorSchemeSelector === 'media') {\n          console.error(['MUI: The `setMode` function has no effect if `colorSchemeSelector` is `media` (`media` is the default value).', 'To toggle the mode manually, please configure `colorSchemeSelector` to use a class or data attribute.', 'To learn more, visit https://mui.com/material-ui/customization/css-theme-variables/configuration/#toggling-dark-mode-manually'].join('\\n'));\n        }\n        setMode(newMode);\n      },\n      systemMode\n    }), [allColorSchemes, colorScheme, darkColorScheme, lightColorScheme, mode, setColorScheme, setMode, systemMode, memoTheme.colorSchemeSelector]);\n    let shouldGenerateStyleSheet = true;\n    if (disableStyleSheetGeneration || restThemeProp.cssVariables === false || nested && (upperTheme === null || upperTheme === void 0 ? void 0 : upperTheme.cssVarPrefix) === cssVarPrefix) {\n      shouldGenerateStyleSheet = false;\n    }\n    const element = /*#__PURE__*/_jsxs(React.Fragment, {\n      children: [/*#__PURE__*/_jsx(ThemeProvider, {\n        themeId: scopedTheme ? themeId : undefined,\n        theme: memoTheme,\n        children: children\n      }), shouldGenerateStyleSheet && /*#__PURE__*/_jsx(GlobalStyles, {\n        styles: ((_memoTheme$generateSt = memoTheme.generateStyleSheets) === null || _memoTheme$generateSt === void 0 ? void 0 : _memoTheme$generateSt.call(memoTheme)) || []\n      })]\n    });\n    if (nested) {\n      return element;\n    }\n    return /*#__PURE__*/_jsx(ColorSchemeContext.Provider, {\n      value: contextValue,\n      children: element\n    });\n  }\n  process.env.NODE_ENV !== \"production\" ? CssVarsProvider.propTypes = {\n    /**\n     * The component tree.\n     */\n    children: PropTypes.node,\n    /**\n     * The node used to attach the color-scheme attribute\n     */\n    colorSchemeNode: PropTypes.any,\n    /**\n     * localStorage key used to store `colorScheme`\n     */\n    colorSchemeStorageKey: PropTypes.string,\n    /**\n     * The default mode when the storage is empty,\n     * require the theme to have `colorSchemes` with light and dark.\n     */\n    defaultMode: PropTypes.string,\n    /**\n     * If `true`, the provider creates its own context and generate stylesheet as if it is a root `CssVarsProvider`.\n     */\n    disableNestedContext: PropTypes.bool,\n    /**\n     * If `true`, the style sheet won't be generated.\n     *\n     * This is useful for controlling nested CssVarsProvider behavior.\n     */\n    disableStyleSheetGeneration: PropTypes.bool,\n    /**\n     * Disable CSS transitions when switching between modes or color schemes.\n     */\n    disableTransitionOnChange: PropTypes.bool,\n    /**\n     * The document to attach the attribute to.\n     */\n    documentNode: PropTypes.any,\n    /**\n     * If `true`, theme values are recalculated when the mode changes.\n     */\n    forceThemeRerender: PropTypes.bool,\n    /**\n     * The key in the local storage used to store current color scheme.\n     */\n    modeStorageKey: PropTypes.string,\n    /**\n     * If `true`, the mode will be the same value as the storage without an extra rerendering after the hydration.\n     * You should use this option in conjuction with `InitColorSchemeScript` component.\n     */\n    noSsr: PropTypes.bool,\n    /**\n     * The storage manager to be used for storing the mode and color scheme\n     * @default using `window.localStorage`\n     */\n    storageManager: PropTypes.func,\n    /**\n     * The window that attaches the 'storage' event listener.\n     * @default window\n     */\n    storageWindow: PropTypes.any,\n    /**\n     * The calculated theme object that will be passed through context.\n     */\n    theme: PropTypes.object\n  } : void 0;\n  const defaultLightColorScheme = typeof defaultColorScheme === 'string' ? defaultColorScheme : defaultColorScheme.light;\n  const defaultDarkColorScheme = typeof defaultColorScheme === 'string' ? defaultColorScheme : defaultColorScheme.dark;\n  const getInitColorSchemeScript = params => InitColorSchemeScript(_objectSpread({\n    colorSchemeStorageKey: defaultColorSchemeStorageKey,\n    defaultLightColorScheme,\n    defaultDarkColorScheme,\n    modeStorageKey: defaultModeStorageKey\n  }, params));\n  return {\n    CssVarsProvider,\n    useColorScheme,\n    getInitColorSchemeScript\n  };\n}", "map": {"version": 3, "names": ["_objectSpread", "React", "PropTypes", "GlobalStyles", "useTheme", "muiUseTheme", "useEnhancedEffect", "ThemeProvider", "InitColorSchemeScript", "DEFAULT_COLOR_SCHEME_STORAGE_KEY", "DEFAULT_MODE_STORAGE_KEY", "useCurrentColorScheme", "jsx", "_jsx", "jsxs", "_jsxs", "DISABLE_CSS_TRANSITION", "createCssVarsProvider", "options", "themeId", "theme", "defaultTheme", "modeStorageKey", "defaultModeStorageKey", "colorSchemeStorageKey", "defaultColorSchemeStorageKey", "disableTransitionOnChange", "designSystemTransitionOnChange", "defaultColorScheme", "resolveTheme", "defaultContext", "allColorSchemes", "colorScheme", "undefined", "darkColorScheme", "lightColorScheme", "mode", "setColorScheme", "setMode", "systemMode", "ColorSchemeContext", "createContext", "process", "env", "NODE_ENV", "displayName", "useColorScheme", "useContext", "defaultColorSchemes", "defaultComponents", "CssVarsProvider", "props", "_colorSchemes$restThe", "_restThemeProp$palett", "_memoTheme$generateSt", "children", "themeProp", "storageManager", "storageWindow", "window", "documentNode", "document", "colorSchemeNode", "documentElement", "disableNestedContext", "disableStyleSheetGeneration", "defaultMode", "initialMode", "forceThemeRerender", "noSsr", "hasMounted", "useRef", "upperTheme", "ctx", "nested", "initialTheme", "useMemo", "scopedTheme", "restThemeProp", "colorSchemes", "components", "cssVarPrefix", "joinedColorSchemes", "Object", "keys", "filter", "k", "join", "split", "defaultLightColorScheme", "light", "defaultDarkColorScheme", "dark", "palette", "stateMode", "stateColorScheme", "supportedColorSchemes", "vars", "console", "warn", "calculatedColorScheme", "memoTheme", "_restThemeProp$genera", "themeVars", "generateThemeVars", "call", "generateSpacing", "spacing", "scheme", "for<PERSON>ach", "<PERSON><PERSON>ey", "colorSchemeSelector", "selector", "rule", "startsWith", "includes", "concat", "classList", "remove", "map", "substring", "replace", "add", "matches", "match", "attr", "value", "removeAttribute", "setAttribute", "useEffect", "timer", "current", "css", "createElement", "append<PERSON><PERSON><PERSON>", "createTextNode", "head", "getComputedStyle", "body", "setTimeout", "<PERSON><PERSON><PERSON><PERSON>", "clearTimeout", "contextValue", "newMode", "error", "shouldGenerateStyleSheet", "cssVariables", "element", "Fragment", "styles", "generateStyleSheets", "Provider", "propTypes", "node", "any", "string", "bool", "func", "object", "getInitColorSchemeScript", "params"], "sources": ["C:/Users/<USER>/OneDrive/ドキュメント/Projects/StudyHub/studyhub-app/node_modules/@mui/system/esm/cssVars/createCssVarsProvider.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport { GlobalStyles } from '@mui/styled-engine';\nimport { useTheme as muiUseTheme } from '@mui/private-theming';\nimport useEnhancedEffect from '@mui/utils/useEnhancedEffect';\nimport ThemeProvider from \"../ThemeProvider/index.js\";\nimport InitColorSchemeScript, { DEFAULT_COLOR_SCHEME_STORAGE_KEY, DEFAULT_MODE_STORAGE_KEY } from \"../InitColorSchemeScript/InitColorSchemeScript.js\";\nimport useCurrentColorScheme from \"./useCurrentColorScheme.js\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nexport const DISABLE_CSS_TRANSITION = '*{-webkit-transition:none!important;-moz-transition:none!important;-o-transition:none!important;-ms-transition:none!important;transition:none!important}';\nexport default function createCssVarsProvider(options) {\n  const {\n    themeId,\n    /**\n     * This `theme` object needs to follow a certain structure to\n     * be used correctly by the finel `CssVarsProvider`. It should have a\n     * `colorSchemes` key with the light and dark (and any other) palette.\n     * It should also ideally have a vars object created using `prepareCssVars`.\n     */\n    theme: defaultTheme = {},\n    modeStorageKey: defaultModeStorageKey = DEFAULT_MODE_STORAGE_KEY,\n    colorSchemeStorageKey: defaultColorSchemeStorageKey = DEFAULT_COLOR_SCHEME_STORAGE_KEY,\n    disableTransitionOnChange: designSystemTransitionOnChange = false,\n    defaultColorScheme,\n    resolveTheme\n  } = options;\n  const defaultContext = {\n    allColorSchemes: [],\n    colorScheme: undefined,\n    darkColorScheme: undefined,\n    lightColorScheme: undefined,\n    mode: undefined,\n    setColorScheme: () => {},\n    setMode: () => {},\n    systemMode: undefined\n  };\n  const ColorSchemeContext = /*#__PURE__*/React.createContext(undefined);\n  if (process.env.NODE_ENV !== 'production') {\n    ColorSchemeContext.displayName = 'ColorSchemeContext';\n  }\n  const useColorScheme = () => React.useContext(ColorSchemeContext) || defaultContext;\n  const defaultColorSchemes = {};\n  const defaultComponents = {};\n  function CssVarsProvider(props) {\n    const {\n      children,\n      theme: themeProp,\n      modeStorageKey = defaultModeStorageKey,\n      colorSchemeStorageKey = defaultColorSchemeStorageKey,\n      disableTransitionOnChange = designSystemTransitionOnChange,\n      storageManager,\n      storageWindow = typeof window === 'undefined' ? undefined : window,\n      documentNode = typeof document === 'undefined' ? undefined : document,\n      colorSchemeNode = typeof document === 'undefined' ? undefined : document.documentElement,\n      disableNestedContext = false,\n      disableStyleSheetGeneration = false,\n      defaultMode: initialMode = 'system',\n      forceThemeRerender = false,\n      noSsr\n    } = props;\n    const hasMounted = React.useRef(false);\n    const upperTheme = muiUseTheme();\n    const ctx = React.useContext(ColorSchemeContext);\n    const nested = !!ctx && !disableNestedContext;\n    const initialTheme = React.useMemo(() => {\n      if (themeProp) {\n        return themeProp;\n      }\n      return typeof defaultTheme === 'function' ? defaultTheme() : defaultTheme;\n    }, [themeProp]);\n    const scopedTheme = initialTheme[themeId];\n    const restThemeProp = scopedTheme || initialTheme;\n    const {\n      colorSchemes = defaultColorSchemes,\n      components = defaultComponents,\n      cssVarPrefix\n    } = restThemeProp;\n    const joinedColorSchemes = Object.keys(colorSchemes).filter(k => !!colorSchemes[k]).join(',');\n    const allColorSchemes = React.useMemo(() => joinedColorSchemes.split(','), [joinedColorSchemes]);\n    const defaultLightColorScheme = typeof defaultColorScheme === 'string' ? defaultColorScheme : defaultColorScheme.light;\n    const defaultDarkColorScheme = typeof defaultColorScheme === 'string' ? defaultColorScheme : defaultColorScheme.dark;\n    const defaultMode = colorSchemes[defaultLightColorScheme] && colorSchemes[defaultDarkColorScheme] ? initialMode : colorSchemes[restThemeProp.defaultColorScheme]?.palette?.mode || restThemeProp.palette?.mode;\n\n    // 1. Get the data about the `mode`, `colorScheme`, and setter functions.\n    const {\n      mode: stateMode,\n      setMode,\n      systemMode,\n      lightColorScheme,\n      darkColorScheme,\n      colorScheme: stateColorScheme,\n      setColorScheme\n    } = useCurrentColorScheme({\n      supportedColorSchemes: allColorSchemes,\n      defaultLightColorScheme,\n      defaultDarkColorScheme,\n      modeStorageKey,\n      colorSchemeStorageKey,\n      defaultMode,\n      storageManager,\n      storageWindow,\n      noSsr\n    });\n    let mode = stateMode;\n    let colorScheme = stateColorScheme;\n    if (nested) {\n      mode = ctx.mode;\n      colorScheme = ctx.colorScheme;\n    }\n    if (process.env.NODE_ENV !== 'production') {\n      if (forceThemeRerender && !restThemeProp.vars) {\n        console.warn(['MUI: The `forceThemeRerender` prop should only be used with CSS theme variables.', 'Note that it will slow down the app when changing between modes, so only do this when you cannot find a better solution.'].join('\\n'));\n      }\n    }\n\n    // `colorScheme` is undefined on the server and hydration phase\n    let calculatedColorScheme = colorScheme || restThemeProp.defaultColorScheme;\n    if (restThemeProp.vars && !forceThemeRerender) {\n      calculatedColorScheme = restThemeProp.defaultColorScheme;\n    }\n    const memoTheme = React.useMemo(() => {\n      // 2. get the `vars` object that refers to the CSS custom properties\n      const themeVars = restThemeProp.generateThemeVars?.() || restThemeProp.vars;\n\n      // 3. Start composing the theme object\n      const theme = {\n        ...restThemeProp,\n        components,\n        colorSchemes,\n        cssVarPrefix,\n        vars: themeVars\n      };\n      if (typeof theme.generateSpacing === 'function') {\n        theme.spacing = theme.generateSpacing();\n      }\n\n      // 4. Resolve the color scheme and merge it to the theme\n      if (calculatedColorScheme) {\n        const scheme = colorSchemes[calculatedColorScheme];\n        if (scheme && typeof scheme === 'object') {\n          // 4.1 Merge the selected color scheme to the theme\n          Object.keys(scheme).forEach(schemeKey => {\n            if (scheme[schemeKey] && typeof scheme[schemeKey] === 'object') {\n              // shallow merge the 1st level structure of the theme.\n              theme[schemeKey] = {\n                ...theme[schemeKey],\n                ...scheme[schemeKey]\n              };\n            } else {\n              theme[schemeKey] = scheme[schemeKey];\n            }\n          });\n        }\n      }\n      return resolveTheme ? resolveTheme(theme) : theme;\n    }, [restThemeProp, calculatedColorScheme, components, colorSchemes, cssVarPrefix]);\n\n    // 5. Declaring effects\n    // 5.1 Updates the selector value to use the current color scheme which tells CSS to use the proper stylesheet.\n    const colorSchemeSelector = restThemeProp.colorSchemeSelector;\n    useEnhancedEffect(() => {\n      if (colorScheme && colorSchemeNode && colorSchemeSelector && colorSchemeSelector !== 'media') {\n        const selector = colorSchemeSelector;\n        let rule = colorSchemeSelector;\n        if (selector === 'class') {\n          rule = `.%s`;\n        }\n        if (selector === 'data') {\n          rule = `[data-%s]`;\n        }\n        if (selector?.startsWith('data-') && !selector.includes('%s')) {\n          // 'data-mui-color-scheme' -> '[data-mui-color-scheme=\"%s\"]'\n          rule = `[${selector}=\"%s\"]`;\n        }\n        if (rule.startsWith('.')) {\n          colorSchemeNode.classList.remove(...allColorSchemes.map(scheme => rule.substring(1).replace('%s', scheme)));\n          colorSchemeNode.classList.add(rule.substring(1).replace('%s', colorScheme));\n        } else {\n          const matches = rule.replace('%s', colorScheme).match(/\\[([^\\]]+)\\]/);\n          if (matches) {\n            const [attr, value] = matches[1].split('=');\n            if (!value) {\n              // for attributes like `data-theme-dark`, `data-theme-light`\n              // remove all the existing data attributes before setting the new one\n              allColorSchemes.forEach(scheme => {\n                colorSchemeNode.removeAttribute(attr.replace(colorScheme, scheme));\n              });\n            }\n            colorSchemeNode.setAttribute(attr, value ? value.replace(/\"|'/g, '') : '');\n          } else {\n            colorSchemeNode.setAttribute(rule, colorScheme);\n          }\n        }\n      }\n    }, [colorScheme, colorSchemeSelector, colorSchemeNode, allColorSchemes]);\n\n    // 5.2 Remove the CSS transition when color scheme changes to create instant experience.\n    // credit: https://github.com/pacocoursey/next-themes/blob/b5c2bad50de2d61ad7b52a9c5cdc801a78507d7a/index.tsx#L313\n    React.useEffect(() => {\n      let timer;\n      if (disableTransitionOnChange && hasMounted.current && documentNode) {\n        const css = documentNode.createElement('style');\n        css.appendChild(documentNode.createTextNode(DISABLE_CSS_TRANSITION));\n        documentNode.head.appendChild(css);\n\n        // Force browser repaint\n        (() => window.getComputedStyle(documentNode.body))();\n        timer = setTimeout(() => {\n          documentNode.head.removeChild(css);\n        }, 1);\n      }\n      return () => {\n        clearTimeout(timer);\n      };\n    }, [colorScheme, disableTransitionOnChange, documentNode]);\n    React.useEffect(() => {\n      hasMounted.current = true;\n      return () => {\n        hasMounted.current = false;\n      };\n    }, []);\n    const contextValue = React.useMemo(() => ({\n      allColorSchemes,\n      colorScheme,\n      darkColorScheme,\n      lightColorScheme,\n      mode,\n      setColorScheme,\n      setMode: process.env.NODE_ENV === 'production' ? setMode : newMode => {\n        if (memoTheme.colorSchemeSelector === 'media') {\n          console.error(['MUI: The `setMode` function has no effect if `colorSchemeSelector` is `media` (`media` is the default value).', 'To toggle the mode manually, please configure `colorSchemeSelector` to use a class or data attribute.', 'To learn more, visit https://mui.com/material-ui/customization/css-theme-variables/configuration/#toggling-dark-mode-manually'].join('\\n'));\n        }\n        setMode(newMode);\n      },\n      systemMode\n    }), [allColorSchemes, colorScheme, darkColorScheme, lightColorScheme, mode, setColorScheme, setMode, systemMode, memoTheme.colorSchemeSelector]);\n    let shouldGenerateStyleSheet = true;\n    if (disableStyleSheetGeneration || restThemeProp.cssVariables === false || nested && upperTheme?.cssVarPrefix === cssVarPrefix) {\n      shouldGenerateStyleSheet = false;\n    }\n    const element = /*#__PURE__*/_jsxs(React.Fragment, {\n      children: [/*#__PURE__*/_jsx(ThemeProvider, {\n        themeId: scopedTheme ? themeId : undefined,\n        theme: memoTheme,\n        children: children\n      }), shouldGenerateStyleSheet && /*#__PURE__*/_jsx(GlobalStyles, {\n        styles: memoTheme.generateStyleSheets?.() || []\n      })]\n    });\n    if (nested) {\n      return element;\n    }\n    return /*#__PURE__*/_jsx(ColorSchemeContext.Provider, {\n      value: contextValue,\n      children: element\n    });\n  }\n  process.env.NODE_ENV !== \"production\" ? CssVarsProvider.propTypes = {\n    /**\n     * The component tree.\n     */\n    children: PropTypes.node,\n    /**\n     * The node used to attach the color-scheme attribute\n     */\n    colorSchemeNode: PropTypes.any,\n    /**\n     * localStorage key used to store `colorScheme`\n     */\n    colorSchemeStorageKey: PropTypes.string,\n    /**\n     * The default mode when the storage is empty,\n     * require the theme to have `colorSchemes` with light and dark.\n     */\n    defaultMode: PropTypes.string,\n    /**\n     * If `true`, the provider creates its own context and generate stylesheet as if it is a root `CssVarsProvider`.\n     */\n    disableNestedContext: PropTypes.bool,\n    /**\n     * If `true`, the style sheet won't be generated.\n     *\n     * This is useful for controlling nested CssVarsProvider behavior.\n     */\n    disableStyleSheetGeneration: PropTypes.bool,\n    /**\n     * Disable CSS transitions when switching between modes or color schemes.\n     */\n    disableTransitionOnChange: PropTypes.bool,\n    /**\n     * The document to attach the attribute to.\n     */\n    documentNode: PropTypes.any,\n    /**\n     * If `true`, theme values are recalculated when the mode changes.\n     */\n    forceThemeRerender: PropTypes.bool,\n    /**\n     * The key in the local storage used to store current color scheme.\n     */\n    modeStorageKey: PropTypes.string,\n    /**\n     * If `true`, the mode will be the same value as the storage without an extra rerendering after the hydration.\n     * You should use this option in conjuction with `InitColorSchemeScript` component.\n     */\n    noSsr: PropTypes.bool,\n    /**\n     * The storage manager to be used for storing the mode and color scheme\n     * @default using `window.localStorage`\n     */\n    storageManager: PropTypes.func,\n    /**\n     * The window that attaches the 'storage' event listener.\n     * @default window\n     */\n    storageWindow: PropTypes.any,\n    /**\n     * The calculated theme object that will be passed through context.\n     */\n    theme: PropTypes.object\n  } : void 0;\n  const defaultLightColorScheme = typeof defaultColorScheme === 'string' ? defaultColorScheme : defaultColorScheme.light;\n  const defaultDarkColorScheme = typeof defaultColorScheme === 'string' ? defaultColorScheme : defaultColorScheme.dark;\n  const getInitColorSchemeScript = params => InitColorSchemeScript({\n    colorSchemeStorageKey: defaultColorSchemeStorageKey,\n    defaultLightColorScheme,\n    defaultDarkColorScheme,\n    modeStorageKey: defaultModeStorageKey,\n    ...params\n  });\n  return {\n    CssVarsProvider,\n    useColorScheme,\n    getInitColorSchemeScript\n  };\n}"], "mappings": "AAAA,YAAY;;AAAC,OAAAA,aAAA;AAEb,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,SAASC,YAAY,QAAQ,oBAAoB;AACjD,SAASC,QAAQ,IAAIC,WAAW,QAAQ,sBAAsB;AAC9D,OAAOC,iBAAiB,MAAM,8BAA8B;AAC5D,OAAOC,aAAa,MAAM,2BAA2B;AACrD,OAAOC,qBAAqB,IAAIC,gCAAgC,EAAEC,wBAAwB,QAAQ,mDAAmD;AACrJ,OAAOC,qBAAqB,MAAM,4BAA4B;AAC9D,SAASC,GAAG,IAAIC,IAAI,EAAEC,IAAI,IAAIC,KAAK,QAAQ,mBAAmB;AAC9D,OAAO,MAAMC,sBAAsB,GAAG,0JAA0J;AAChM,eAAe,SAASC,qBAAqBA,CAACC,OAAO,EAAE;EACrD,MAAM;IACJC,OAAO;IACP;AACJ;AACA;AACA;AACA;AACA;IACIC,KAAK,EAAEC,YAAY,GAAG,CAAC,CAAC;IACxBC,cAAc,EAAEC,qBAAqB,GAAGb,wBAAwB;IAChEc,qBAAqB,EAAEC,4BAA4B,GAAGhB,gCAAgC;IACtFiB,yBAAyB,EAAEC,8BAA8B,GAAG,KAAK;IACjEC,kBAAkB;IAClBC;EACF,CAAC,GAAGX,OAAO;EACX,MAAMY,cAAc,GAAG;IACrBC,eAAe,EAAE,EAAE;IACnBC,WAAW,EAAEC,SAAS;IACtBC,eAAe,EAAED,SAAS;IAC1BE,gBAAgB,EAAEF,SAAS;IAC3BG,IAAI,EAAEH,SAAS;IACfI,cAAc,EAAEA,CAAA,KAAM,CAAC,CAAC;IACxBC,OAAO,EAAEA,CAAA,KAAM,CAAC,CAAC;IACjBC,UAAU,EAAEN;EACd,CAAC;EACD,MAAMO,kBAAkB,GAAG,aAAavC,KAAK,CAACwC,aAAa,CAACR,SAAS,CAAC;EACtE,IAAIS,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzCJ,kBAAkB,CAACK,WAAW,GAAG,oBAAoB;EACvD;EACA,MAAMC,cAAc,GAAGA,CAAA,KAAM7C,KAAK,CAAC8C,UAAU,CAACP,kBAAkB,CAAC,IAAIV,cAAc;EACnF,MAAMkB,mBAAmB,GAAG,CAAC,CAAC;EAC9B,MAAMC,iBAAiB,GAAG,CAAC,CAAC;EAC5B,SAASC,eAAeA,CAACC,KAAK,EAAE;IAAA,IAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA;IAC9B,MAAM;MACJC,QAAQ;MACRnC,KAAK,EAAEoC,SAAS;MAChBlC,cAAc,GAAGC,qBAAqB;MACtCC,qBAAqB,GAAGC,4BAA4B;MACpDC,yBAAyB,GAAGC,8BAA8B;MAC1D8B,cAAc;MACdC,aAAa,GAAG,OAAOC,MAAM,KAAK,WAAW,GAAG1B,SAAS,GAAG0B,MAAM;MAClEC,YAAY,GAAG,OAAOC,QAAQ,KAAK,WAAW,GAAG5B,SAAS,GAAG4B,QAAQ;MACrEC,eAAe,GAAG,OAAOD,QAAQ,KAAK,WAAW,GAAG5B,SAAS,GAAG4B,QAAQ,CAACE,eAAe;MACxFC,oBAAoB,GAAG,KAAK;MAC5BC,2BAA2B,GAAG,KAAK;MACnCC,WAAW,EAAEC,WAAW,GAAG,QAAQ;MACnCC,kBAAkB,GAAG,KAAK;MAC1BC;IACF,CAAC,GAAGlB,KAAK;IACT,MAAMmB,UAAU,GAAGrE,KAAK,CAACsE,MAAM,CAAC,KAAK,CAAC;IACtC,MAAMC,UAAU,GAAGnE,WAAW,CAAC,CAAC;IAChC,MAAMoE,GAAG,GAAGxE,KAAK,CAAC8C,UAAU,CAACP,kBAAkB,CAAC;IAChD,MAAMkC,MAAM,GAAG,CAAC,CAACD,GAAG,IAAI,CAACT,oBAAoB;IAC7C,MAAMW,YAAY,GAAG1E,KAAK,CAAC2E,OAAO,CAAC,MAAM;MACvC,IAAIpB,SAAS,EAAE;QACb,OAAOA,SAAS;MAClB;MACA,OAAO,OAAOnC,YAAY,KAAK,UAAU,GAAGA,YAAY,CAAC,CAAC,GAAGA,YAAY;IAC3E,CAAC,EAAE,CAACmC,SAAS,CAAC,CAAC;IACf,MAAMqB,WAAW,GAAGF,YAAY,CAACxD,OAAO,CAAC;IACzC,MAAM2D,aAAa,GAAGD,WAAW,IAAIF,YAAY;IACjD,MAAM;MACJI,YAAY,GAAG/B,mBAAmB;MAClCgC,UAAU,GAAG/B,iBAAiB;MAC9BgC;IACF,CAAC,GAAGH,aAAa;IACjB,MAAMI,kBAAkB,GAAGC,MAAM,CAACC,IAAI,CAACL,YAAY,CAAC,CAACM,MAAM,CAACC,CAAC,IAAI,CAAC,CAACP,YAAY,CAACO,CAAC,CAAC,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC;IAC7F,MAAMxD,eAAe,GAAG9B,KAAK,CAAC2E,OAAO,CAAC,MAAMM,kBAAkB,CAACM,KAAK,CAAC,GAAG,CAAC,EAAE,CAACN,kBAAkB,CAAC,CAAC;IAChG,MAAMO,uBAAuB,GAAG,OAAO7D,kBAAkB,KAAK,QAAQ,GAAGA,kBAAkB,GAAGA,kBAAkB,CAAC8D,KAAK;IACtH,MAAMC,sBAAsB,GAAG,OAAO/D,kBAAkB,KAAK,QAAQ,GAAGA,kBAAkB,GAAGA,kBAAkB,CAACgE,IAAI;IACpH,MAAM1B,WAAW,GAAGa,YAAY,CAACU,uBAAuB,CAAC,IAAIV,YAAY,CAACY,sBAAsB,CAAC,GAAGxB,WAAW,GAAG,EAAAf,qBAAA,GAAA2B,YAAY,CAACD,aAAa,CAAClD,kBAAkB,CAAC,cAAAwB,qBAAA,gBAAAA,qBAAA,GAA9CA,qBAAA,CAAgDyC,OAAO,cAAAzC,qBAAA,uBAAvDA,qBAAA,CAAyDhB,IAAI,OAAAiB,qBAAA,GAAIyB,aAAa,CAACe,OAAO,cAAAxC,qBAAA,uBAArBA,qBAAA,CAAuBjB,IAAI;;IAE9M;IACA,MAAM;MACJA,IAAI,EAAE0D,SAAS;MACfxD,OAAO;MACPC,UAAU;MACVJ,gBAAgB;MAChBD,eAAe;MACfF,WAAW,EAAE+D,gBAAgB;MAC7B1D;IACF,CAAC,GAAG1B,qBAAqB,CAAC;MACxBqF,qBAAqB,EAAEjE,eAAe;MACtC0D,uBAAuB;MACvBE,sBAAsB;MACtBrE,cAAc;MACdE,qBAAqB;MACrB0C,WAAW;MACXT,cAAc;MACdC,aAAa;MACbW;IACF,CAAC,CAAC;IACF,IAAIjC,IAAI,GAAG0D,SAAS;IACpB,IAAI9D,WAAW,GAAG+D,gBAAgB;IAClC,IAAIrB,MAAM,EAAE;MACVtC,IAAI,GAAGqC,GAAG,CAACrC,IAAI;MACfJ,WAAW,GAAGyC,GAAG,CAACzC,WAAW;IAC/B;IACA,IAAIU,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACzC,IAAIwB,kBAAkB,IAAI,CAACU,aAAa,CAACmB,IAAI,EAAE;QAC7CC,OAAO,CAACC,IAAI,CAAC,CAAC,kFAAkF,EAAE,0HAA0H,CAAC,CAACZ,IAAI,CAAC,IAAI,CAAC,CAAC;MAC3O;IACF;;IAEA;IACA,IAAIa,qBAAqB,GAAGpE,WAAW,IAAI8C,aAAa,CAAClD,kBAAkB;IAC3E,IAAIkD,aAAa,CAACmB,IAAI,IAAI,CAAC7B,kBAAkB,EAAE;MAC7CgC,qBAAqB,GAAGtB,aAAa,CAAClD,kBAAkB;IAC1D;IACA,MAAMyE,SAAS,GAAGpG,KAAK,CAAC2E,OAAO,CAAC,MAAM;MAAA,IAAA0B,qBAAA;MACpC;MACA,MAAMC,SAAS,GAAG,EAAAD,qBAAA,GAAAxB,aAAa,CAAC0B,iBAAiB,cAAAF,qBAAA,uBAA/BA,qBAAA,CAAAG,IAAA,CAAA3B,aAAkC,CAAC,KAAIA,aAAa,CAACmB,IAAI;;MAE3E;MACA,MAAM7E,KAAK,GAAApB,aAAA,CAAAA,aAAA,KACN8E,aAAa;QAChBE,UAAU;QACVD,YAAY;QACZE,YAAY;QACZgB,IAAI,EAAEM;MAAS,EAChB;MACD,IAAI,OAAOnF,KAAK,CAACsF,eAAe,KAAK,UAAU,EAAE;QAC/CtF,KAAK,CAACuF,OAAO,GAAGvF,KAAK,CAACsF,eAAe,CAAC,CAAC;MACzC;;MAEA;MACA,IAAIN,qBAAqB,EAAE;QACzB,MAAMQ,MAAM,GAAG7B,YAAY,CAACqB,qBAAqB,CAAC;QAClD,IAAIQ,MAAM,IAAI,OAAOA,MAAM,KAAK,QAAQ,EAAE;UACxC;UACAzB,MAAM,CAACC,IAAI,CAACwB,MAAM,CAAC,CAACC,OAAO,CAACC,SAAS,IAAI;YACvC,IAAIF,MAAM,CAACE,SAAS,CAAC,IAAI,OAAOF,MAAM,CAACE,SAAS,CAAC,KAAK,QAAQ,EAAE;cAC9D;cACA1F,KAAK,CAAC0F,SAAS,CAAC,GAAA9G,aAAA,CAAAA,aAAA,KACXoB,KAAK,CAAC0F,SAAS,CAAC,GAChBF,MAAM,CAACE,SAAS,CAAC,CACrB;YACH,CAAC,MAAM;cACL1F,KAAK,CAAC0F,SAAS,CAAC,GAAGF,MAAM,CAACE,SAAS,CAAC;YACtC;UACF,CAAC,CAAC;QACJ;MACF;MACA,OAAOjF,YAAY,GAAGA,YAAY,CAACT,KAAK,CAAC,GAAGA,KAAK;IACnD,CAAC,EAAE,CAAC0D,aAAa,EAAEsB,qBAAqB,EAAEpB,UAAU,EAAED,YAAY,EAAEE,YAAY,CAAC,CAAC;;IAElF;IACA;IACA,MAAM8B,mBAAmB,GAAGjC,aAAa,CAACiC,mBAAmB;IAC7DzG,iBAAiB,CAAC,MAAM;MACtB,IAAI0B,WAAW,IAAI8B,eAAe,IAAIiD,mBAAmB,IAAIA,mBAAmB,KAAK,OAAO,EAAE;QAC5F,MAAMC,QAAQ,GAAGD,mBAAmB;QACpC,IAAIE,IAAI,GAAGF,mBAAmB;QAC9B,IAAIC,QAAQ,KAAK,OAAO,EAAE;UACxBC,IAAI,QAAQ;QACd;QACA,IAAID,QAAQ,KAAK,MAAM,EAAE;UACvBC,IAAI,cAAc;QACpB;QACA,IAAID,QAAQ,aAARA,QAAQ,eAARA,QAAQ,CAAEE,UAAU,CAAC,OAAO,CAAC,IAAI,CAACF,QAAQ,CAACG,QAAQ,CAAC,IAAI,CAAC,EAAE;UAC7D;UACAF,IAAI,OAAAG,MAAA,CAAOJ,QAAQ,aAAQ;QAC7B;QACA,IAAIC,IAAI,CAACC,UAAU,CAAC,GAAG,CAAC,EAAE;UACxBpD,eAAe,CAACuD,SAAS,CAACC,MAAM,CAAC,GAAGvF,eAAe,CAACwF,GAAG,CAACX,MAAM,IAAIK,IAAI,CAACO,SAAS,CAAC,CAAC,CAAC,CAACC,OAAO,CAAC,IAAI,EAAEb,MAAM,CAAC,CAAC,CAAC;UAC3G9C,eAAe,CAACuD,SAAS,CAACK,GAAG,CAACT,IAAI,CAACO,SAAS,CAAC,CAAC,CAAC,CAACC,OAAO,CAAC,IAAI,EAAEzF,WAAW,CAAC,CAAC;QAC7E,CAAC,MAAM;UACL,MAAM2F,OAAO,GAAGV,IAAI,CAACQ,OAAO,CAAC,IAAI,EAAEzF,WAAW,CAAC,CAAC4F,KAAK,CAAC,cAAc,CAAC;UACrE,IAAID,OAAO,EAAE;YACX,MAAM,CAACE,IAAI,EAAEC,KAAK,CAAC,GAAGH,OAAO,CAAC,CAAC,CAAC,CAACnC,KAAK,CAAC,GAAG,CAAC;YAC3C,IAAI,CAACsC,KAAK,EAAE;cACV;cACA;cACA/F,eAAe,CAAC8E,OAAO,CAACD,MAAM,IAAI;gBAChC9C,eAAe,CAACiE,eAAe,CAACF,IAAI,CAACJ,OAAO,CAACzF,WAAW,EAAE4E,MAAM,CAAC,CAAC;cACpE,CAAC,CAAC;YACJ;YACA9C,eAAe,CAACkE,YAAY,CAACH,IAAI,EAAEC,KAAK,GAAGA,KAAK,CAACL,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,GAAG,EAAE,CAAC;UAC5E,CAAC,MAAM;YACL3D,eAAe,CAACkE,YAAY,CAACf,IAAI,EAAEjF,WAAW,CAAC;UACjD;QACF;MACF;IACF,CAAC,EAAE,CAACA,WAAW,EAAE+E,mBAAmB,EAAEjD,eAAe,EAAE/B,eAAe,CAAC,CAAC;;IAExE;IACA;IACA9B,KAAK,CAACgI,SAAS,CAAC,MAAM;MACpB,IAAIC,KAAK;MACT,IAAIxG,yBAAyB,IAAI4C,UAAU,CAAC6D,OAAO,IAAIvE,YAAY,EAAE;QACnE,MAAMwE,GAAG,GAAGxE,YAAY,CAACyE,aAAa,CAAC,OAAO,CAAC;QAC/CD,GAAG,CAACE,WAAW,CAAC1E,YAAY,CAAC2E,cAAc,CAACvH,sBAAsB,CAAC,CAAC;QACpE4C,YAAY,CAAC4E,IAAI,CAACF,WAAW,CAACF,GAAG,CAAC;;QAElC;QACA,CAAC,MAAMzE,MAAM,CAAC8E,gBAAgB,CAAC7E,YAAY,CAAC8E,IAAI,CAAC,EAAE,CAAC;QACpDR,KAAK,GAAGS,UAAU,CAAC,MAAM;UACvB/E,YAAY,CAAC4E,IAAI,CAACI,WAAW,CAACR,GAAG,CAAC;QACpC,CAAC,EAAE,CAAC,CAAC;MACP;MACA,OAAO,MAAM;QACXS,YAAY,CAACX,KAAK,CAAC;MACrB,CAAC;IACH,CAAC,EAAE,CAAClG,WAAW,EAAEN,yBAAyB,EAAEkC,YAAY,CAAC,CAAC;IAC1D3D,KAAK,CAACgI,SAAS,CAAC,MAAM;MACpB3D,UAAU,CAAC6D,OAAO,GAAG,IAAI;MACzB,OAAO,MAAM;QACX7D,UAAU,CAAC6D,OAAO,GAAG,KAAK;MAC5B,CAAC;IACH,CAAC,EAAE,EAAE,CAAC;IACN,MAAMW,YAAY,GAAG7I,KAAK,CAAC2E,OAAO,CAAC,OAAO;MACxC7C,eAAe;MACfC,WAAW;MACXE,eAAe;MACfC,gBAAgB;MAChBC,IAAI;MACJC,cAAc;MACdC,OAAO,EAAEI,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGN,OAAO,GAAGyG,OAAO,IAAI;QACpE,IAAI1C,SAAS,CAACU,mBAAmB,KAAK,OAAO,EAAE;UAC7Cb,OAAO,CAAC8C,KAAK,CAAC,CAAC,+GAA+G,EAAE,uGAAuG,EAAE,+HAA+H,CAAC,CAACzD,IAAI,CAAC,IAAI,CAAC,CAAC;QACvX;QACAjD,OAAO,CAACyG,OAAO,CAAC;MAClB,CAAC;MACDxG;IACF,CAAC,CAAC,EAAE,CAACR,eAAe,EAAEC,WAAW,EAAEE,eAAe,EAAEC,gBAAgB,EAAEC,IAAI,EAAEC,cAAc,EAAEC,OAAO,EAAEC,UAAU,EAAE8D,SAAS,CAACU,mBAAmB,CAAC,CAAC;IAChJ,IAAIkC,wBAAwB,GAAG,IAAI;IACnC,IAAIhF,2BAA2B,IAAIa,aAAa,CAACoE,YAAY,KAAK,KAAK,IAAIxE,MAAM,IAAI,CAAAF,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAES,YAAY,MAAKA,YAAY,EAAE;MAC9HgE,wBAAwB,GAAG,KAAK;IAClC;IACA,MAAME,OAAO,GAAG,aAAapI,KAAK,CAACd,KAAK,CAACmJ,QAAQ,EAAE;MACjD7F,QAAQ,EAAE,CAAC,aAAa1C,IAAI,CAACN,aAAa,EAAE;QAC1CY,OAAO,EAAE0D,WAAW,GAAG1D,OAAO,GAAGc,SAAS;QAC1Cb,KAAK,EAAEiF,SAAS;QAChB9C,QAAQ,EAAEA;MACZ,CAAC,CAAC,EAAE0F,wBAAwB,IAAI,aAAapI,IAAI,CAACV,YAAY,EAAE;QAC9DkJ,MAAM,EAAE,EAAA/F,qBAAA,GAAA+C,SAAS,CAACiD,mBAAmB,cAAAhG,qBAAA,uBAA7BA,qBAAA,CAAAmD,IAAA,CAAAJ,SAAgC,CAAC,KAAI;MAC/C,CAAC,CAAC;IACJ,CAAC,CAAC;IACF,IAAI3B,MAAM,EAAE;MACV,OAAOyE,OAAO;IAChB;IACA,OAAO,aAAatI,IAAI,CAAC2B,kBAAkB,CAAC+G,QAAQ,EAAE;MACpDzB,KAAK,EAAEgB,YAAY;MACnBvF,QAAQ,EAAE4F;IACZ,CAAC,CAAC;EACJ;EACAzG,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGM,eAAe,CAACsG,SAAS,GAAG;IAClE;AACJ;AACA;IACIjG,QAAQ,EAAErD,SAAS,CAACuJ,IAAI;IACxB;AACJ;AACA;IACI3F,eAAe,EAAE5D,SAAS,CAACwJ,GAAG;IAC9B;AACJ;AACA;IACIlI,qBAAqB,EAAEtB,SAAS,CAACyJ,MAAM;IACvC;AACJ;AACA;AACA;IACIzF,WAAW,EAAEhE,SAAS,CAACyJ,MAAM;IAC7B;AACJ;AACA;IACI3F,oBAAoB,EAAE9D,SAAS,CAAC0J,IAAI;IACpC;AACJ;AACA;AACA;AACA;IACI3F,2BAA2B,EAAE/D,SAAS,CAAC0J,IAAI;IAC3C;AACJ;AACA;IACIlI,yBAAyB,EAAExB,SAAS,CAAC0J,IAAI;IACzC;AACJ;AACA;IACIhG,YAAY,EAAE1D,SAAS,CAACwJ,GAAG;IAC3B;AACJ;AACA;IACItF,kBAAkB,EAAElE,SAAS,CAAC0J,IAAI;IAClC;AACJ;AACA;IACItI,cAAc,EAAEpB,SAAS,CAACyJ,MAAM;IAChC;AACJ;AACA;AACA;IACItF,KAAK,EAAEnE,SAAS,CAAC0J,IAAI;IACrB;AACJ;AACA;AACA;IACInG,cAAc,EAAEvD,SAAS,CAAC2J,IAAI;IAC9B;AACJ;AACA;AACA;IACInG,aAAa,EAAExD,SAAS,CAACwJ,GAAG;IAC5B;AACJ;AACA;IACItI,KAAK,EAAElB,SAAS,CAAC4J;EACnB,CAAC,GAAG,KAAK,CAAC;EACV,MAAMrE,uBAAuB,GAAG,OAAO7D,kBAAkB,KAAK,QAAQ,GAAGA,kBAAkB,GAAGA,kBAAkB,CAAC8D,KAAK;EACtH,MAAMC,sBAAsB,GAAG,OAAO/D,kBAAkB,KAAK,QAAQ,GAAGA,kBAAkB,GAAGA,kBAAkB,CAACgE,IAAI;EACpH,MAAMmE,wBAAwB,GAAGC,MAAM,IAAIxJ,qBAAqB,CAAAR,aAAA;IAC9DwB,qBAAqB,EAAEC,4BAA4B;IACnDgE,uBAAuB;IACvBE,sBAAsB;IACtBrE,cAAc,EAAEC;EAAqB,GAClCyI,MAAM,CACV,CAAC;EACF,OAAO;IACL9G,eAAe;IACfJ,cAAc;IACdiH;EACF,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}