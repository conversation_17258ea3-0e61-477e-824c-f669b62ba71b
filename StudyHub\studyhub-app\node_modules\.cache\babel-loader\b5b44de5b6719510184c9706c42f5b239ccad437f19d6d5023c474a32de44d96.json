{"ast": null, "code": "import * as React from 'react';\nexport default function isMuiElement(element, muiNames) {\n  var _muiName, _element$type;\n  return /*#__PURE__*/React.isValidElement(element) && muiNames.indexOf(\n  // For server components `muiName` is avaialble in element.type._payload.value.muiName\n  // relevant info - https://github.com/facebook/react/blob/2807d781a08db8e9873687fccc25c0f12b4fb3d4/packages/react/src/ReactLazy.js#L45\n  // eslint-disable-next-line no-underscore-dangle\n  (_muiName = element.type.muiName) != null ? _muiName : (_element$type = element.type) == null || (_element$type = _element$type._payload) == null || (_element$type = _element$type.value) == null ? void 0 : _element$type.muiName) !== -1;\n}", "map": {"version": 3, "names": ["React", "isMuiElement", "element", "muiNames", "_muiName", "_element$type", "isValidElement", "indexOf", "type", "mui<PERSON><PERSON>", "_payload", "value"], "sources": ["C:/Users/<USER>/OneDrive/ドキュメント/Projects/StudyHub/studyhub-app/node_modules/@mui/utils/esm/isMuiElement/isMuiElement.js"], "sourcesContent": ["import * as React from 'react';\nexport default function isMuiElement(element, muiNames) {\n  var _muiName, _element$type;\n  return /*#__PURE__*/React.isValidElement(element) && muiNames.indexOf( // For server components `muiName` is avaialble in element.type._payload.value.muiName\n  // relevant info - https://github.com/facebook/react/blob/2807d781a08db8e9873687fccc25c0f12b4fb3d4/packages/react/src/ReactLazy.js#L45\n  // eslint-disable-next-line no-underscore-dangle\n  (_muiName = element.type.muiName) != null ? _muiName : (_element$type = element.type) == null || (_element$type = _element$type._payload) == null || (_element$type = _element$type.value) == null ? void 0 : _element$type.muiName) !== -1;\n}"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,eAAe,SAASC,YAAYA,CAACC,OAAO,EAAEC,QAAQ,EAAE;EACtD,IAAIC,QAAQ,EAAEC,aAAa;EAC3B,OAAO,aAAaL,KAAK,CAACM,cAAc,CAACJ,OAAO,CAAC,IAAIC,QAAQ,CAACI,OAAO;EAAE;EACvE;EACA;EACA,CAACH,QAAQ,GAAGF,OAAO,CAACM,IAAI,CAACC,OAAO,KAAK,IAAI,GAAGL,QAAQ,GAAG,CAACC,aAAa,GAAGH,OAAO,CAACM,IAAI,KAAK,IAAI,IAAI,CAACH,aAAa,GAAGA,aAAa,CAACK,QAAQ,KAAK,IAAI,IAAI,CAACL,aAAa,GAAGA,aAAa,CAACM,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGN,aAAa,CAACI,OAAO,CAAC,KAAK,CAAC,CAAC;AAC7O", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}