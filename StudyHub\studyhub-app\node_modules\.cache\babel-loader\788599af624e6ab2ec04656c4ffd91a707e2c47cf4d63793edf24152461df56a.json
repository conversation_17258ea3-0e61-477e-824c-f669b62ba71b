{"ast": null, "code": "'use client';\n\nimport * as React from 'react';\nimport { ThemeContext } from '@mui/styled-engine';\nfunction isObjectEmpty(obj) {\n  return Object.keys(obj).length === 0;\n}\nfunction useTheme() {\n  let defaultTheme = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : null;\n  const contextTheme = React.useContext(ThemeContext);\n  return !contextTheme || isObjectEmpty(contextTheme) ? defaultTheme : contextTheme;\n}\nexport default useTheme;", "map": {"version": 3, "names": ["React", "ThemeContext", "isObjectEmpty", "obj", "Object", "keys", "length", "useTheme", "defaultTheme", "arguments", "undefined", "contextTheme", "useContext"], "sources": ["C:/Users/<USER>/OneDrive/ドキュメント/Projects/StudyHub/studyhub-app/node_modules/@mui/system/esm/useThemeWithoutDefault/useThemeWithoutDefault.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport { ThemeContext } from '@mui/styled-engine';\nfunction isObjectEmpty(obj) {\n  return Object.keys(obj).length === 0;\n}\nfunction useTheme(defaultTheme = null) {\n  const contextTheme = React.useContext(ThemeContext);\n  return !contextTheme || isObjectEmpty(contextTheme) ? defaultTheme : contextTheme;\n}\nexport default useTheme;"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,YAAY,QAAQ,oBAAoB;AACjD,SAASC,aAAaA,CAACC,GAAG,EAAE;EAC1B,OAAOC,MAAM,CAACC,IAAI,CAACF,GAAG,CAAC,CAACG,MAAM,KAAK,CAAC;AACtC;AACA,SAASC,QAAQA,CAAA,EAAsB;EAAA,IAArBC,YAAY,GAAAC,SAAA,CAAAH,MAAA,QAAAG,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAG,IAAI;EACnC,MAAME,YAAY,GAAGX,KAAK,CAACY,UAAU,CAACX,YAAY,CAAC;EACnD,OAAO,CAACU,YAAY,IAAIT,aAAa,CAACS,YAAY,CAAC,GAAGH,YAAY,GAAGG,YAAY;AACnF;AACA,eAAeJ,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}