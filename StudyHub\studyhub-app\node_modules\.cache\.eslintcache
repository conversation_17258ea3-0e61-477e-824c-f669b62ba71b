[{"C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Projects\\StudyHub\\studyhub-app\\src\\index.tsx": "1", "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Projects\\StudyHub\\studyhub-app\\src\\reportWebVitals.ts": "2", "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Projects\\StudyHub\\studyhub-app\\src\\App.tsx": "3", "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Projects\\StudyHub\\studyhub-app\\src\\pages\\Dashboard.tsx": "4", "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Projects\\StudyHub\\studyhub-app\\src\\components\\Navbar.tsx": "5", "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Projects\\StudyHub\\studyhub-app\\src\\pages\\Profile.tsx": "6", "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Projects\\StudyHub\\studyhub-app\\src\\pages\\Home.tsx": "7", "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Projects\\StudyHub\\studyhub-app\\src\\pages\\Login.tsx": "8", "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Projects\\StudyHub\\studyhub-app\\src\\pages\\StudyGroups.tsx": "9", "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Projects\\StudyHub\\studyhub-app\\src\\pages\\Resources.tsx": "10", "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Projects\\StudyHub\\studyhub-app\\src\\pages\\Register.tsx": "11", "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Projects\\StudyHub\\studyhub-app\\src\\firebase\\config.ts": "12", "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Projects\\StudyHub\\studyhub-app\\src\\contexts\\AuthContext.tsx": "13", "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Projects\\StudyHub\\studyhub-app\\src\\pages\\Chat.tsx": "14", "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Projects\\StudyHub\\studyhub-app\\src\\services\\studyGroupService.ts": "15", "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Projects\\StudyHub\\studyhub-app\\src\\services\\chatService.ts": "16", "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Projects\\StudyHub\\studyhub-app\\src\\components\\NotificationBell.tsx": "17", "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Projects\\StudyHub\\studyhub-app\\src\\components\\Chat\\GroupChat.tsx": "18", "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Projects\\StudyHub\\studyhub-app\\src\\services\\notificationService.ts": "19"}, {"size": 554, "mtime": 1751274012425, "results": "20", "hashOfConfig": "21"}, {"size": 425, "mtime": 1751274012156, "results": "22", "hashOfConfig": "21"}, {"size": 2097, "mtime": 1751276419445, "results": "23", "hashOfConfig": "21"}, {"size": 7012, "mtime": 1751278646718, "results": "24", "hashOfConfig": "21"}, {"size": 4450, "mtime": 1751276602001, "results": "25", "hashOfConfig": "21"}, {"size": 16114, "mtime": 1751278692051, "results": "26", "hashOfConfig": "21"}, {"size": 5556, "mtime": 1751278657814, "results": "27", "hashOfConfig": "21"}, {"size": 3214, "mtime": 1751275105189, "results": "28", "hashOfConfig": "21"}, {"size": 20076, "mtime": 1751278760834, "results": "29", "hashOfConfig": "21"}, {"size": 9778, "mtime": 1751278738612, "results": "30", "hashOfConfig": "21"}, {"size": 5301, "mtime": 1751278710320, "results": "31", "hashOfConfig": "21"}, {"size": 842, "mtime": 1751275390123, "results": "32", "hashOfConfig": "21"}, {"size": 6970, "mtime": 1751277142691, "results": "33", "hashOfConfig": "21"}, {"size": 14050, "mtime": 1751278611595, "results": "34", "hashOfConfig": "21"}, {"size": 9706, "mtime": 1751276739141, "results": "35", "hashOfConfig": "21"}, {"size": 11144, "mtime": 1751276636941, "results": "36", "hashOfConfig": "21"}, {"size": 9097, "mtime": 1751277610045, "results": "37", "hashOfConfig": "21"}, {"size": 12758, "mtime": 1751278266720, "results": "38", "hashOfConfig": "21"}, {"size": 9610, "mtime": 1751276539771, "results": "39", "hashOfConfig": "21"}, {"filePath": "40", "messages": "41", "suppressedMessages": "42", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1jwz4wj", {"filePath": "43", "messages": "44", "suppressedMessages": "45", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "52", "messages": "53", "suppressedMessages": "54", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "55", "messages": "56", "suppressedMessages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Projects\\StudyHub\\studyhub-app\\src\\index.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Projects\\StudyHub\\studyhub-app\\src\\reportWebVitals.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Projects\\StudyHub\\studyhub-app\\src\\App.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Projects\\StudyHub\\studyhub-app\\src\\pages\\Dashboard.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Projects\\StudyHub\\studyhub-app\\src\\components\\Navbar.tsx", ["97"], [], "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Projects\\StudyHub\\studyhub-app\\src\\pages\\Profile.tsx", ["98", "99", "100", "101"], [], "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Projects\\StudyHub\\studyhub-app\\src\\pages\\Home.tsx", ["102"], [], "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Projects\\StudyHub\\studyhub-app\\src\\pages\\Login.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Projects\\StudyHub\\studyhub-app\\src\\pages\\StudyGroups.tsx", ["103", "104", "105", "106", "107", "108"], [], "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Projects\\StudyHub\\studyhub-app\\src\\pages\\Resources.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Projects\\StudyHub\\studyhub-app\\src\\pages\\Register.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Projects\\StudyHub\\studyhub-app\\src\\firebase\\config.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Projects\\StudyHub\\studyhub-app\\src\\contexts\\AuthContext.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Projects\\StudyHub\\studyhub-app\\src\\pages\\Chat.tsx", ["109", "110", "111", "112", "113", "114", "115", "116"], [], "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Projects\\StudyHub\\studyhub-app\\src\\services\\studyGroupService.ts", ["117", "118", "119", "120", "121"], [], "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Projects\\StudyHub\\studyhub-app\\src\\services\\chatService.ts", ["122", "123", "124", "125", "126"], [], "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Projects\\StudyHub\\studyhub-app\\src\\components\\NotificationBell.tsx", ["127"], [], "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Projects\\StudyHub\\studyhub-app\\src\\components\\Chat\\GroupChat.tsx", ["128", "129"], [], "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Projects\\StudyHub\\studyhub-app\\src\\services\\notificationService.ts", [], [], {"ruleId": "130", "severity": 1, "message": "131", "line": 18, "column": 3, "nodeType": "132", "messageId": "133", "endLine": 18, "endColumn": 16}, {"ruleId": "130", "severity": 1, "message": "134", "line": 19, "column": 3, "nodeType": "132", "messageId": "133", "endLine": 19, "endColumn": 9}, {"ruleId": "130", "severity": 1, "message": "135", "line": 20, "column": 3, "nodeType": "132", "messageId": "133", "endLine": 20, "endColumn": 14}, {"ruleId": "130", "severity": 1, "message": "136", "line": 21, "column": 3, "nodeType": "132", "messageId": "133", "endLine": 21, "endColumn": 16}, {"ruleId": "130", "severity": 1, "message": "137", "line": 22, "column": 3, "nodeType": "132", "messageId": "133", "endLine": 22, "endColumn": 16}, {"ruleId": "130", "severity": 1, "message": "138", "line": 9, "column": 3, "nodeType": "132", "messageId": "133", "endLine": 9, "endColumn": 14}, {"ruleId": "130", "severity": 1, "message": "139", "line": 34, "column": 3, "nodeType": "132", "messageId": "133", "endLine": 34, "endColumn": 10}, {"ruleId": "130", "severity": 1, "message": "140", "line": 39, "column": 3, "nodeType": "132", "messageId": "133", "endLine": 39, "endColumn": 8}, {"ruleId": "130", "severity": 1, "message": "141", "line": 40, "column": 3, "nodeType": "132", "messageId": "133", "endLine": 40, "endColumn": 9}, {"ruleId": "130", "severity": 1, "message": "142", "line": 41, "column": 3, "nodeType": "132", "messageId": "133", "endLine": 41, "endColumn": 11}, {"ruleId": "130", "severity": 1, "message": "143", "line": 45, "column": 3, "nodeType": "132", "messageId": "133", "endLine": 45, "endColumn": 13}, {"ruleId": "144", "severity": 1, "message": "145", "line": 107, "column": 6, "nodeType": "146", "endLine": 107, "endColumn": 19, "suggestions": "147"}, {"ruleId": "130", "severity": 1, "message": "148", "line": 14, "column": 3, "nodeType": "132", "messageId": "133", "endLine": 14, "endColumn": 13}, {"ruleId": "130", "severity": 1, "message": "149", "line": 27, "column": 3, "nodeType": "132", "messageId": "133", "endLine": 27, "endColumn": 7}, {"ruleId": "130", "severity": 1, "message": "150", "line": 36, "column": 3, "nodeType": "132", "messageId": "133", "endLine": 36, "endColumn": 8}, {"ruleId": "130", "severity": 1, "message": "151", "line": 54, "column": 10, "nodeType": "132", "messageId": "133", "endLine": 54, "endColumn": 21}, {"ruleId": "130", "severity": 1, "message": "152", "line": 55, "column": 10, "nodeType": "132", "messageId": "133", "endLine": 55, "endColumn": 23}, {"ruleId": "130", "severity": 1, "message": "153", "line": 55, "column": 25, "nodeType": "132", "messageId": "133", "endLine": 55, "endColumn": 41}, {"ruleId": "144", "severity": 1, "message": "154", "line": 75, "column": 6, "nodeType": "146", "endLine": 75, "endColumn": 12, "suggestions": "155"}, {"ruleId": "130", "severity": 1, "message": "156", "line": 129, "column": 13, "nodeType": "132", "messageId": "133", "endLine": 129, "endColumn": 19}, {"ruleId": "130", "severity": 1, "message": "157", "line": 4, "column": 3, "nodeType": "132", "messageId": "133", "endLine": 4, "endColumn": 9}, {"ruleId": "130", "severity": 1, "message": "158", "line": 6, "column": 3, "nodeType": "132", "messageId": "133", "endLine": 6, "endColumn": 12}, {"ruleId": "130", "severity": 1, "message": "159", "line": 12, "column": 3, "nodeType": "132", "messageId": "133", "endLine": 12, "endColumn": 8}, {"ruleId": "130", "severity": 1, "message": "160", "line": 24, "column": 3, "nodeType": "132", "messageId": "133", "endLine": 24, "endColumn": 23}, {"ruleId": "130", "severity": 1, "message": "161", "line": 25, "column": 3, "nodeType": "132", "messageId": "133", "endLine": 25, "endColumn": 24}, {"ruleId": "130", "severity": 1, "message": "157", "line": 4, "column": 3, "nodeType": "132", "messageId": "133", "endLine": 4, "endColumn": 9}, {"ruleId": "130", "severity": 1, "message": "162", "line": 15, "column": 3, "nodeType": "132", "messageId": "133", "endLine": 15, "endColumn": 13}, {"ruleId": "130", "severity": 1, "message": "163", "line": 16, "column": 3, "nodeType": "132", "messageId": "133", "endLine": 16, "endColumn": 14}, {"ruleId": "130", "severity": 1, "message": "164", "line": 19, "column": 3, "nodeType": "132", "messageId": "133", "endLine": 19, "endColumn": 12}, {"ruleId": "130", "severity": 1, "message": "165", "line": 28, "column": 3, "nodeType": "132", "messageId": "133", "endLine": 28, "endColumn": 22}, {"ruleId": "130", "severity": 1, "message": "166", "line": 6, "column": 3, "nodeType": "132", "messageId": "133", "endLine": 6, "endColumn": 11}, {"ruleId": "130", "severity": 1, "message": "167", "line": 22, "column": 3, "nodeType": "132", "messageId": "133", "endLine": 22, "endColumn": 11}, {"ruleId": "130", "severity": 1, "message": "168", "line": 49, "column": 10, "nodeType": "132", "messageId": "133", "endLine": 49, "endColumn": 24}, "@typescript-eslint/no-unused-vars", "'AccountCircle' is defined but never used.", "Identifier", "unusedVar", "'Dialog' is defined but never used.", "'DialogTitle' is defined but never used.", "'DialogContent' is defined but never used.", "'DialogActions' is defined but never used.", "'CardActions' is defined but never used.", "'Tooltip' is defined but never used.", "'Group' is defined but never used.", "'Person' is defined but never used.", "'Schedule' is defined but never used.", "'FilterList' is defined but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'loadMyGroups'. Either include it or remove the dependency array.", "ArrayExpression", ["169"], "'IconButton' is defined but never used.", "'Chip' is defined but never used.", "'Close' is defined but never used.", "'studyGroups' is assigned a value but never used.", "'selectedUsers' is assigned a value but never used.", "'setSelectedUsers' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'loadStudyGroups'. Either include it or remove the dependency array.", ["170"], "'chatId' is assigned a value but never used.", "'addDoc' is defined but never used.", "'deleteDoc' is defined but never used.", "'limit' is defined but never used.", "'StudyGroupInvitation' is defined but never used.", "'StudyGroupJoinRequest' is defined but never used.", "'arrayUnion' is defined but never used.", "'arrayRemove' is defined but never used.", "'Timestamp' is defined but never used.", "'CreateGroupChatData' is defined but never used.", "'MenuItem' is defined but never used.", "'MoreVert' is defined but never used.", "'editingMessage' is assigned a value but never used.", {"desc": "171", "fix": "172"}, {"desc": "173", "fix": "174"}, "Update the dependencies array to be: [currentUser, loadMyGroups]", {"range": "175", "text": "176"}, "Update the dependencies array to be: [loadStudyGroups, user]", {"range": "177", "text": "178"}, [2383, 2396], "[currentUser, loadMyGroups]", [1912, 1918], "[loadStudyGroups, user]"]