[{"C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Projects\\StudyHub\\studyhub-app\\src\\index.tsx": "1", "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Projects\\StudyHub\\studyhub-app\\src\\reportWebVitals.ts": "2", "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Projects\\StudyHub\\studyhub-app\\src\\App.tsx": "3", "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Projects\\StudyHub\\studyhub-app\\src\\pages\\Dashboard.tsx": "4", "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Projects\\StudyHub\\studyhub-app\\src\\components\\Navbar.tsx": "5", "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Projects\\StudyHub\\studyhub-app\\src\\pages\\Profile.tsx": "6", "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Projects\\StudyHub\\studyhub-app\\src\\pages\\Home.tsx": "7", "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Projects\\StudyHub\\studyhub-app\\src\\pages\\Login.tsx": "8", "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Projects\\StudyHub\\studyhub-app\\src\\pages\\StudyGroups.tsx": "9", "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Projects\\StudyHub\\studyhub-app\\src\\pages\\Resources.tsx": "10", "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Projects\\StudyHub\\studyhub-app\\src\\pages\\Register.tsx": "11", "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Projects\\StudyHub\\studyhub-app\\src\\firebase\\config.ts": "12", "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Projects\\StudyHub\\studyhub-app\\src\\contexts\\AuthContext.tsx": "13", "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Projects\\StudyHub\\studyhub-app\\src\\pages\\Chat.tsx": "14", "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Projects\\StudyHub\\studyhub-app\\src\\services\\studyGroupService.ts": "15", "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Projects\\StudyHub\\studyhub-app\\src\\services\\chatService.ts": "16", "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Projects\\StudyHub\\studyhub-app\\src\\components\\NotificationBell.tsx": "17", "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Projects\\StudyHub\\studyhub-app\\src\\components\\Chat\\GroupChat.tsx": "18", "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Projects\\StudyHub\\studyhub-app\\src\\services\\notificationService.ts": "19", "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Projects\\StudyHub\\studyhub-app\\src\\pages\\Feed.tsx": "20", "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Projects\\StudyHub\\studyhub-app\\src\\pages\\Explore.tsx": "21", "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Projects\\StudyHub\\studyhub-app\\src\\pages\\Communities.tsx": "22", "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Projects\\StudyHub\\studyhub-app\\src\\pages\\Trending.tsx": "23"}, {"size": 554, "mtime": 1751274012425, "results": "24", "hashOfConfig": "25"}, {"size": 425, "mtime": 1751274012156, "results": "26", "hashOfConfig": "25"}, {"size": 2304, "mtime": 1751284705953, "results": "27", "hashOfConfig": "25"}, {"size": 7027, "mtime": 1751283349238, "results": "28", "hashOfConfig": "25"}, {"size": 4665, "mtime": 1751284658654, "results": "29", "hashOfConfig": "25"}, {"size": 16129, "mtime": 1751283403376, "results": "30", "hashOfConfig": "25"}, {"size": 8415, "mtime": 1751288019056, "results": "31", "hashOfConfig": "25"}, {"size": 3214, "mtime": 1751275105189, "results": "32", "hashOfConfig": "25"}, {"size": 20086, "mtime": 1751283145361, "results": "33", "hashOfConfig": "25"}, {"size": 9798, "mtime": 1751283113708, "results": "34", "hashOfConfig": "25"}, {"size": 6610, "mtime": 1751282964254, "results": "35", "hashOfConfig": "25"}, {"size": 842, "mtime": 1751275390123, "results": "36", "hashOfConfig": "25"}, {"size": 6970, "mtime": 1751277142691, "results": "37", "hashOfConfig": "25"}, {"size": 14060, "mtime": 1751283174999, "results": "38", "hashOfConfig": "25"}, {"size": 9706, "mtime": 1751276739141, "results": "39", "hashOfConfig": "25"}, {"size": 11144, "mtime": 1751276636941, "results": "40", "hashOfConfig": "25"}, {"size": 9097, "mtime": 1751277610045, "results": "41", "hashOfConfig": "25"}, {"size": 12758, "mtime": 1751278266720, "results": "42", "hashOfConfig": "25"}, {"size": 9610, "mtime": 1751276539771, "results": "43", "hashOfConfig": "25"}, {"size": 9675, "mtime": 1751286583240, "results": "44", "hashOfConfig": "25"}, {"size": 11546, "mtime": 1751286603391, "results": "45", "hashOfConfig": "25"}, {"size": 13875, "mtime": 1751286616139, "results": "46", "hashOfConfig": "25"}, {"size": 13772, "mtime": 1751286631272, "results": "47", "hashOfConfig": "25"}, {"filePath": "48", "messages": "49", "suppressedMessages": "50", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1jwz4wj", {"filePath": "51", "messages": "52", "suppressedMessages": "53", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "57", "messages": "58", "suppressedMessages": "59", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "60", "messages": "61", "suppressedMessages": "62", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "63", "messages": "64", "suppressedMessages": "65", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "66", "messages": "67", "suppressedMessages": "68", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "69", "messages": "70", "suppressedMessages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "72", "messages": "73", "suppressedMessages": "74", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "75", "messages": "76", "suppressedMessages": "77", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "78", "messages": "79", "suppressedMessages": "80", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "81", "messages": "82", "suppressedMessages": "83", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "84", "messages": "85", "suppressedMessages": "86", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "87", "messages": "88", "suppressedMessages": "89", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "90", "messages": "91", "suppressedMessages": "92", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "93", "messages": "94", "suppressedMessages": "95", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "96", "messages": "97", "suppressedMessages": "98", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "99", "messages": "100", "suppressedMessages": "101", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "102", "messages": "103", "suppressedMessages": "104", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "105", "messages": "106", "suppressedMessages": "107", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "108", "messages": "109", "suppressedMessages": "110", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "111", "messages": "112", "suppressedMessages": "113", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "114", "messages": "115", "suppressedMessages": "116", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Projects\\StudyHub\\studyhub-app\\src\\index.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Projects\\StudyHub\\studyhub-app\\src\\reportWebVitals.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Projects\\StudyHub\\studyhub-app\\src\\App.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Projects\\StudyHub\\studyhub-app\\src\\pages\\Dashboard.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Projects\\StudyHub\\studyhub-app\\src\\components\\Navbar.tsx", ["117", "118"], [], "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Projects\\StudyHub\\studyhub-app\\src\\pages\\Profile.tsx", ["119", "120", "121", "122"], [], "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Projects\\StudyHub\\studyhub-app\\src\\pages\\Home.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Projects\\StudyHub\\studyhub-app\\src\\pages\\Login.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Projects\\StudyHub\\studyhub-app\\src\\pages\\StudyGroups.tsx", ["123", "124", "125", "126", "127", "128"], [], "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Projects\\StudyHub\\studyhub-app\\src\\pages\\Resources.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Projects\\StudyHub\\studyhub-app\\src\\pages\\Register.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Projects\\StudyHub\\studyhub-app\\src\\firebase\\config.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Projects\\StudyHub\\studyhub-app\\src\\contexts\\AuthContext.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Projects\\StudyHub\\studyhub-app\\src\\pages\\Chat.tsx", ["129", "130", "131", "132", "133", "134", "135", "136"], [], "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Projects\\StudyHub\\studyhub-app\\src\\services\\studyGroupService.ts", ["137", "138", "139", "140", "141"], [], "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Projects\\StudyHub\\studyhub-app\\src\\services\\chatService.ts", ["142", "143", "144", "145", "146"], [], "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Projects\\StudyHub\\studyhub-app\\src\\components\\NotificationBell.tsx", ["147"], [], "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Projects\\StudyHub\\studyhub-app\\src\\components\\Chat\\GroupChat.tsx", ["148", "149"], [], "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Projects\\StudyHub\\studyhub-app\\src\\services\\notificationService.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Projects\\StudyHub\\studyhub-app\\src\\pages\\Feed.tsx", ["150"], [], "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Projects\\StudyHub\\studyhub-app\\src\\pages\\Explore.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Projects\\StudyHub\\studyhub-app\\src\\pages\\Communities.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Projects\\StudyHub\\studyhub-app\\src\\pages\\Trending.tsx", [], [], {"ruleId": "151", "severity": 1, "message": "152", "line": 17, "column": 3, "nodeType": "153", "messageId": "154", "endLine": 17, "endColumn": 8}, {"ruleId": "151", "severity": 1, "message": "155", "line": 18, "column": 3, "nodeType": "153", "messageId": "154", "endLine": 18, "endColumn": 16}, {"ruleId": "151", "severity": 1, "message": "156", "line": 19, "column": 3, "nodeType": "153", "messageId": "154", "endLine": 19, "endColumn": 9}, {"ruleId": "151", "severity": 1, "message": "157", "line": 20, "column": 3, "nodeType": "153", "messageId": "154", "endLine": 20, "endColumn": 14}, {"ruleId": "151", "severity": 1, "message": "158", "line": 21, "column": 3, "nodeType": "153", "messageId": "154", "endLine": 21, "endColumn": 16}, {"ruleId": "151", "severity": 1, "message": "159", "line": 22, "column": 3, "nodeType": "153", "messageId": "154", "endLine": 22, "endColumn": 16}, {"ruleId": "151", "severity": 1, "message": "160", "line": 34, "column": 3, "nodeType": "153", "messageId": "154", "endLine": 34, "endColumn": 10}, {"ruleId": "151", "severity": 1, "message": "161", "line": 39, "column": 3, "nodeType": "153", "messageId": "154", "endLine": 39, "endColumn": 8}, {"ruleId": "151", "severity": 1, "message": "162", "line": 40, "column": 3, "nodeType": "153", "messageId": "154", "endLine": 40, "endColumn": 9}, {"ruleId": "151", "severity": 1, "message": "163", "line": 41, "column": 3, "nodeType": "153", "messageId": "154", "endLine": 41, "endColumn": 11}, {"ruleId": "151", "severity": 1, "message": "164", "line": 45, "column": 3, "nodeType": "153", "messageId": "154", "endLine": 45, "endColumn": 13}, {"ruleId": "165", "severity": 1, "message": "166", "line": 107, "column": 6, "nodeType": "167", "endLine": 107, "endColumn": 19, "suggestions": "168"}, {"ruleId": "151", "severity": 1, "message": "169", "line": 14, "column": 3, "nodeType": "153", "messageId": "154", "endLine": 14, "endColumn": 13}, {"ruleId": "151", "severity": 1, "message": "170", "line": 27, "column": 3, "nodeType": "153", "messageId": "154", "endLine": 27, "endColumn": 7}, {"ruleId": "151", "severity": 1, "message": "171", "line": 36, "column": 3, "nodeType": "153", "messageId": "154", "endLine": 36, "endColumn": 8}, {"ruleId": "151", "severity": 1, "message": "172", "line": 54, "column": 10, "nodeType": "153", "messageId": "154", "endLine": 54, "endColumn": 21}, {"ruleId": "151", "severity": 1, "message": "173", "line": 55, "column": 10, "nodeType": "153", "messageId": "154", "endLine": 55, "endColumn": 23}, {"ruleId": "151", "severity": 1, "message": "174", "line": 55, "column": 25, "nodeType": "153", "messageId": "154", "endLine": 55, "endColumn": 41}, {"ruleId": "165", "severity": 1, "message": "175", "line": 75, "column": 6, "nodeType": "167", "endLine": 75, "endColumn": 12, "suggestions": "176"}, {"ruleId": "151", "severity": 1, "message": "177", "line": 129, "column": 13, "nodeType": "153", "messageId": "154", "endLine": 129, "endColumn": 19}, {"ruleId": "151", "severity": 1, "message": "178", "line": 4, "column": 3, "nodeType": "153", "messageId": "154", "endLine": 4, "endColumn": 9}, {"ruleId": "151", "severity": 1, "message": "179", "line": 6, "column": 3, "nodeType": "153", "messageId": "154", "endLine": 6, "endColumn": 12}, {"ruleId": "151", "severity": 1, "message": "180", "line": 12, "column": 3, "nodeType": "153", "messageId": "154", "endLine": 12, "endColumn": 8}, {"ruleId": "151", "severity": 1, "message": "181", "line": 24, "column": 3, "nodeType": "153", "messageId": "154", "endLine": 24, "endColumn": 23}, {"ruleId": "151", "severity": 1, "message": "182", "line": 25, "column": 3, "nodeType": "153", "messageId": "154", "endLine": 25, "endColumn": 24}, {"ruleId": "151", "severity": 1, "message": "178", "line": 4, "column": 3, "nodeType": "153", "messageId": "154", "endLine": 4, "endColumn": 9}, {"ruleId": "151", "severity": 1, "message": "183", "line": 15, "column": 3, "nodeType": "153", "messageId": "154", "endLine": 15, "endColumn": 13}, {"ruleId": "151", "severity": 1, "message": "184", "line": 16, "column": 3, "nodeType": "153", "messageId": "154", "endLine": 16, "endColumn": 14}, {"ruleId": "151", "severity": 1, "message": "185", "line": 19, "column": 3, "nodeType": "153", "messageId": "154", "endLine": 19, "endColumn": 12}, {"ruleId": "151", "severity": 1, "message": "186", "line": 28, "column": 3, "nodeType": "153", "messageId": "154", "endLine": 28, "endColumn": 22}, {"ruleId": "151", "severity": 1, "message": "187", "line": 6, "column": 3, "nodeType": "153", "messageId": "154", "endLine": 6, "endColumn": 11}, {"ruleId": "151", "severity": 1, "message": "188", "line": 22, "column": 3, "nodeType": "153", "messageId": "154", "endLine": 22, "endColumn": 11}, {"ruleId": "151", "severity": 1, "message": "189", "line": 49, "column": 10, "nodeType": "153", "messageId": "154", "endLine": 49, "endColumn": 24}, {"ruleId": "151", "severity": 1, "message": "190", "line": 48, "column": 11, "nodeType": "153", "messageId": "154", "endLine": 48, "endColumn": 22}, "@typescript-eslint/no-unused-vars", "'Forum' is defined but never used.", "Identifier", "unusedVar", "'AccountCircle' is defined but never used.", "'Dialog' is defined but never used.", "'DialogTitle' is defined but never used.", "'DialogContent' is defined but never used.", "'DialogActions' is defined but never used.", "'Tooltip' is defined but never used.", "'Group' is defined but never used.", "'Person' is defined but never used.", "'Schedule' is defined but never used.", "'FilterList' is defined but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'loadMyGroups'. Either include it or remove the dependency array.", "ArrayExpression", ["191"], "'IconButton' is defined but never used.", "'Chip' is defined but never used.", "'Close' is defined but never used.", "'studyGroups' is assigned a value but never used.", "'selectedUsers' is assigned a value but never used.", "'setSelectedUsers' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'loadStudyGroups'. Either include it or remove the dependency array.", ["192"], "'chatId' is assigned a value but never used.", "'addDoc' is defined but never used.", "'deleteDoc' is defined but never used.", "'limit' is defined but never used.", "'StudyGroupInvitation' is defined but never used.", "'StudyGroupJoinRequest' is defined but never used.", "'arrayUnion' is defined but never used.", "'arrayRemove' is defined but never used.", "'Timestamp' is defined but never used.", "'CreateGroupChatData' is defined but never used.", "'MenuItem' is defined but never used.", "'MoreVert' is defined but never used.", "'editingMessage' is assigned a value but never used.", "'PostComment' is defined but never used.", {"desc": "193", "fix": "194"}, {"desc": "195", "fix": "196"}, "Update the dependencies array to be: [currentUser, loadMyGroups]", {"range": "197", "text": "198"}, "Update the dependencies array to be: [loadStudyGroups, user]", {"range": "199", "text": "200"}, [2383, 2396], "[currentUser, loadMyGroups]", [1912, 1918], "[loadStudyGroups, user]"]