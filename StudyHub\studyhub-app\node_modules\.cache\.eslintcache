[{"C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Projects\\StudyHub\\studyhub-app\\src\\index.tsx": "1", "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Projects\\StudyHub\\studyhub-app\\src\\reportWebVitals.ts": "2", "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Projects\\StudyHub\\studyhub-app\\src\\App.tsx": "3", "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Projects\\StudyHub\\studyhub-app\\src\\pages\\Dashboard.tsx": "4", "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Projects\\StudyHub\\studyhub-app\\src\\components\\Navbar.tsx": "5", "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Projects\\StudyHub\\studyhub-app\\src\\pages\\Profile.tsx": "6", "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Projects\\StudyHub\\studyhub-app\\src\\pages\\Home.tsx": "7", "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Projects\\StudyHub\\studyhub-app\\src\\pages\\Login.tsx": "8", "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Projects\\StudyHub\\studyhub-app\\src\\pages\\StudyGroups.tsx": "9", "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Projects\\StudyHub\\studyhub-app\\src\\pages\\Resources.tsx": "10", "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Projects\\StudyHub\\studyhub-app\\src\\pages\\Register.tsx": "11", "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Projects\\StudyHub\\studyhub-app\\src\\firebase\\config.ts": "12", "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Projects\\StudyHub\\studyhub-app\\src\\contexts\\AuthContext.tsx": "13", "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Projects\\StudyHub\\studyhub-app\\src\\pages\\Chat.tsx": "14", "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Projects\\StudyHub\\studyhub-app\\src\\services\\studyGroupService.ts": "15", "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Projects\\StudyHub\\studyhub-app\\src\\services\\chatService.ts": "16", "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Projects\\StudyHub\\studyhub-app\\src\\components\\NotificationBell.tsx": "17", "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Projects\\StudyHub\\studyhub-app\\src\\components\\Chat\\GroupChat.tsx": "18", "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Projects\\StudyHub\\studyhub-app\\src\\services\\notificationService.ts": "19", "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Projects\\StudyHub\\studyhub-app\\src\\pages\\Feed.tsx": "20", "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Projects\\StudyHub\\studyhub-app\\src\\pages\\Explore.tsx": "21", "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Projects\\StudyHub\\studyhub-app\\src\\pages\\Communities.tsx": "22", "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Projects\\StudyHub\\studyhub-app\\src\\pages\\Trending.tsx": "23", "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Projects\\StudyHub\\studyhub-app\\src\\services\\postService.ts": "24"}, {"size": 554, "mtime": 1751274012425, "results": "25", "hashOfConfig": "26"}, {"size": 425, "mtime": 1751274012156, "results": "27", "hashOfConfig": "26"}, {"size": 2304, "mtime": 1751284705953, "results": "28", "hashOfConfig": "26"}, {"size": 7027, "mtime": 1751283349238, "results": "29", "hashOfConfig": "26"}, {"size": 8881, "mtime": 1751290757903, "results": "30", "hashOfConfig": "26"}, {"size": 16129, "mtime": 1751283403376, "results": "31", "hashOfConfig": "26"}, {"size": 8415, "mtime": 1751288019056, "results": "32", "hashOfConfig": "26"}, {"size": 3214, "mtime": 1751275105189, "results": "33", "hashOfConfig": "26"}, {"size": 20086, "mtime": 1751283145361, "results": "34", "hashOfConfig": "26"}, {"size": 9798, "mtime": 1751283113708, "results": "35", "hashOfConfig": "26"}, {"size": 6610, "mtime": 1751282964254, "results": "36", "hashOfConfig": "26"}, {"size": 842, "mtime": 1751275390123, "results": "37", "hashOfConfig": "26"}, {"size": 6970, "mtime": 1751277142691, "results": "38", "hashOfConfig": "26"}, {"size": 14060, "mtime": 1751283174999, "results": "39", "hashOfConfig": "26"}, {"size": 9706, "mtime": 1751276739141, "results": "40", "hashOfConfig": "26"}, {"size": 11144, "mtime": 1751276636941, "results": "41", "hashOfConfig": "26"}, {"size": 9097, "mtime": 1751277610045, "results": "42", "hashOfConfig": "26"}, {"size": 12758, "mtime": 1751278266720, "results": "43", "hashOfConfig": "26"}, {"size": 9610, "mtime": 1751276539771, "results": "44", "hashOfConfig": "26"}, {"size": 10582, "mtime": 1751290691179, "results": "45", "hashOfConfig": "26"}, {"size": 11546, "mtime": 1751286603391, "results": "46", "hashOfConfig": "26"}, {"size": 13875, "mtime": 1751286616139, "results": "47", "hashOfConfig": "26"}, {"size": 13772, "mtime": 1751286631272, "results": "48", "hashOfConfig": "26"}, {"size": 4226, "mtime": 1751290518249, "results": "49", "hashOfConfig": "26"}, {"filePath": "50", "messages": "51", "suppressedMessages": "52", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1jwz4wj", {"filePath": "53", "messages": "54", "suppressedMessages": "55", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "62", "messages": "63", "suppressedMessages": "64", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "65", "messages": "66", "suppressedMessages": "67", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "77", "messages": "78", "suppressedMessages": "79", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "80", "messages": "81", "suppressedMessages": "82", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "83", "messages": "84", "suppressedMessages": "85", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "86", "messages": "87", "suppressedMessages": "88", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "89", "messages": "90", "suppressedMessages": "91", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "92", "messages": "93", "suppressedMessages": "94", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "95", "messages": "96", "suppressedMessages": "97", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "98", "messages": "99", "suppressedMessages": "100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "101", "messages": "102", "suppressedMessages": "103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "104", "messages": "105", "suppressedMessages": "106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "107", "messages": "108", "suppressedMessages": "109", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "110", "messages": "111", "suppressedMessages": "112", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "113", "messages": "114", "suppressedMessages": "115", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "116", "messages": "117", "suppressedMessages": "118", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "119", "messages": "120", "suppressedMessages": "121", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Projects\\StudyHub\\studyhub-app\\src\\index.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Projects\\StudyHub\\studyhub-app\\src\\reportWebVitals.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Projects\\StudyHub\\studyhub-app\\src\\App.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Projects\\StudyHub\\studyhub-app\\src\\pages\\Dashboard.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Projects\\StudyHub\\studyhub-app\\src\\components\\Navbar.tsx", ["122"], [], "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Projects\\StudyHub\\studyhub-app\\src\\pages\\Profile.tsx", ["123", "124", "125", "126"], [], "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Projects\\StudyHub\\studyhub-app\\src\\pages\\Home.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Projects\\StudyHub\\studyhub-app\\src\\pages\\Login.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Projects\\StudyHub\\studyhub-app\\src\\pages\\StudyGroups.tsx", ["127", "128", "129", "130", "131", "132"], [], "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Projects\\StudyHub\\studyhub-app\\src\\pages\\Resources.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Projects\\StudyHub\\studyhub-app\\src\\pages\\Register.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Projects\\StudyHub\\studyhub-app\\src\\firebase\\config.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Projects\\StudyHub\\studyhub-app\\src\\contexts\\AuthContext.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Projects\\StudyHub\\studyhub-app\\src\\pages\\Chat.tsx", ["133", "134", "135", "136", "137", "138", "139", "140"], [], "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Projects\\StudyHub\\studyhub-app\\src\\services\\studyGroupService.ts", ["141", "142", "143", "144", "145"], [], "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Projects\\StudyHub\\studyhub-app\\src\\services\\chatService.ts", ["146", "147", "148", "149", "150"], [], "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Projects\\StudyHub\\studyhub-app\\src\\components\\NotificationBell.tsx", ["151"], [], "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Projects\\StudyHub\\studyhub-app\\src\\components\\Chat\\GroupChat.tsx", ["152", "153"], [], "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Projects\\StudyHub\\studyhub-app\\src\\services\\notificationService.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Projects\\StudyHub\\studyhub-app\\src\\pages\\Feed.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Projects\\StudyHub\\studyhub-app\\src\\pages\\Explore.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Projects\\StudyHub\\studyhub-app\\src\\pages\\Communities.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Projects\\StudyHub\\studyhub-app\\src\\pages\\Trending.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Projects\\StudyHub\\studyhub-app\\src\\services\\postService.ts", [], [], {"ruleId": "154", "severity": 1, "message": "155", "line": 25, "column": 3, "nodeType": "156", "messageId": "157", "endLine": 25, "endColumn": 8}, {"ruleId": "154", "severity": 1, "message": "158", "line": 19, "column": 3, "nodeType": "156", "messageId": "157", "endLine": 19, "endColumn": 9}, {"ruleId": "154", "severity": 1, "message": "159", "line": 20, "column": 3, "nodeType": "156", "messageId": "157", "endLine": 20, "endColumn": 14}, {"ruleId": "154", "severity": 1, "message": "160", "line": 21, "column": 3, "nodeType": "156", "messageId": "157", "endLine": 21, "endColumn": 16}, {"ruleId": "154", "severity": 1, "message": "161", "line": 22, "column": 3, "nodeType": "156", "messageId": "157", "endLine": 22, "endColumn": 16}, {"ruleId": "154", "severity": 1, "message": "162", "line": 34, "column": 3, "nodeType": "156", "messageId": "157", "endLine": 34, "endColumn": 10}, {"ruleId": "154", "severity": 1, "message": "163", "line": 39, "column": 3, "nodeType": "156", "messageId": "157", "endLine": 39, "endColumn": 8}, {"ruleId": "154", "severity": 1, "message": "164", "line": 40, "column": 3, "nodeType": "156", "messageId": "157", "endLine": 40, "endColumn": 9}, {"ruleId": "154", "severity": 1, "message": "165", "line": 41, "column": 3, "nodeType": "156", "messageId": "157", "endLine": 41, "endColumn": 11}, {"ruleId": "154", "severity": 1, "message": "166", "line": 45, "column": 3, "nodeType": "156", "messageId": "157", "endLine": 45, "endColumn": 13}, {"ruleId": "167", "severity": 1, "message": "168", "line": 107, "column": 6, "nodeType": "169", "endLine": 107, "endColumn": 19, "suggestions": "170"}, {"ruleId": "154", "severity": 1, "message": "171", "line": 14, "column": 3, "nodeType": "156", "messageId": "157", "endLine": 14, "endColumn": 13}, {"ruleId": "154", "severity": 1, "message": "172", "line": 27, "column": 3, "nodeType": "156", "messageId": "157", "endLine": 27, "endColumn": 7}, {"ruleId": "154", "severity": 1, "message": "173", "line": 36, "column": 3, "nodeType": "156", "messageId": "157", "endLine": 36, "endColumn": 8}, {"ruleId": "154", "severity": 1, "message": "174", "line": 54, "column": 10, "nodeType": "156", "messageId": "157", "endLine": 54, "endColumn": 21}, {"ruleId": "154", "severity": 1, "message": "175", "line": 55, "column": 10, "nodeType": "156", "messageId": "157", "endLine": 55, "endColumn": 23}, {"ruleId": "154", "severity": 1, "message": "176", "line": 55, "column": 25, "nodeType": "156", "messageId": "157", "endLine": 55, "endColumn": 41}, {"ruleId": "167", "severity": 1, "message": "177", "line": 75, "column": 6, "nodeType": "169", "endLine": 75, "endColumn": 12, "suggestions": "178"}, {"ruleId": "154", "severity": 1, "message": "179", "line": 129, "column": 13, "nodeType": "156", "messageId": "157", "endLine": 129, "endColumn": 19}, {"ruleId": "154", "severity": 1, "message": "180", "line": 4, "column": 3, "nodeType": "156", "messageId": "157", "endLine": 4, "endColumn": 9}, {"ruleId": "154", "severity": 1, "message": "181", "line": 6, "column": 3, "nodeType": "156", "messageId": "157", "endLine": 6, "endColumn": 12}, {"ruleId": "154", "severity": 1, "message": "182", "line": 12, "column": 3, "nodeType": "156", "messageId": "157", "endLine": 12, "endColumn": 8}, {"ruleId": "154", "severity": 1, "message": "183", "line": 24, "column": 3, "nodeType": "156", "messageId": "157", "endLine": 24, "endColumn": 23}, {"ruleId": "154", "severity": 1, "message": "184", "line": 25, "column": 3, "nodeType": "156", "messageId": "157", "endLine": 25, "endColumn": 24}, {"ruleId": "154", "severity": 1, "message": "180", "line": 4, "column": 3, "nodeType": "156", "messageId": "157", "endLine": 4, "endColumn": 9}, {"ruleId": "154", "severity": 1, "message": "185", "line": 15, "column": 3, "nodeType": "156", "messageId": "157", "endLine": 15, "endColumn": 13}, {"ruleId": "154", "severity": 1, "message": "186", "line": 16, "column": 3, "nodeType": "156", "messageId": "157", "endLine": 16, "endColumn": 14}, {"ruleId": "154", "severity": 1, "message": "187", "line": 19, "column": 3, "nodeType": "156", "messageId": "157", "endLine": 19, "endColumn": 12}, {"ruleId": "154", "severity": 1, "message": "188", "line": 28, "column": 3, "nodeType": "156", "messageId": "157", "endLine": 28, "endColumn": 22}, {"ruleId": "154", "severity": 1, "message": "189", "line": 6, "column": 3, "nodeType": "156", "messageId": "157", "endLine": 6, "endColumn": 11}, {"ruleId": "154", "severity": 1, "message": "190", "line": 22, "column": 3, "nodeType": "156", "messageId": "157", "endLine": 22, "endColumn": 11}, {"ruleId": "154", "severity": 1, "message": "191", "line": 49, "column": 10, "nodeType": "156", "messageId": "157", "endLine": 49, "endColumn": 24}, "@typescript-eslint/no-unused-vars", "'Forum' is defined but never used.", "Identifier", "unusedVar", "'Dialog' is defined but never used.", "'DialogTitle' is defined but never used.", "'DialogContent' is defined but never used.", "'DialogActions' is defined but never used.", "'Tooltip' is defined but never used.", "'Group' is defined but never used.", "'Person' is defined but never used.", "'Schedule' is defined but never used.", "'FilterList' is defined but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'loadMyGroups'. Either include it or remove the dependency array.", "ArrayExpression", ["192"], "'IconButton' is defined but never used.", "'Chip' is defined but never used.", "'Close' is defined but never used.", "'studyGroups' is assigned a value but never used.", "'selectedUsers' is assigned a value but never used.", "'setSelectedUsers' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'loadStudyGroups'. Either include it or remove the dependency array.", ["193"], "'chatId' is assigned a value but never used.", "'addDoc' is defined but never used.", "'deleteDoc' is defined but never used.", "'limit' is defined but never used.", "'StudyGroupInvitation' is defined but never used.", "'StudyGroupJoinRequest' is defined but never used.", "'arrayUnion' is defined but never used.", "'arrayRemove' is defined but never used.", "'Timestamp' is defined but never used.", "'CreateGroupChatData' is defined but never used.", "'MenuItem' is defined but never used.", "'MoreVert' is defined but never used.", "'editingMessage' is assigned a value but never used.", {"desc": "194", "fix": "195"}, {"desc": "196", "fix": "197"}, "Update the dependencies array to be: [currentUser, loadMyGroups]", {"range": "198", "text": "199"}, "Update the dependencies array to be: [loadStudyGroups, user]", {"range": "200", "text": "201"}, [2383, 2396], "[currentUser, loadMyGroups]", [1912, 1918], "[loadStudyGroups, user]"]