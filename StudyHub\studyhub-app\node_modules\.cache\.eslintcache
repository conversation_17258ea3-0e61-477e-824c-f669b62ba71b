[{"C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Projects\\StudyHub\\studyhub-app\\src\\index.tsx": "1", "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Projects\\StudyHub\\studyhub-app\\src\\reportWebVitals.ts": "2", "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Projects\\StudyHub\\studyhub-app\\src\\App.tsx": "3", "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Projects\\StudyHub\\studyhub-app\\src\\pages\\Dashboard.tsx": "4", "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Projects\\StudyHub\\studyhub-app\\src\\components\\Navbar.tsx": "5", "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Projects\\StudyHub\\studyhub-app\\src\\pages\\Profile.tsx": "6", "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Projects\\StudyHub\\studyhub-app\\src\\pages\\Home.tsx": "7", "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Projects\\StudyHub\\studyhub-app\\src\\pages\\Login.tsx": "8", "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Projects\\StudyHub\\studyhub-app\\src\\pages\\StudyGroups.tsx": "9", "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Projects\\StudyHub\\studyhub-app\\src\\pages\\Resources.tsx": "10", "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Projects\\StudyHub\\studyhub-app\\src\\pages\\Register.tsx": "11", "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Projects\\StudyHub\\studyhub-app\\src\\firebase\\config.ts": "12"}, {"size": 554, "mtime": 1751274012425, "results": "13", "hashOfConfig": "14"}, {"size": 425, "mtime": 1751274012156, "results": "15", "hashOfConfig": "14"}, {"size": 1876, "mtime": 1751274290690, "results": "16", "hashOfConfig": "14"}, {"size": 6997, "mtime": 1751274398889, "results": "17", "hashOfConfig": "14"}, {"size": 3916, "mtime": 1751274311724, "results": "18", "hashOfConfig": "14"}, {"size": 10765, "mtime": 1751274503105, "results": "19", "hashOfConfig": "14"}, {"size": 5531, "mtime": 1751274335724, "results": "20", "hashOfConfig": "14"}, {"size": 3262, "mtime": 1751274350643, "results": "21", "hashOfConfig": "14"}, {"size": 8740, "mtime": 1751274432173, "results": "22", "hashOfConfig": "14"}, {"size": 9798, "mtime": 1751274467710, "results": "23", "hashOfConfig": "14"}, {"size": 5952, "mtime": 1751274370850, "results": "24", "hashOfConfig": "14"}, {"size": 735, "mtime": 1751274275994, "results": "25", "hashOfConfig": "14"}, {"filePath": "26", "messages": "27", "suppressedMessages": "28", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1jwz4wj", {"filePath": "29", "messages": "30", "suppressedMessages": "31", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "32", "messages": "33", "suppressedMessages": "34", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "35", "messages": "36", "suppressedMessages": "37", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "38", "messages": "39", "suppressedMessages": "40", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "41", "messages": "42", "suppressedMessages": "43", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "44", "messages": "45", "suppressedMessages": "46", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "47", "messages": "48", "suppressedMessages": "49", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "50", "messages": "51", "suppressedMessages": "52", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "53", "messages": "54", "suppressedMessages": "55", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Projects\\StudyHub\\studyhub-app\\src\\index.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Projects\\StudyHub\\studyhub-app\\src\\reportWebVitals.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Projects\\StudyHub\\studyhub-app\\src\\App.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Projects\\StudyHub\\studyhub-app\\src\\pages\\Dashboard.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Projects\\StudyHub\\studyhub-app\\src\\components\\Navbar.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Projects\\StudyHub\\studyhub-app\\src\\pages\\Profile.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Projects\\StudyHub\\studyhub-app\\src\\pages\\Home.tsx", ["62"], [], "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Projects\\StudyHub\\studyhub-app\\src\\pages\\Login.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Projects\\StudyHub\\studyhub-app\\src\\pages\\StudyGroups.tsx", ["63"], [], "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Projects\\StudyHub\\studyhub-app\\src\\pages\\Resources.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Projects\\StudyHub\\studyhub-app\\src\\pages\\Register.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\ドキュメント\\Projects\\StudyHub\\studyhub-app\\src\\firebase\\config.ts", [], [], {"ruleId": "64", "severity": 1, "message": "65", "line": 10, "column": 3, "nodeType": "66", "messageId": "67", "endLine": 10, "endColumn": 14}, {"ruleId": "64", "severity": 1, "message": "68", "line": 25, "column": 22, "nodeType": "66", "messageId": "67", "endLine": 25, "endColumn": 28}, "@typescript-eslint/no-unused-vars", "'CardActions' is defined but never used.", "Identifier", "unusedVar", "'Person' is defined but never used."]